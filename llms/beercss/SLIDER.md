# Slider

Sliders let users make selections from a range of values. Default range is 0-100.

## Element

```html
<label class="slider">
  <input type="range">
  <span></span>
</label>
```

## Example

```html
<label class="slider">
  <input type="range">
  <span></span>
</label>

<label class="slider">
  <input type="range" min="4" max="8">
  <span></span>
</label>
```

## Value indicator example

```html
<label class="slider">
  <input type="range">
  <span></span>
  <div class="tooltip"></div>
</label>
```

## Inset icon example

The icon will show only with `medium`, `large` or `extra` helpers.

```html
<label class="slider medium">
  <input type="range">
  <span>
    <i>sunny</i>
  </span>
</label>
```

## In field elements example

```html
<div class="field middle-align">
  <label class="slider">
    <input type="range">
    <span></span>
  </label>
</div>
```

## Custom slider example

```html
<article>
  <label class="slider max">
    <input type="range">
    <span></span>
  </label>
<article>
```

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)