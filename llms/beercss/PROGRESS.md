# Progress

Progress display the length of a process or an unspecified wait time.

## Element

```html
<progress>...</progress>
```

## Most used helpers

**Directions**

vertical

**Forms**

circle

**Sizes**

small, medium, large, min

## Linear example (default)

```html
<progress></progress>
<progress value="0.25"></progress>
<progress value="25" max="100"></progress>
```

## Circular example

```html
<progress class="circle"></progress>
```

## Custom examples

```html
<article>
  <progress class="max" value="25" max="100"></progress>
  <h5>Title</h5>
</article>

<article>
  <h5>Title</h5>
  <progress class="max" value="25" max="100"></progress>
</article>
```

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)