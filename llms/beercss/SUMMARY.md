# Summary

Use this page to learn. This page has the most used combinations of elements and helpers. This project has about 100 css classes.

## Elements

**&lt;a&gt;** link, inverse-link, underline

**absolute** left, right, top, bottom, front, back, small, medium, large

**&lt;aside&gt;** left, right

**&lt;article&gt;** small, medium, large, border, round, no-round, left-round, top-round, right-round, bottom-round, padding, no-padding, tiny-padding small-padding, medium-padding, large-padding

**&lt;b&gt;**

**badge** min, left, right, top, bottom, none, border, circle, square, round, no-round, left-round, right-round, top-round, bottom-round

**&lt;blockquote&gt;** border, no-border, scroll

**button or &lt;button&gt;** small, medium, large, extra, extend, border, circle, square, round, no-round, left-round, right-round, top-round, bottom-round, responsive, horizontal, vertical

**chip** small, medium, large, border, no-border, circle, square, round, no-round, left-round, right-round, top-round, bottom-round, horizontal, vertical

**&lt;code&gt;**

**&lt;details&gt;**

**&lt;dialog&gt;** left, right, top, bottom, small, medium, large, modal, border, round, no-round, left-round, right-round, top-round, bottom-round, active

**&lt;em&gt;**

**field** small, medium, large, extra, label, border, round, fill, prefix, suffix, textarea

**&lt;fieldset&gt;**

**fixed** left, right, top, bottom, front, back, small, medium, large

**&lt;footer&gt;** fixed, responsive, max

**grid** space, no-space, small-space, medium-space, large-space

**&lt;h1&gt;...&lt;h6&gt;** small, medium, large

**&lt;header&gt;** fixed, responsive, max

**&lt;hr&gt;** small, medium, large

**&lt;i&gt;** tiny, small, medium, large, extra, fill

**&lt;img&gt;** tiny, small, medium, large, extra, circle, round, no-round, left-round, right-round, top-round, bottom-round, responsive

**&lt;label&gt;** active, radio, checkbox, switch, slider, tiny, small, medium, large, extra

**&lt;legend&gt;**

**&lt;li&gt;**

**list** border, space, no-space, small-space, medium-space, large-space

**&lt;main&gt;** responsive, max

**&lt;menu&gt;** border, left, right, top, bottom, wrap, no-wrap, active, min, max, space, no-space, small-space, medium-space, large-space

**&lt;nav&gt;** left, right, top, bottom, drawer, group, toolbar, min, max, left-align, right-align, center-align, top-align, bottom-align, middle-align, border, round, no-round, left-round, right-round, top-round, bottom-round, space, no-space, small-space, medium-space, large-space, wrap, no-wrap, margin, no-margin, tiny-margin, small-margin, medium-margin, large-margin

**&lt;ol&gt;**

**overlay** left-align, right-align, center-align, top-align, bottom-align, middle-align, active, blur, small-blur, medium-blur, large-blur

**page** left, right, top, bottom, active

**&lt;pre&gt;** border, no-border, scroll

**&lt;progress&gt;** small, medium, large, circle, max, vertical

**row** left-align, right-align, center-align, top-align, bottom-align, middle-align, space, no-space, small-space, medium-space, large-space, horizontal, vertical

**snackbar** top, bottom, active

**&lt;strong&gt;**

**&lt;summary&gt;** none

**&lt;table&gt;** border, stripes, left-align, right-align, center-align, space, no-space, small-space, medium-space, large-space

**tabs** left-align, right-align, center-align, horizontal, vertical, min, max

**&lt;td&gt;** min

**&lt;th&gt;** min

**&lt;tfoot&gt;** fixed

**&lt;thead&gt;** fixed

**tooltip** left, right, top, bottom, max

**&lt;ul&gt;**

**&lt;video&gt;** tiny, small, medium, large, extra, circle, round, no-round, left-round, right-round, top-round, bottom-round, responsive

## Helpers

**Alignments** left-align, right-align, center-align, top-align, bottom-align, middle-align

**Blurs** blur, small-blur, medium-blur, large-blur, light, dark

**Colors** amber1, amber2, amber3, amber4, amber5, amber6, amber7, amber8, amber9, amber10, amber, amber-border, amber-text, blue1, blue2, blue3, blue4, blue5, blue6, blue7, blue8, blue9, blue10, blue, blue-border, blue-text, blue-grey1, blue-grey2, blue-grey3, blue-grey4, blue-grey5, blue-grey6, blue-grey7, blue-grey8, blue-grey9, blue-grey10, blue-grey, blue-grey-border, blue-grey-text, brown1, brown2, brown3, brown4, brown5, brown6, brown7, brown8, brown9, brown10, brown, brown-border, brown-text, cyan1, cyan2, cyan3, cyan4, cyan5, cyan6, cyan7, cyan8, cyan9, cyan10, cyan, cyan-border, cyan-text, deep-orange1, deep-orange2, deep-orange3, deep-orange4, deep-orange5, deep-orange6, deep-orange7, deep-orange8, deep-orange9, deep-orange10, deep-orange, deep-orange-border, deep-orange-text, deep-purple1, deep-purple2, deep-purple3, deep-purple4, deep-purple5, deep-purple6, deep-purple7, deep-purple8, deep-purple9, deep-purple10, deep-purple, deep-purple-border, deep-purple-text, green1, green2, green3, green4, green5, green6n, green7, green8, green9, green10, green, green-border, green-text, grey1, grey2, grey3, grey4, grey5, grey6, grey7, grey8, grey9, grey10, grey, grey-border, grey-text, indigo1, indigo2, indigo3, indigo4, indigo5, indigo6, indigo7, indigo8, indigo9, indigo10, indigo, indigo-border, indigo-text, light-blue1, light-blue2, light-blue3, light-blue4, light-blue5, light-blue6, light-blue7, light-blue8, light-blue9, light-blue10, light-blue, light-blue-border, light-blue-text, light-green1, light-green2, light-green3, light-green4, light-green5, light-green6, light-green7, light-green8, light-green9, light-green10, light-green, light-green-border, light-green-text, lime1, lime2, lime3, lime4, lime5, lime6, lime7, lime8, lime9, lime10, lime, lime-border, lime-text, orange1, orange2, orange3, orange4, orange5, orange6, orange7, orange8, orange9, orange10, orange, orange-border, orange-text, pink1, pink2, pink3, pink4, pink5, pink6, pink7, pink8, pink9, pink10, pink, pink-border, pink-text, purple1, purple2, purple3, purple4, purple5, purple6, purple7, purple8, purple9, purple10, purple, purple-border, purple-text, red1, red2, red3, red4, red5, red6, red7, red8, red9, red10, red, red-border, red-text, teal1, teal2, teal3, teal4, teal5, teal6, teal7, teal8, teal9, teal10, teal, teal-border, teal-text, yellow1, yellow2, yellow3, yellow4, yellow5, yellow6, yellow7, yellow8, yellow9, yellow10, yellow, yellow-border, yellow-text

**Directions** horizontal, vertical

**Elevates** elevate, no-elevate, small-elevate, medium-elevate, large-elevate

**Forms** border, no-border, circle, square, none, fill, extend, drawer, tabbed, round, no-round, small-round, medium-round, large-round, left-round, right-round, top-round, bottom-round

**Margins** margin, no-margin, auto-margin, tiny-margin, small-margin, medium-margin, large-margin, left-margin, right-margin, top-margin, bottom-margin, horizontal-margin, vertical-margin

**Opacities** opacity, no-opacity, small-opacity, medium-opacity, large-opacity

**Paddings** padding, no-padding, tiny-padding small-padding, medium-padding, large-padding, left-padding, right-padding, top-padding, bottom-padding, horizontal-padding, vertical-padding

**Positions** left, right, center, top, bottom, middle, front, back

**Responsive** responsive, small-device, medium-device, large-device, s, m, l

**Ripples** ripple, slow-ripple, fast-ripple

**Scrolls** scroll, no-scroll

**Shadows** shadow, left-shadow, right-shadow, top-shadow, bottom-shadow

**Sizes** tiny, small, medium, large, extra, wrap, no-wrap, max, auto-width, small-width, medium-width, large-width, auto-height, small-height, medium-height, large-height

**Spaces** space, no-space, small-space, medium-space, large-space

**Theme** light, dark, primary, primary-text, primary-border, primary-container, secondary, secondary-text, secondary-border, secondary-container, tertiary, tertiary-text, tertiary-border, tertiary-container, error, error-text, error-border, error-container, background, surface, surface-variant, inverse-surface, inverse-primary, inverse-primary-text, inverse-primary-border, black, black-text, black-border, white, white-text, white-border, transparent, transparent-text, transparent-border

**Triggers** active

**Typography** italic, bold, underline, overline, upper, lower, capitalize, link, small-text, medium-text, large-text 

**Waves** wave, no-wave

## Settings

**Variables** --primary, --on-primary, --primary-container, --on-primary-container, --secondary, --on-secondary, --secondary-container, --on-secondary-container, --tertiary, --on-tertiary, --tertiary-container, --on-tertiary-container, --error, --on-error, --error-container, --on-error-container, --background, --on-background, --surface, --on-surface, --surface-variant, --on-surface-variant, --outline, --outline-variant, --shadow, --scrim, --inverse-surface, --inverse-on-surface, --inverse-primary, --surface-dim, --surface-bright, --surface-container-lowest, --surface-container-low, --surface-container, --surface-container-high, --surface-container-highest, --outline, --active, --overlay, --font, --size, --elevate1, --elevate2, --elevate3, --speed1, --speed2, --speed3, --speed4

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)
