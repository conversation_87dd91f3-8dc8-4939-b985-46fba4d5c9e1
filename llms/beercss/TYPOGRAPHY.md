# Typography

Use typography to present your design and content as clearly and efficiently as possible.

## Element

```html
<h1>...</h1>
<h2>...</h2>
<h3>...</h3>
<h4>...</h4>
<h5>...</h5>
<h6>...</h6>
<b>...</b>
<p>...</p>
<span>...</span>
<div>...</div>
<...>...</...>
```

## Most used helpers

**texts**

italic, bold, underline, overline, upper, lower, capitalize, link, small-text, medium-text, large-text

**Sizes**

small, medium, large

## Display and Headline example

```html
<h1>Display</h1>
<h2>Display</h2>
<h3>Display</h3>
<h4>Headline</h4>
<h5>Headline</h5>
<h6>Headline</h6>
```

## Formatting example
```html
<a class="link">link</a>
<a class="inverse-link">inverse-link</a>
<p class="italic">italic</p>
<p class="bold">bold</p>
<p class="underline">underline</p>
<p class="overline">overline</p>
<p class="upper">upper</p>
<p class="lower">lower</p>
<p class="capitalize">capitalize</p>
<p class="small-text">small-text</p>
<p class="medium-text">medium-text</p>
<p class="large-text">large-text</p>
```

## Line spacing example

```html
<div class="no-line">...</div>
<div class="tiny-line">...</div>
<div class="small-line">...</div>
<div class="medium-line">...</div>
<div class="large-line">...</div>
<div class="extra-line">...</div>
```

## Blockquote, pre and code example

```html
<blockquote>...</blockquote>
<pre>...</pre>
<pre>
  <code>...</code>
</pre>
<p>
  ... <code>...</code> ...
</p>
```

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)