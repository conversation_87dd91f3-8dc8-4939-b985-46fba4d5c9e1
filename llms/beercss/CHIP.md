# Chip

Chips are compact elements that represent an input, attribute, or action.

## Element

```html
<button class="chip">...</button>
<a class="chip">...</a>
```

## Most used helpers

**Colors**

fill, primary, secondary, tertiary

**Directions**

horizontal, vertical

**Forms**

border, circle, square, round, no-round, left-round, right-round, top-round, bottom-round

**Sizes**

small, medium, large


## Example

```html
<button class="chip">Chip</button>

<button class="chip">
  <i>home</i>
  <span>Chip</span>
</button>
```

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [But<PERSON>](BUTTON.md), [<PERSON>](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)