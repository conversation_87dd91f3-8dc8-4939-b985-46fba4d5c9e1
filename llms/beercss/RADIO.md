# Radio

Radio buttons allow users to select one option from a set.

## Element

```html
<label class="radio">
  <input type="radio">
  <span></span>
</label>
```

## Most used helpers

**Sizes**

small, medium, large, extra

## Example

```html
<label class="radio">
  <input type="radio">
  <span></span>
</label>

<label class="radio">
  <input type="radio">
  <span>Click here</span>
</label>

<label class="radio icon">
  <input type="radio">
  <span>
    <i>close</i>
    <i>done</i>
  </span>
</label>

<nav>
  <label class="radio">
    <input type="radio">
    <span>Item 1</span>
  </label>
  <label class="radio">
    <input type="radio">
    <span>Item 2</span>
  </label>
  <label class="radio">
    <input type="radio">
    <span>Item 3</span>
  </label>
</nav>
```

## In field elements example

```html
<div class="field middle-align">
  <nav>
    <label class="radio">
      <input type="radio">
      <span>Item 1</span>
    </label>
    <label class="radio">
      <input type="radio">
      <span>Item 2</span>
    </label>
    <label class="radio">
      <input type="radio">
      <span>Item 3</span>
    </label>
  </nav>
</div>
```

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)