# Media

Media can be a image or video element.

## Element

```html
<img>

<video>...</video>
```

## Most used helpers

**Forms**

circle, round, no-round, left-round, right-round, top-round, bottom-round, responsive

**Sizes**

tiny, small, medium, large, extra

## Example

```html
<img src="/image.png" class="circle extra">

<video class="circle extra">
  <source src="/video.mp4" type="video/mp4">
</video>

<svg class="circle extra" viewBox="0 0 24 24">
  <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"></path>
</svg>
```

## Responsive media example

The responsive media is a image/video that automatically adjust with the width/height of your container.

```html
<img src="/image.png" class="responsive">

<video class="responsive">
  <source src="/video.mp4" type="video/mp4">
</video>

<svg class="responsive" viewBox="0 0 24 24">
  <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"></path>
</svg>
```

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)