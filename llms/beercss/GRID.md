# Grid

Grids are rows and cols system grid. They are most used to organize content.

## Element

```html
<div class="grid">
  <div>...</div>
</div>
```

## Most used helpers

**Sizes**

s1...s12, m1...m12, l1...l12

**Spaces**

no-space, space, small-space, medium-space, large-space

## Example

This will render one or more lines, depends the user screen.

```html
<div class="grid">
  <div class="s12 m6 l3">
    <h5>First</h5>
  </div>
  <div class="s12 m6 l3">
    <h5>Second</h5>
  </div>
  <div class="s12 m6 l3">
    <h5>Third</h5>
  </div>
</div>
```

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)