# Switch

Switches toggle the state of a single item on or off.

## Element

```html
<label class="switch">
  <input type="checkbox">
  <span></span>
</label>
```

## Example

```html
<label class="switch">
  <input type="checkbox">
  <span></span>
</label>

<nav>
  <div class="max">
    <h6>Title</h6>
    <div>Complementary text</div>
  </div>
  <label class="switch">
    <input type="checkbox">
    <span></span>
  </label>
</nav>
```

## With icons

```html
<label class="switch icon">
  <input type="checkbox">
  <span>
    <i>wifi</i>
  </span>
</label>

<label class="switch icon">
  <input type="checkbox">
  <span>
    <i>close</i>
    <i>done</i>
  </span>
</label>
```

## In field elements example

```html
<div class="field middle-align">
  <nav>
    <div class="max">
      <h6>Title</h6>
      <div>Complementary text</div>
    </div>
    <label class="switch">
      <input type="checkbox">
      <span></span>
    </label>
  </nav>
</div>
```


## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)