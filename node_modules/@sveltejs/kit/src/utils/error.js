import { HttpError, SvelteKitError } from '../runtime/control.js';

/**
 * @param {unknown} err
 * @return {Error}
 */
export function coalesce_to_error(err) {
	return err instanceof Error ||
		(err && /** @type {any} */ (err).name && /** @type {any} */ (err).message)
		? /** @type {Error} */ (err)
		: new Error(JSON.stringify(err));
}

/**
 * This is an identity function that exists to make TypeScript less
 * paranoid about people throwing things that aren't errors, which
 * frankly is not something we should care about
 * @param {unknown} error
 */
export function normalize_error(error) {
	return /** @type {import('../runtime/control.js').Redirect | HttpError | SvelteKitError | Error} */ (
		error
	);
}

/**
 * @param {unknown} error
 */
export function get_status(error) {
	return error instanceof HttpError || error instanceof SvelteKitError ? error.status : 500;
}

/**
 * @param {unknown} error
 */
export function get_message(error) {
	return error instanceof SvelteKitError ? error.text : 'Internal Error';
}
