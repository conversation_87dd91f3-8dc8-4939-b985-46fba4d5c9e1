export interface IMaterialDynamicColorsThemeColor {
  primary: string,
  onPrimary: string,
  primaryContainer: string,
  onPrimaryContainer: string,
  secondary: string,
  onSecondary: string,
  secondaryContainer: string,
  onSecondaryContainer: string,
  tertiary: string,
  onTertiary: string,
  tertiaryContainer: string,
  onTertiaryContainer: string,
  error: string,
  onError: string,
  errorContainer: string,
  onErrorContainer: string,
  background: string,
  onBackground: string,
  surface: string,
  onSurface: string,
  surfaceVariant: string,
  onSurfaceVariant: string,
  outline: string,
  outlineVariant: string,
  shadow: string,
  scrim: string,
  inverseSurface: string,
  inverseOnSurface: string,
  inversePrimary: string,
  surfaceDim: string,
  surfaceBright: string,
  surfaceContainerLowest: string,
  surfaceContainerLow: string,
  surfaceContainer: string,
  surfaceContainerHigh: string,
  surfaceContainerHighest: string
}

export interface IMaterialDynamicColorsTheme {
  light: IMaterialDynamicColorsThemeColor,
  dark: IMaterialDynamicColorsThemeColor
}