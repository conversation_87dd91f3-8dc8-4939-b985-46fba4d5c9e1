/* outlined icons */
@font-face {
  font-family: "Material Symbols Outlined";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("../material-symbols-outlined.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-outlined.woff2") format("woff2");
}

/* rounded icons */
@font-face {
  font-family: "Material Symbols Rounded";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("../material-symbols-rounded.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-rounded.woff2") format("woff2");
}

/* sharp icons */
@font-face {
  font-family: "Material Symbols Sharp";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("../material-symbols-sharp.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-sharp.woff2") format("woff2");
}

/* subset of only required icons */
@font-face {
  font-family: "Material Symbols Subset";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("../material-symbols-subset.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-subset.woff2") format("woff2");
}
