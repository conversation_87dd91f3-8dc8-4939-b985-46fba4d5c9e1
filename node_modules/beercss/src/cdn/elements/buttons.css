.button,
button {
  --_padding: 1rem;
  --_size: 2.5rem;
  box-sizing: content-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  block-size: var(--_size);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--on-primary);
  padding: 0 var(--_padding);
  background-color: var(--primary);
  margin: 0 0.5rem;
  border-radius: var(--_size);
  transition: transform var(--speed3), border-radius var(--speed2), padding var(--speed3);
  user-select: none;
  gap: 0.5rem;
  line-height: normal;
}

:is(button, .button).small {
  --_size: 2rem;
  --_padding: 0.75rem;
}

:is(button, .button).large {
  --_size: 3rem;
  --_padding: 1.25rem;
}

:is(.button, button):is(.extra, .extend) {
  --_size: 3.5rem;
  font-size: 1rem;
  --_padding: 1.5rem;
}

:is(button, .button):is(.square, .circle) {
  --_padding: 0;
}

:is(button, .button).border {
  border-color: var(--outline-variant);
  color: var(--primary);
}

.extend > span {
  display: none;
}

.extend:is(:hover, .active) {
  inline-size: auto;
  --_padding: 1.5rem;
  padding: 0 var(--_padding);
}

.extend:is(:hover, .active) > i + span {
  display: inherit;
  margin-inline-start: var(--_padding);
}

.extend:is(:hover, .active) > :is(img, svg) + span {
  display: inherit;
  margin-inline-start: calc(1rem + var(--_padding));
}

:is(.button, button)[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.button[disabled] {
  pointer-events: none;
}

:is(.button, button)[disabled]::before,
:is(.button, button)[disabled]::after {
  display: none;
}

:is(.button, button):not(.chip, .extend).fill {
  background-color: var(--secondary-container) !important;
  color: var(--on-secondary-container) !important;
}

:is(.button, button):not(.chip, .extend).active {
  background-color: var(--primary-container);
  color: var(--on-primary-container);
}

:is(.button, button):not(.chip, .extend).fill.active {
  background-color: var(--secondary) !important;
  color: var(--on-secondary) !important;
}

:is(.button, button):not(.chip, .extend).border.active {
  background-color: var(--inverse-surface) !important;
  color: var(--inverse-on-surface) !important;
  border-color: var(--inverse-surface) !important;
}

:is(.button, button):not(.chip):active,
:is(.button, button):not(.chip).active {
  border-radius: 0.5rem !important;
}
