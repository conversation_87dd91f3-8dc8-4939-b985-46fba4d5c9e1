menu {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  box-shadow: var(--elevate2);
  background-color: var(--surface-container);
  z-index: 11;
  inset: auto auto 0 0;
  inline-size: 100%;
  max-block-size: 50vh;
  max-inline-size: none !important;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 0.875rem;
  font-weight: normal;
  text-transform: none;
  color: var(--on-surface);
  line-height: normal;
  text-align: start;
  border-radius: 0.25rem;
  transform: scale(0.8) translateY(120%);
  transition: all var(--speed2), 0s background-color;
  justify-content: flex-start;
}

[dir=rtl] menu {
  inset: auto 0 0 auto;
}

menu.no-wrap {
  inline-size: max-content;
  white-space: nowrap !important;
}

menu.active,
:not(menu, [data-ui]):focus-within > menu,
menu > li:hover > menu,
menu > li > menu:hover {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(100%);
}

menu.active.top,
:not(menu, [data-ui]):focus-within > menu.top,
menu > li:hover > menu.top,
menu > li > menu.top:hover {
  transform: scale(1) translateY(-100%);
}

menu * {
  white-space: inherit !important;
}

menu > li,
menu > li > a:only-child {
  all: unset;
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: center;
  align-self: normal;
  text-align: start;
  justify-content: inherit;
  white-space: nowrap;
  gap: 1rem;
  padding: 0.5rem 1rem;
  min-block-size: 3rem;
  flex: 1;
  margin: 0 !important;
  cursor: pointer;
}

menu > li:is(:hover, :focus, .active) {
  background-color: var(--active);
}

menu > li > :is(.max, .field),
menu > li > a:only-child > .max,
menu > li:has(.field, a:only-child) {
  flex: 1;
  padding: 0;
  margin: 0;
}

menu.min {
  inset: 0 0 auto 0;
  transform: none !important;
  background-color: var(--surface-variant) !important;
  color: var(--on-surface-variant) !important;
}

[dir=rtl] menu.min.right,
menu.min.left,
menu.top.left {
  inset: 0 0 auto auto;
}

[dir=rtl] menu.min.left,
menu.min.right,
menu.top,
menu.top.right {
  inset: 0 auto auto 0;
}

menu.max {
  position: fixed;
  inset: 0;
  block-size: 100%;
  max-block-size: none;
  min-block-size: auto;
  z-index: 100;
  transform: none !important;
  background-color: var(--surface-variant) !important;
  color: var(--on-surface-variant) !important;
  border-radius: 0;
}

menu.no-wrap:is(.min, .max) {
  min-inline-size: 16rem;
}

[dir=rtl] menu.right,
[dir=rtl] menu.top.min.right,
menu.left,
menu.top.min.left {
  inset: auto 0 0 auto;
}

[dir=rtl] menu.left,
[dir=rtl] menu.top.min.left,
menu.right,
menu.top.min {
  inset: auto auto 0 0;
}

menu.top {
  transform: scale(0.8) translateY(-120%);
}

menu:has(menu) {
  --_child: 1;
  --_type: 0;
  overflow: unset;
  white-space: nowrap;
  inline-size: auto;
  min-inline-size: 12rem;
  max-block-size: none;
}

menu > li > :is(menu, menu.right),
[dir=rtl] menu > li > menu.left {
  inset: auto auto calc(3rem * (var(--_child) - var(--_type))) 100%;
}

[dir=rtl] menu > li > :is(menu, menu.right),
menu > li > menu.left {
  inset: auto 100% calc(3rem * (var(--_child) - var(--_type))) auto;
}

menu > li > :is(menu.top, menu.top.right),
[dir=rtl] menu > li > menu.top.left {
  inset: calc(3rem * (var(--_child) - var(--_type))) auto auto 100%;
}

[dir=rtl] menu > li > :is(menu.top, menu.top.right),
menu > li > menu.top.left {
  inset: calc(3rem * (var(--_child) - var(--_type))) 100% auto auto;
}

menu.no-space > li {
  min-block-size: 2.5rem;
}

menu.medium-space > li {
  min-block-size: 3.5rem;
}

menu.large-space > li {
  min-block-size: 4rem;
}

menu.border > li:not(:last-child)::before {
  content: '';
  position: absolute;
  background-color: var(--outline-variant);
  inset: auto 0 0 0;
  block-size: 0.0625rem;
  inline-size: auto;
}

menu.transparent {
  margin: 0 -1rem !important;
  padding: 0.5rem;
}

menu.transparent > li {
  background-color: inherit;
  box-shadow: none;
  padding: 0;
}

menu > li:nth-last-child(2) {
  --_child: 2;
}

menu > li:nth-last-child(3) {
  --_child: 3;
}

menu > li:nth-last-child(4) {
  --_child: 4;
}

menu > li:nth-last-child(5) {
  --_child: 5;
}

menu > li:nth-last-child(6) {
  --_child: 6;
}

menu > li:nth-last-child(7) {
  --_child: 7;
}

menu > li:nth-last-child(8) {
  --_child: 8;
}

menu > li:nth-last-child(9) {
  --_child: 9;
}

menu > li:nth-last-child(10) {
  --_child: 10;
}

menu > li:nth-last-child(11) {
  --_child: 11;
}

menu > li:nth-last-of-type(2) {
  --_type: 1;
}

menu > li:nth-last-of-type(3) {
  --_type: 2;
}

menu > li:nth-last-of-type(4) {
  --_type: 3;
}

menu > li:nth-last-of-type(5) {
  --_type: 4;
}

menu > li:nth-last-of-type(6) {
  --_type: 5;
}

menu > li:nth-last-of-type(7) {
  --_type: 6;
}

menu > li:nth-last-of-type(8) {
  --_type: 7;
}

menu > li:nth-last-of-type(9) {
  --_type: 8;
}

menu > li:nth-last-of-type(10) {
  --_type: 9;
}

menu > li:nth-last-of-type(11) {
  --_type: 10;
}