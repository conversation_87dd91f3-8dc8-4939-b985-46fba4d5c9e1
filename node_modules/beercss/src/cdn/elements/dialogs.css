dialog {
  --_padding: 1.5rem;
  display: block;
  visibility: hidden;
  border: none;
  opacity: 0;
  position: fixed;
  box-shadow: var(--elevate2);
  color: var(--on-surface);
  background-color: var(--surface-container-high);
  padding: var(--_padding);
  z-index: 100;
  inset: 10% auto auto 50%;
  min-inline-size: 20rem;
  max-inline-size: 100%;
  max-block-size: 80%;
  overflow-x: hidden;
  overflow-y: auto;
  transition: all var(--speed3), 0s background-color;
  border-radius: 1.75rem;
  transform: translate(-50%, -4rem);
  outline: none;
}

dialog.small {
  inline-size: 25%;
  block-size: 25%;
}

dialog.medium {
  inline-size: 50%;
  block-size: 50%;
}

dialog.large {
  inline-size: 75%;
  block-size: 75%;
}

dialog:is(.active, [open]) {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, 0);
}

dialog:popover-open {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, 0);
}

dialog:is(.top, .right, .bottom, .left, .max) {
  --_padding: 1rem;
  padding: calc(var(--top) + var(--_padding)) calc(var(--right) + var(--_padding)) calc(var(--bottom) + var(--_padding)) calc(var(--left) + var(--_padding));
}

dialog:is(.top, .bottom) {
  opacity: 1;
  block-size: auto;
  inline-size: 100%;
  min-inline-size: auto;
  max-block-size: 100%;
}

dialog.top {
  inset: 0 auto auto 0;
  transform: translateY(-100%);
  border-radius: 0 0 1rem 1rem;
  padding-block-end: var(--_padding);
}

dialog.bottom {
  inset: auto auto 0 0;
  transform: translateY(100%);
  border-radius: 1rem 1rem 0 0;
}

dialog:is(.left, .right) {
  opacity: 1;
  inset: 0 auto auto 0;
  inline-size: auto;
  block-size: 100%;
  max-block-size: 100%;
  background-color: var(--surface);
}

[dir=rtl] dialog.right,
dialog.left {
  inset: 0 auto auto 0;
  border-radius: 0 1rem 1rem 0;
  transform: translateX(-100%);
}

[dir=rtl] dialog.left,
dialog.right {
  inset: 0 0 auto auto;
  border-radius: 1rem 0 0 1rem;
  transform: translateX(100%);
}

dialog.max {
  inset: 0 auto auto 0;
  inline-size: 100%;
  block-size: 100%;
  max-inline-size: 100%;
  max-block-size: 100%;
  transform: translateY(4rem);
  background-color: var(--surface);
  border-radius: 0;
}

dialog:not(.left, .right, .top, .bottom, .max) {
  --top: 0rem;
  --bottom: 0rem;
  --left: 0rem;
  --right: 0rem;
}

dialog:is(.active, [open]):is(.left, .right, .top, .bottom, .max) {
  transform: translate(0, 0);
}

dialog:popover-open:is(.left, .right, .top, .bottom, .max) {
  transform: translate(0, 0);
}

dialog.small:is(.left, .right) {
  inline-size: 20rem;
}

dialog.medium:is(.left, .right) {
  inline-size: 32rem;
}

dialog.large:is(.left, .right) {
  inline-size: 44rem;
}

dialog.small:is(.top, .bottom) {
  block-size: 16rem;
}

dialog.medium:is(.top, .bottom) {
  block-size: 24rem;
}

dialog.large:is(.top, .bottom) {
  block-size: 32rem;
}
