/* checkbox, radio, switch */
.checkbox,
.radio,
.switch {
  --_size: 1.5rem;
  direction: ltr;
  inline-size: auto;
  block-size: auto;
  line-height: normal;
  white-space: nowrap;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}

:is(.checkbox, .radio, .switch).small {
  --_size: 1rem;
}

:is(.checkbox, .radio, .switch).large {
  --_size: 2rem;
}

:is(.checkbox, .radio, .switch).extra {
  --_size: 2.5rem;
}

:is(.checkbox, .radio) > input {
  inline-size: var(--_size);
  block-size: var(--_size);
  opacity: 0;
}

.switch > input {
  inline-size: 3.25rem;
  block-size: 2rem;
  opacity: 0;
}

:is(.checkbox, .radio, .switch) > span {
  display: inline-flex;
  align-items: center;
  color: var(--on-surface);
  font-size: 0.875rem;
}

:is(.checkbox, .radio) > span:not(:empty) {
  padding-inline-start: 0.25rem;
}

:is(.checkbox, .radio, .switch) > span::before,
:is(.checkbox, .radio, .switch) > span > i,
:is(.checkbox, .radio) > span::after {
  --_size: inherit;
  content: '';
  inline-size: var(--_size);
  block-size: var(--_size);
  box-sizing: border-box;
  margin: 0 auto;
  outline: none;
  color: var(--primary);
  position: absolute;
  inset: auto auto auto calc(var(--_size) * -1);
  border-radius: 50%;
  user-select: none;
  z-index: 1;
}

.switch > span::before,
.switch.icon > span > i {
  position: absolute;
  inset: 50% auto auto 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--speed2);
  font-size: calc(var(--_size) - 0.5rem);
  user-select: none;
  min-inline-size: var(--_size);
  min-block-size: var(--_size);
  content: "";
  color: var(--surface-variant);
  background-color: var(--outline);
}

.switch > span::before,
.switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(0.6);
}

.switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(1);
}

.checkbox > span::before {
  content: "check_box_outline_blank";
}

.checkbox > input:checked + span::before {
  content: "check_box";
  font-variation-settings: "FILL" 1;
}

.checkbox > input:indeterminate + span::before {
  content: "indeterminate_check_box";
}

.radio > span::before {
  content: "radio_button_unchecked";
}

.radio > input:checked + span::before {
  content: "radio_button_checked";
}

:is(.radio, .checkbox, .switch).icon > span::before {
  content: "" !important;
  font-variation-settings: unset !important;
}

:is(.checkbox, .radio) > span::after {
  transition: all var(--speed1);
  background-color: currentColor;
  box-shadow: 0 0 0 0 currentColor;
  opacity: 0;
}

:is(.checkbox, .radio):is(:hover) > input:not(:disabled) + span::after,
:is(.checkbox, .radio) > input:not(:disabled):is(:focus) + span::after {
  box-shadow: 0 0 0 0.5rem currentColor;
  opacity: 0.1;
}

.switch > input:not(:disabled):is(:focus, :hover) + span::before,
.switch.icon > input:not(:disabled):is(:focus, :hover) + span > i {
  box-shadow: 0 0 0 0.5rem var(--active);
}

:is(.checkbox, .radio) > input:checked + span::before,
:is(.checkbox, .radio).icon > input:checked + span > i {
  color: var(--primary);
}

.icon > input:checked + span > i:first-child,
.icon > span > i:last-child {
  opacity: 0;
}

.icon > input:checked + span > i:last-child,
.icon > span > i:first-child {
  opacity: 1;
}

.switch > input:checked + span::after {
  border: none;
  background-color: var(--primary);
}

.switch > input:checked + span::before,
.switch.icon > input:checked + span > i {
  content: "check";
  color: var(--primary);
  background-color: var(--on-primary);
  transform: translate(-1.75rem, -50%) scale(1);
}

.switch > input:active:not(:disabled) + span::before,
.switch.icon > input:active:not(:disabled) + span > i {
  transform: translate(-3rem, -50%) scale(1.2);
}

.switch > input:active:checked:not(:disabled) + span::before,
.switch.icon > input:active:checked:not(:disabled) + span > i {
  transform: translate(-1.75rem, -50%) scale(1.2);
}

:is(.checkbox, .radio, .switch) > input:disabled + span {
  opacity: 0.5;
  cursor: not-allowed;
}

.switch > span::after {
  content: "";
  position: absolute;
  inset: 50% auto auto 0;
  background-color: var(--active);
  border: 0.125rem solid var(--outline);
  box-sizing: border-box;
  inline-size: 3.25rem;
  block-size: 2rem;
  border-radius: 2rem;
  transform: translate(-3.25rem, -50%);
}

.field > :is(nav, .row) {
  flex-grow: 1;
  padding: 0 1rem;
}

.field.round > :is(nav, .row) {
  flex-grow: 1;
  padding: 0 1.5rem;
}

[dir=rtl] .switch {
  transform: scale(-1);
}

[dir=rtl] .switch > span::before,
[dir=rtl] .switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(-0.6);
}

[dir=rtl] .switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(-1);
}

[dir=rtl] .switch > input:checked + span::before,
[dir=rtl] .switch.icon > input:checked + span > i {
  transform: translate(-1.75rem, -50%) scale(-1);
}

.switch > :focus-visible + span::after {
  outline: 0.125rem solid var(--primary);
  outline-offset: 0.25rem;
}

:is(.checkbox, .radio) > :focus-visible + span::before {
  outline: 0.125rem solid var(--primary);
  outline-offset: 0.375rem;
}