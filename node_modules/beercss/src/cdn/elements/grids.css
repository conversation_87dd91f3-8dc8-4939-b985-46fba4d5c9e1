.grid {
  --_gap: 1rem;
  display: grid;
  grid-template-columns: repeat(12, calc(8.33% - var(--_gap) + (var(--_gap) / 12)));
  gap: var(--_gap);
  block-size: auto;
}

.grid.no-space {
  --_gap: 0rem;
}

.grid.medium-space {
  --_gap: 1.5rem;
}

.grid.large-space {
  --_gap: 2rem;
}

.grid > * {
  margin: 0;
}

.s1 {
  grid-area: auto/span 1;
}

.s2 {
  grid-area: auto/span 2;
}

.s3 {
  grid-area: auto/span 3;
}

.s4 {
  grid-area: auto/span 4;
}

.s5 {
  grid-area: auto/span 5;
}

.s6 {
  grid-area: auto/span 6;
}

.s7 {
  grid-area: auto/span 7;
}

.s8 {
  grid-area: auto/span 8;
}

.s9 {
  grid-area: auto/span 9;
}

.s10 {
  grid-area: auto/span 10;
}

.s11 {
  grid-area: auto/span 11;
}

.s12 {
  grid-area: auto/span 12;
}

@media only screen and (min-width: 601px) {
  .m1 {
    grid-area: auto/span 1;
  }

  .m2 {
    grid-area: auto/span 2;
  }

  .m3 {
    grid-area: auto/span 3;
  }

  .m4 {
    grid-area: auto/span 4;
  }

  .m5 {
    grid-area: auto/span 5;
  }

  .m6 {
    grid-area: auto/span 6;
  }

  .m7 {
    grid-area: auto/span 7;
  }

  .m8 {
    grid-area: auto/span 8;
  }

  .m9 {
    grid-area: auto/span 9;
  }

  .m10 {
    grid-area: auto/span 10;
  }

  .m11 {
    grid-area: auto/span 11;
  }

  .m12 {
    grid-area: auto/span 12;
  }
}

@media only screen and (min-width: 993px) {
  .l1 {
    grid-area: auto/span 1;
  }

  .l2 {
    grid-area: auto/span 2;
  }

  .l3 {
    grid-area: auto/span 3;
  }

  .l4 {
    grid-area: auto/span 4;
  }

  .l5 {
    grid-area: auto/span 5;
  }

  .l6 {
    grid-area: auto/span 6;
  }

  .l7 {
    grid-area: auto/span 7;
  }

  .l8 {
    grid-area: auto/span 8;
  }

  .l9 {
    grid-area: auto/span 9;
  }

  .l10 {
    grid-area: auto/span 10;
  }

  .l11 {
    grid-area: auto/span 11;
  }

  .l12 {
    grid-area: auto/span 12;
  }
}
