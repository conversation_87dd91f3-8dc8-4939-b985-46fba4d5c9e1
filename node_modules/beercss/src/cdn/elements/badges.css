.badge {
  --_x: 0;
  --_y: -100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  font-size: 0.6875rem;
  text-transform: none;
  z-index: 2;
  padding: 0 0.25rem;
  min-block-size: 1rem;
  min-inline-size: 1rem;
  background-color: var(--error);
  color: var(--on-error);
  line-height: normal;
  border-radius: 1rem;
  inset: 50% auto auto 50%;
  transform: translate(var(--_x, 50%), var(--_y, -50%));
  font-family: var(--font);
}

.badge.top {
  --_y: -100%;
}

.badge.bottom {
  --_y: 0;
}

.badge.left {
  --_x: -100%;
}

.badge.right {
  --_x: 0;
}

.badge.border {
  border-color: var(--error);
  color: var(--error);
  background-color: var(--surface);
}

.badge:is(.circle, .square) {
  text-align: center;
  inline-size: auto;
  block-size: auto;
  padding: 0 0.25rem;
  border-radius: 1rem;
}

.badge.square {
  border-radius: 0;
}

.badge.min > * {
  display: none;
}

.badge.min {
  clip-path: circle(18.75% at 50% 50%);
}

nav:is(.left, .right, .top, .bottom) > a > .badge,
nav:is(.left, .right, .top, .bottom) > :is(ol, ul) > li > a > .badge {
  inset: 1rem auto auto 50%;
}

.badge.none {
  inset: auto !important;
  transform: none;
  position: relative;
  margin: 0 0.125rem;
}

:is(button, .button, .chip) > .badge.none {
  margin: 0 -0.5rem;
}
