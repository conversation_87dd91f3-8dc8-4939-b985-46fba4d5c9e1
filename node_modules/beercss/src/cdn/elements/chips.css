.chip {
  --_padding: 0.75rem;
  --_size: 2rem;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  block-size: var(--_size);
  min-inline-size: var(--_size);
  font-size: 0.875rem;
  font-weight: 500;
  background-color: transparent;
  border: 0.0625rem solid var(--outline-variant);
  color: var(--on-surface-variant);
  padding: 0 var(--_padding);
  margin: 0 0.5rem;
  text-transform: none;
  border-radius: 0.5rem;
  transition: transform var(--speed3), border-radius var(--speed3), padding var(--speed3);
  user-select: none;
  gap: 0.5rem;
  line-height: normal;
  letter-spacing: normal;
}

.chip.medium {
  --_size: 2.5rem;
  --_padding: 1rem;
}

.chip.large {
  --_padding: 1.25rem;
  --_size: 3rem;
}

.chip.fill {
  border: none;
}
