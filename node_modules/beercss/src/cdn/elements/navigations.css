nav > :is(ol, ul),
nav > :is(ol, ul) > li {
  all: unset;
}

nav,
.row,
a.row,
nav.drawer > :is(a, label),
nav.drawer > :is(ol, ul) > li > :is(a, label) {
  display: flex;
  align-items: center;
  align-self: normal;
  text-align: start;
  justify-content: flex-start;
  white-space: nowrap;
  gap: 1rem;
  border-radius: 0;
}

a.row,
nav.row {
  min-block-size: 3rem;
  margin: 0;
}

:is(nav, .row, .max) > :only-child,
nav > :is(ol, ul) > li > :only-child {
  margin: 0;
}

:is(nav, .row) > :not(ul, ol) {
  margin: 0;
  white-space: normal;
  flex: none;
}

:is(nav, .row).min {
  display: inline-flex;
}

:is(nav, .row, li).no-space {
  gap: 0;
}

:is(nav, .row, li).tiny-space {
  gap: 0.5rem;
}

:is(nav, .row, li).medium-space {
  gap: 1.5rem;
}

:is(nav, .row, li).large-space {
  gap: 2rem;
}

:is(nav, .row) > .max,
:is(nav, .row) > :is(ol, ul) > .max,
nav.drawer > :is(a, label) > .max,
nav.drawer > :is(ol, ul) > li > :is(a, label) > .max {
  flex: 1;
}

:is(nav, .row).wrap {
  display: flex;
  flex-wrap: wrap;
}

:is(header, footer) > :is(nav, .row) {
  min-block-size: inherit;
}

nav:is(.left, .right, .top, .bottom) {
  border: 0;
  position: fixed;
  color: var(--on-surface);
  transform: none;
  z-index: 100;
  block-size: auto;
  inline-size: auto;
  text-align: center;
  padding: calc(var(--top) + 0.5rem) calc(var(--right) + 1rem) calc(var(--bottom) + 0.5rem) calc(var(--left) + 1rem);
  margin: 0;
}

nav:is(.left, .right) {
  justify-content: flex-start;
  flex-direction: column;
  background-color: var(--surface);
}

nav:is(.top, .bottom) {
  justify-content: center;
  flex-direction: row;
  background-color: var(--surface-container);
}

nav.top {
  block-size: calc(var(--top) + 5rem);
  inset: 0 0 auto 0;
  padding-block-end: 0.5rem;
}

nav.left,
[dir=rtl] nav.right {
  inline-size: calc(var(--left) + 5rem);
  inset: 0 auto 0 0;
  padding-inline-end: 1rem;
}

[dir=rtl] nav.right {
  padding-inline-end: calc(var(--left) + 1rem);
}

nav.right,
[dir=rtl] nav.left {
  inline-size: calc(var(--right) + 5rem);
  inset: 0 0 0 auto;
  padding-inline-start: 1rem;
}

[dir=rtl] nav.left {
  padding-inline-start: calc(var(--right) + 1rem);
}

nav.bottom {
  min-block-size: calc(var(--bottom) + 5rem);
  inset: auto 0 0 0;
  padding-block-start: 0.5rem;
}

nav.drawer,
[dir=rtl] nav.drawer {
  flex-direction: column;
  align-items: normal;
  inline-size: 20rem;
  gap: 0;
}

nav.drawer:not(.left, .right, .top, .bottom) {
  padding: 0.5rem 1rem;
}

dialog > nav.drawer:not(.left, .right, .top, .bottom) {
  padding: 0 1rem;
  background-color: inherit;
}

nav.drawer:is(.min, .max) {
  inline-size: auto;
}

nav.drawer.max {
  inline-size: 100%;
}

nav.drawer > :is(a, label),
nav.drawer > :is(ol, ul) > li > :is(a, label),
:is(a.row, nav.row):is(.wave, .slow-ripple, .ripple, .fast-ripple) {
  padding: 0.75rem;
  font-size: inherit;
}

nav.drawer > a,
nav.drawer > :is(ol, ul) > li > a {
  border-radius: 2rem;
}

nav.drawer > a:is(:hover, .active),
nav.drawer > :is(ol, ul) > li > a:is(:hover, .active) {
  background-color: var(--secondary-container);
}

nav.drawer > a:is(:hover, :focus, .active) > i,
nav.drawer > :is(ol, ul) > li > a:is(:hover, :focus, .active) > i {
  font-variation-settings: "FILL" 1;
}

nav > :is(ol, ul) {
  all: inherit;
  flex: auto;
}

nav:not(.left, .right, .bottom, .top) > :is(ol, ul) {
  padding: 0;
}

nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip),
nav:is(.left, .right, .top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip) {
  align-self: center;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  line-height: normal;
}

nav:is(.top, .bottom):not(.drawer) > a:not(.button, .chip),
nav:is(.top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip) {
  inline-size: 3.5rem;
}

nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip) > i,
nav:is(.left, .right, .top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip) > i {
  padding: 0.25rem;
  border-radius: 2rem;
  transition: padding var(--speed1) linear;
  margin: 0 auto;
}

nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip):is(:hover, :focus, .active) > i,
nav:is(.left, .right, .top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip):is(:hover, :focus, .active) > i {
  background-color: var(--secondary-container);
  color: var(--on-secondary-container);
  padding: 0.25rem 1rem;
  font-variation-settings: "FILL" 1;
}

nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip).active {
  background: none !important;
  color: currentColor !important;
}

:is(nav, .row):is(.left-align, .top-align, .vertical) {
  justify-content: flex-start;
}

:is(nav, .row):is(.right-align, .bottom-align) {
  justify-content: flex-end;
}

:is(nav, .row):is(.center-align, .middle-align) {
  justify-content: center;
}

:is(nav, .row):is(.left-align, .top-align, .vertical).vertical {
  align-items: flex-start;
}

:is(nav, .row):is(.right-align, .bottom-align).vertical {
  align-items: flex-end;
}

:is(nav, .row):is(.center-align, .middle-align).vertical {
  align-items: center;
}

:is(.drawer, .vertical) > :is(li, [class*=divider], hr):not(.vertical),
:is(.drawer, .vertical) > :is(ol, ul) > li:not(.vertical) {
  align-self: stretch;
}

nav:not(.left, .right) > .space {
  inline-size: 0.5rem;
}

nav:not(.left, .right) > .medium-space {
  inline-size: 1rem;
}

nav:not(.left, .right) > .large-space {
  inline-size: 1.5rem;
}

nav.tabbed {
  background-color: var(--surface-container);
  border-radius: 4rem !important;
  gap: 0rem;
  block-size: 4rem;
}

nav.tabbed.small {
  block-size: 3rem;
}

nav.tabbed.large {
  block-size: 5rem;
}

nav.tabbed > a {
  border-radius: inherit;
  block-size: inherit;
  display: inline-flex;
  align-items: center;
  padding-inline: 1rem;
  gap: 0.5rem;
  font-size: 1rem;
  flex: 1;
}

nav.tabbed > a.active {
  background-color: var(--primary-container);
}

nav.toolbar {
  display: inline-flex;
  justify-content: space-around;
  border-radius: 2rem;
  background-color: var(--surface-container);
  color: var(--on-surface);
  padding: 0 1rem;
  gap: 0.5rem;
  min-block-size: 4rem;
  min-inline-size: 4rem;
}

nav.toolbar > a {
  display: inline-flex;
  gap: 0.5rem;
  min-inline-size: 2.5rem;
  min-block-size: 2.5rem;
  border-radius: 1.75rem;
}

nav.toolbar > a:has(> :not(i)) {
  padding: 0 1rem;
}

nav.toolbar > a.active {
  background-color: var(--secondary-container);
  color: var(--on-secondary-container);
}

nav.toolbar.fill {
  background-color: var(--primary-container) !important;
  color: var(--on-primary-container) !important;
}

nav.toolbar.fill > a.active {
  background-color: var(--surface-container) !important;
  color: var(--on-surface) !important;
}

nav.toolbar.vertical {
  flex-direction: column !important;
  min-inline-size: 4rem;
  padding: 1rem 0;
  align-self: center;
  align-items: center !important;
}

nav.toolbar.vertical > a {
  inline-size: 2.5rem;
  block-size: 2.5rem;
}

nav.toolbar.vertical > a > :is(div, span):not(.badge, .tooltip) {
  display: none;
}

nav.toolbar.max {
  border-radius: 0;
  display: flex;
}

nav.group {
  background: none !important;
}

nav.group:is(.connected, .split) {
  gap: 0.125rem;
}

nav.group:not(.split) > :is(.button, button):not(.chip, .fill, .border).active {
  background-color: var(--primary);
  color: var(--on-primary);
}

nav.group:not(.split) > :is(.button, button):not(.chip, .fill, .border) {
  background-color: var(--surface-container);
  color: var(--on-surface-container);
}

nav.group:is(.connected, .split) > :is(.button, button):not(.chip).active,
nav.split > :is(.button, button):active {
  border-radius: 2rem !important;
}

:not(nav) > :is(ul, ol) {
  all: revert;
}

:is(.scroll, .no-scroll, .no-space, .tabs, .tabbed) > :focus-visible {
  outline: 0.125rem solid var(--primary);
  outline-offset: -0.125rem;
}

nav.split > :is(.button, button):not(.chip, .fill, .border) {
  background-color: var(--primary);
  color: var(--on-primary);
}

nav.primary > :is(button, .button),
nav:not(.toolbar, .tabbed, .drawer, .group).primary-container > a:is(:hover, :focus, .active) > i,
nav.drawer.primary-container > a:is(:hover, :focus, .active),
nav:not(.split).primary-container > :is(a, button, .button).active,
:is(a, button, .button):not(.extend).primary-container.active {
  background-color: var(--primary) !important;
  color: var(--on-primary) !important;
}

nav.primary-container > :is(button, .button),
nav:not(.toolbar, .tabbed, .drawer, .group).primary > a:is(:hover, :focus, .active) > i,
nav.drawer.primary > a:is(:hover, :focus, .active),
nav:not(.split).primary > :is(a, button, .button).active,
:is(a, button, .button):not(.extend).primary.active {
  background-color: var(--primary-container) !important;
  color: var(--on-primary-container) !important;
}

nav.secondary > :is(button, .button),
nav:not(.toolbar, .tabbed, .drawer, .group).secondary-container > a:is(:hover, :focus, .active) > i,
nav.drawer.secondary-container > a:is(:hover, :focus, .active),
nav:not(.split).secondary-container > :is(a, button, .button).active,
:is(a, button, .button):not(.extend).secondary-container.active {
  background-color: var(--secondary) !important;
  color: var(--on-secondary) !important;
}

nav.secondary-container > :is(button, .button),
nav:not(.toolbar, .tabbed, .drawer, .group).secondary > a:is(:hover, :focus, .active) > i,
nav.drawer.secondary > a:is(:hover, :focus, .active),
nav:not(.split).secondary > :is(a, button, .button).active,
:is(a, button, .button):not(.extend).secondary.active {
  background-color: var(--secondary-container) !important;
  color: var(--on-secondary-container) !important;
}

nav.tertiary > :is(button, .button),
nav:not(.toolbar, .tabbed, .drawer, .group).tertiary-container > a:is(:hover, :focus, .active) > i,
nav.drawer.tertiary-container > a:is(:hover, :focus, .active),
nav:not(.split).tertiary-container > :is(a, button, .button).active,
:is(a, button, .button):not(.extend).tertiary-container.active {
  background-color: var(--tertiary) !important;
  color: var(--on-tertiary) !important;
}

nav.tertiary-container > :is(button, .button),
nav:not(.toolbar, .tabbed, .drawer, .group).tertiary > a:is(:hover, :focus, .active) > i,
nav.drawer.tertiary > a:is(:hover, :focus, .active),
nav:not(.split).tertiary > :is(a, button, .button).active,
:is(a, button, .button):not(.extend).tertiary.active {
  background-color: var(--tertiary-container) !important;
  color: var(--on-tertiary-container) !important;
}

@media only screen and (max-width: 600px) {
  nav.top,
  nav.bottom {
    justify-content: space-around;
  }
}
