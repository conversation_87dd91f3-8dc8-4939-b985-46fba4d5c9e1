i,
:is(.checkbox, .radio, .switch) > span::before,
:is(.checkbox, .radio, .switch) > span > i {
  --_size: 1.5rem;
  font-family: var(--font-icon);
  font-weight: normal;
  font-style: normal;
  font-size: var(--_size);
  letter-spacing: normal;
  text-transform: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  inline-size: var(--_size);
  min-inline-size: var(--_size);
  block-size: var(--_size);
  min-block-size: var(--_size);
  box-sizing: content-box;
  line-height: normal;
  border-radius: 0;
}

i.tiny {
  --_size: 1rem;
}

.chip > i,
i.small {
  --_size: 1.25rem;
}

i.medium {
  --_size: 1.5rem;
}

i.large {
  --_size: 1.75rem;
}

i.extra {
  --_size: 2rem;
}

i.fill,
a.row:is(:hover, :focus) > i,
.transparent:is(:hover, :focus) > i {
  font-variation-settings: "FILL" 1;
}

i > :is(img, svg) {
  inline-size: 100%;
  block-size: 100%;
  background-size: 100%;
  border-radius: inherit;
  position: absolute;
  inset: 0 auto auto 0;
  padding: inherit;
}

i[class*=fa-] {
  font-size: calc(var(--_size) * 0.85);
  line-height: normal;
  block-size: auto;
  min-block-size: auto;
}
