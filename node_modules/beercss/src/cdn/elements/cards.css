article {
  --_padding: 1rem;
  box-shadow: var(--elevate1);
  background-color: var(--surface-container-low);
  color: var(--on-surface);
  padding: var(--_padding);
  border-radius: 0.75rem;
  display: block;
  transition: transform var(--speed3), border-radius var(--speed3), padding var(--speed3);
}

article.small {
  block-size: 12rem;
}

article.medium {
  block-size: 20rem;
}

article.large {
  block-size: 32rem;
}

article.border {
  box-shadow: none;
  border: 0.0625rem solid var(--outline-variant);
}