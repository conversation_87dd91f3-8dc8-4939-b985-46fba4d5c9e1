.slider {
  --_start: 0%;
  --_end: 0%;
  --_value1: "";
  --_value2: "";
  --_track: 1rem;
  --_thumb: max(2.5rem, calc(var(--_track) + 0.5rem));
  display: flex;
  align-items: center !important;
  inline-size: auto;
  block-size: var(--_thumb);
  flex: none;
  direction: ltr;
  margin: 0 1.25rem;
}

[dir=rtl] .slider {
  transform: scaleX(-1);
}

.slider.vertical {
  flex-direction: row !important;
  margin: 0.5rem auto !important;
  padding: 50% 0;
  transform: rotate(-90deg);
  inline-size: 100%;
}

.slider.tiny {
  --_track: 1rem;
}

.slider.small {
  --_track: 1.5rem;
}

.slider.medium {
  --_track: 2.5rem;
}

.slider.large {
  --_track: 3.5rem;
}

.slider.extra {
  --_track: 6rem;
}

.slider > input {
  appearance: none;
  box-shadow: none;
  border: none;
  outline: none;
  pointer-events: none;
  inline-size: 100%;
  block-size: var(--_track);
  background: none;
  z-index: 1;
  padding: 0;
  margin: 0;
  transform: rotate(0deg);
}

.slider > input:only-of-type {
  pointer-events: all;
}

.slider > input + input {
  position: absolute;
}

.slider > input::-webkit-slider-thumb {
  appearance: none;
  box-shadow: none;
  border: none;
  outline: none;
  pointer-events: all;
  block-size: var(--_thumb);
  inline-size: 0.25rem;
  border-radius: 0.25rem;
  background: var(--primary);
  cursor: grab;
  margin: 0;
  z-index: 1;
}

.slider > input::-webkit-slider-thumb:active {
  cursor: grabbing;
}

.slider > input::-moz-range-thumb {
  appearance: none;
  box-shadow: none;
  border: none;
  outline: none;
  pointer-events: all;
  block-size: 2.75rem;
  inline-size: 0.25rem;
  border-radius: 0.25rem;
  background: var(--primary);
  cursor: grab;
  margin: 0;
}

.slider > input::-moz-range-thumb:active {
  cursor: grabbing;
}

.slider > input:not(:disabled):is(:focus)::-webkit-slider-thumb {
  transform: scaleX(0.6);
}

.slider > input:not(:disabled):is(:focus)::-moz-range-thumb {
  transform: scaleX(0.6);
}

.slider > input:disabled {
  cursor: not-allowed;
  opacity: 1;
}

.slider > input:disabled::-webkit-slider-thumb {
  background: var(--outline);
  cursor: not-allowed;
}

.slider > input:disabled::-moz-range-thumb {
  background: var(--outline);
  cursor: not-allowed;
}

.slider > input:disabled ~ span {
  background: var(--outline);
}

.slider > span {
  position: absolute;
  block-size: var(--_track);
  border-radius: 1rem 0 0 1rem;
  background: var(--primary);
  color: var(--on-primary);
  z-index: 0;
  inset: calc(50% - (var(--_track) / 2)) var(--_end) auto var(--_start);
  clip-path: polygon(0 0, calc(100% - 0.5rem) 0, calc(100% - 0.5rem) 100%, 0 100%);
}

.slider > input[type=range] + input[type=range] ~ span {
  border-radius: 0;
  clip-path: polygon(0.5rem 0, max(0.5rem, calc(100% - 0.5rem)) 0, max(0.5rem, calc(100% - 0.5rem)) 100%, 0.5rem 100%);
}

.field > .slider {
  inline-size: 100%;
}

.slider::before {
  content: "";
  position: absolute;
  inline-size: 100%;
  block-size: var(--_track);
  border-radius: 1rem;
  background: var(--secondary-container);
  clip-path: polygon(calc(var(--_start) - 0.5rem) 0, 0 0, 0 100%, calc(var(--_start) - 0.5rem) 100%, calc(var(--_start) - 0.5rem) 0, calc(100% - var(--_end) + 0.5rem) 0, 100% 0, 100% 100%, calc(100% - var(--_end) + 0.5rem) 100%, calc(100% - var(--_end) + 0.5rem) 0);
}

.slider:has(> [disabled])::before {
  background: var(--outline-variant);
}

.slider:has([disabled]) {
  opacity: 0.62;
}

.slider > span > i {
  position: absolute;
  block-size: auto;
  inset: 0 auto 0 0.5rem;
  color: currentColor;
  z-index: 1;
}

.slider:not(.medium, .large, .extra) > span > i {
  display: none;
}

.slider.vertical > i {
  transform: rotate(90deg);
}

.slider > .tooltip {
  visibility: hidden !important;
  opacity: 0 !important;
  inset: 0 auto auto calc(100% - var(--_end));
  border-radius: 2rem;
  transition: top var(--speed2) ease, opacity var(--speed2) ease;
  transform: translate(-50%, -50%) !important;
  padding: 0.75rem 1rem;
}

[dir=rtl] .slider > .tooltip {
  transform: translate(-50%, -50%) scaleX(-1) !important;
}

.slider > .tooltip + .tooltip {
  inset: 0.25rem calc(100% - var(--_start)) auto auto;
  transform: translate(50%, -50%) !important;
}

[dir=rtl] .slider > .tooltip + .tooltip {
  transform: translate(50%, -50%) scaleX(-1) !important;
}

.slider > .tooltip::before {
  content: var(--_value1);
}

.slider > .tooltip + .tooltip::before {
  content: var(--_value2);
}

.slider > :focus ~ .tooltip {
  inset-block-start: -1rem !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.slider.vertical > .tooltip {
  inset-block-start: auto !important;
  margin-block-start: calc(-1 * var(--_thumb)) !important;
  block-size: 2.5rem;
  inline-size: 2.5rem;
  transform: rotate(90deg) translate(-75%, 50%) !important;
}

.slider.vertical > .tooltip + .tooltip {
  transform: rotate(90deg) translate(-75%, -50%) !important;
}

:is(nav, .row, .field) > .slider:not(.circle, .small, .medium, .large) {
  flex: auto;
}

.slider.max,
.slider.max.vertical,
.slider.max > input,
.slider.max.vertical > input {
  all: unset;
  margin: 0 !important;
  position: absolute;
  color: var(--primary);
  inset: 0;
  border-radius: inherit;
  overflow: hidden;
  z-index: 2;
  cursor: grab;
  inline-size: 100%;
  block-size: 100%;
}

.slider.max::before {
  display: none;
}

.slider.max.vertical > input {
  writing-mode: vertical-lr;
  transform: rotate(-180deg);
}

.slider.max > input::-webkit-slider-thumb {
  opacity: 0;
  inline-size: 1rem;
  block-size: 100vh;
  transform: none !important;
}

.slider.max > input::-moz-range-thumb {
  opacity: 0;
  inline-size: 1rem;
  block-size: 100vh;
  transform: none !important;
}

.slider.max > span {
  block-size: auto !important;
  inset: 0 var(--_end) 0 var(--_start);
  clip-path: none;
  background: currentcolor;
  color: inherit;
  border-radius: 0;
}

.slider.max.vertical > span {
  inset: var(--_end) 0 var(--_start) 0;
}

.slider > input:focus-visible::-webkit-slider-thumb {
  outline: 0.1875rem solid var(--primary);
  outline-offset: 0.25rem;
}

.slider > input:focus-visible::-moz-range-thumb {
  outline: 0.1875rem solid var(--primary);
  outline-offset: 0.25rem;
}

.slider.max > input:focus-visible {
  outline: 0.1875rem solid var(--primary);
  outline-offset: -0.125rem;
}

@media (pointer: coarse) {
  .slider > :hover ~ .tooltip {
    inset-block-start: -1rem !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
}