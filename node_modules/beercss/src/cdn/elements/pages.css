.page {
  --_transform: translate(0, 0);
  opacity: 0;
  position: absolute;
  display: none;
}

.page.active {
  opacity: 1;
  position: inherit;
  display: inherit;
  animation: var(--speed4) to-page ease;
}

.page.active.top {
  --_transform: translate(0, -4rem);
}

.page.active.bottom {
  --_transform: translate(0, 4rem);
}

.page.active.left {
  --_transform: translate(-4rem, 0);
}

.page.active.right {
  --_transform: translate(4rem, 0);
}

@keyframes to-page {
  from {
    opacity: 0;
    transform: var(--_transform);
  }

  to {
    opacity: 1;
    transform: translate(0, 0);
  }
}
