.tabs {
  display: flex;
  white-space: nowrap;
  border-block-end: 0.0625rem solid var(--surface-variant);
  border-radius: 0;
}

.tabs:not(.left-align, .right-align, .center-align) {
  justify-content: space-around;
}

.tabs > a {
  display: flex;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--on-surface-variant);
  padding: 0.5rem 1rem;
  text-align: center;
  min-block-size: 3rem;
  inline-size: 100%;
  gap: 0.25rem;
}

.tabs.small > a {
  min-block-size: 2rem;
}

.tabs.large > a {
  min-block-size: 4rem;
}

.tabs > a.active,
.tabs > a.active > i {
  color: var(--primary);
}

.tabs > a.active::before {
  content: '';
  position: absolute;
  inset: auto 0 0 0;
  block-size: 0.125rem;
  background-color: var(--primary);
}

.tabs.min > a.active::before {
  margin: 0 auto;
  max-inline-size: min(100%, 4rem);
}

.tabs:is(.left-align, .center-align, .right-align) > a {
  inline-size: auto;
}
