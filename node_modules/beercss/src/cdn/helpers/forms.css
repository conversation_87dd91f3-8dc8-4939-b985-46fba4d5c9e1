[class*=round] {
  --_round: 2rem;
  border-radius: var(--_round) !important;
}

.small-round {
  --_round: 0.5rem;
}

.large-round {
  --_round: 3.5rem;
}

.no-round,
.square,
.top-round,
.bottom-round,
.left-round,
.right-round {
  border-radius: 0.5rem !important;
}

.top-round {
  border-start-start-radius: var(--_round) !important;
  border-start-end-radius: var(--_round) !important;
}

.bottom-round {
  border-end-end-radius: var(--_round) !important;
  border-end-start-radius: var(--_round) !important;
}

.left-round {
  border-start-start-radius: var(--_round) !important;
  border-end-start-radius: var(--_round) !important;
}

.right-round {
  border-start-end-radius: var(--_round) !important;
  border-end-end-radius: var(--_round) !important;
}

.circle:not(.extend) {
  border-radius: 50%;
}

:is(.circle, .square):is(button, .button, .chip) {
  padding: 0;
  block-size: var(--_size);
  inline-size: var(--_size);
}

:is(.circle, .square) > span {
  display: none;
}

:is(.circle, .square).round {
  border-radius: 1rem !important;
}

.border:not(table, .field, .list, menu, article) {
  box-sizing: border-box;
  border: 0.0625rem solid var(--outline);
  background-color: transparent;
  box-shadow: none;
}

.no-border {
  border-color: transparent !important;
}

.border:not(.extend, .circle, .square, .badge) {
  box-sizing: content-box;
}
