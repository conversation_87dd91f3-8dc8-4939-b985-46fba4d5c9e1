html {
  font-size: var(--size);
}

body {
  font-family: var(--font);
  font-size: 0.875rem;
  line-height: 1.5rem;
  letter-spacing: 0.0313rem;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
  display: block;
  align-items: center;
  line-height: normal;
}

h1 {
  font-size: 3.5625rem;
}

h2 {
  font-size: 2.8125rem;
}

h3 {
  font-size: 2.25rem;
}

h4 {
  font-size: 2rem;
}

h5 {
  font-size: 1.75rem;
}

h6 {
  font-size: 1.5rem;
}

h1.small {
  font-size: 3.0625rem;
}

h2.small {
  font-size: 2.3125rem;
}

h3.small {
  font-size: 1.75rem;
}

h4.small {
  font-size: 1.5rem;
}

h5.small {
  font-size: 1.25rem;
}

h6.small {
  font-size: 1rem;
}

h1.large {
  font-size: 4.0625rem;
}

h2.large {
  font-size: 3.3125rem;
}

h3.large {
  font-size: 2.75rem;
}

h4.large {
  font-size: 2.5rem;
}

h5.large {
  font-size: 2.25rem;
}

h6.large {
  font-size: 2rem;
}

.link {
  color: var(--primary) !important;
}

.inverse-link {
  color: var(--inverse-primary) !important;
}

.truncate {
  overflow: hidden;
  white-space: nowrap !important;
  text-overflow: ellipsis;
  flex: inherit;
}

.truncate > * {
  white-space: nowrap !important;
}

.small-text {
  font-size: 0.75rem;
}

.medium-text {
  font-size: 0.875rem;
}

.large-text {
  font-size: 1rem;
}

.upper {
  text-transform: uppercase;
}

.lower {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.bold {
  font-weight: bold;
}

.overline {
  text-decoration: line-through;
}

.underline {
  text-decoration: underline;
}

.italic {
  font-style: italic;
}

p {
  margin: 0.5rem 0;
}

.no-line {
  line-height: normal;
}

.tiny-line {
  line-height: 1.25rem;
}

.small-line {
  line-height: 1.5rem;
}

.medium-line {
  line-height: 1.75rem;
}

.large-line {
  line-height: 2rem;
}

.extra-line {
  line-height: 2.25rem;
}

pre {
  border-radius: 0;
  background-color: var(--surface-container);
  white-space: pre-wrap;
  padding: 1rem;
  border-inline-start: 0.25rem solid var(--primary);
  font-family: inherit;
}

blockquote {
  border-radius: 0;
  padding: 1rem;
  border-inline-start: 0.25rem solid var(--primary);
  font-family: inherit;
}

code {
  border-radius: 0;
  background-color: var(--surface-container);
  white-space: pre-wrap;
  padding: 0.25rem;
}

pre > code,
blockquote > code {
  padding: 0;
}

.scroll > code {
  white-space: pre;
}

pre:has(> code){
  direction: ltr;
  text-align: start;
}