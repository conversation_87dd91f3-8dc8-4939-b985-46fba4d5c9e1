.horizontal {
  display: inline-flex;
  flex-direction: row !important;
  gap: 1rem;
  inline-size: auto !important;
  max-inline-size: none !important;
}

.horizontal > * {
  margin-block: 0 !important;
}

.vertical {
  display: flex;
  flex-direction: column !important;
}

:is(a, button, .button, .chip).vertical {
  display: inline-flex;
  gap: 0.25rem;
  block-size: auto !important;
  max-block-size: none !important;
  padding-block: 0.5rem;
}

.vertical > * {
  margin-inline: 0 !important;
}
