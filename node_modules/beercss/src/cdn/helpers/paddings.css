[class*=padding]:not(.left-padding, .right-padding, .top-padding, .bottom-padding, .horizontal-padding, .vertical-padding) {
  padding: var(--_padding) !important;
}

[class*=padding] {
  --_padding: 1rem;
}

.no-padding {
  --_padding: 0 !important;
}

.tiny-padding {
  --_padding: 0.25rem !important;
}

.small-padding {
  --_padding: 0.5rem !important;
}

.large-padding {
  --_padding: 1.5rem !important;
}

.left-padding,
.horizontal-padding {
  padding-inline-start: var(--_padding) !important;
}

.right-padding,
.horizontal-padding {
  padding-inline-end: var(--_padding) !important;
}

.top-padding,
.vertical-padding {
  padding-block-start: var(--_padding) !important;
}

.bottom-padding,
.vertical-padding {
  padding-block-end: var(--_padding) !important;
}
