:is(.wave, .chip, .button, button, nav.tabbed > a, .tabs > a, .toolbar > a):not(.slow-ripple, .ripple, .fast-ripple)::after {
  content: "";
  position: absolute;
  inset: 0;
  z-index: 1;
  border-radius: inherit;
  inline-size: 100%;
  block-size: 100%;
  background-position: center;
  background-image: radial-gradient(circle, currentColor 1%, transparent 1%);
  opacity: 0;
  transition: none;
}

:is(.wave, .chip, .button, button, nav.tabbed > a, .tabs > a, .toolbar > a):not(.slow-ripple, .ripple, .fast-ripple):is(:focus-visible, :hover)::after {
  background-size: 15000%;
  opacity: 0.1;
  transition: background-size var(--speed2) linear;
}

:is(.wave, .chip, .button, button, nav.tabbed > a, .tabs > a, .toolbar > a):not(.slow-ripple, .ripple, .fast-ripple):active::after {
  background-size: 5000%;
  opacity: 0;
  transition: none;
}

.no-wave::after,
.no-wave:is(:hover, :active)::after {
  display: none;
}
