const V=[];function S(){return window==null?void 0:window.matchMedia("(pointer: coarse)").matches}function lt(){return window==null?void 0:window.matchMedia("(prefers-color-scheme: dark)").matches}async function W(t){await new Promise(n=>setTimeout(n,t))}function ft(){return"fxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const n=Math.random()*16|0;return(t==="x"?n:n&3|8).toString(16)})}function h(t,n){try{return typeof t=="string"?(n??document).querySelector(t):t}catch{return null}}function u(t,n){try{return typeof t=="string"?(n??document).querySelectorAll(t):t??V}catch{return V}}function a(t,n){return(t==null?void 0:t.classList.contains(n))??!1}function y(t,n){var e;return((e=t==null?void 0:t.tagName)==null?void 0:e.toLowerCase())===n}function T(t,n){var e;return((e=t==null?void 0:t.type)==null?void 0:e.toLowerCase())===n}function g(t,n){if(t instanceof NodeList)for(let e=0;e<t.length;e++)t[e].classList.add(n);else t==null||t.classList.add(n)}function p(t,n){if(t instanceof NodeList)for(let e=0;e<t.length;e++)t[e].classList.remove(n);else t==null||t.classList.remove(n)}function o(t,n,e,i=!0){t!=null&&t.addEventListener&&t.addEventListener(n,e,i)}function _(t,n,e,i=!0){t!=null&&t.removeEventListener&&t.removeEventListener(n,e,i)}function dt(t,n){var e;(e=n==null?void 0:n.parentNode)==null||e.insertBefore(t,n)}function O(t){return t==null?void 0:t.previousElementSibling}function F(t){return t==null?void 0:t.nextElementSibling}function v(t){return t==null?void 0:t.parentElement}function pt(t){const n=document.createElement("div");for(let e=0,i=Object.keys(t),s=i.length;e<s;e++){const c=i[e],r=t[c];n.setAttribute(c,r)}return n}function $(){var t;(t=document.activeElement)==null||t.blur()}function yt(t){return u('[data-ui="#'+t+'"]')}function gt(t){return h('[data-ui="#'+t+'"]')}function ht(t){t.id&&a(t,"page")&&(t=gt(t.id)??t);const n=v(t);if(!a(n,"tabs")&&!a(n,"tabbed")&&!y(n,"nav"))return;const e=u("a",n);for(let i=0;i<e.length;i++)p(e[i],"active");!y(t,"button")&&!a(t,"button")&&!a(t,"chip")&&g(t,"active")}function X(t){t.placeholder||(t.placeholder=" ")}function vt(t){const n=t.currentTarget,e=v(n),i=h("input:not([type=file], [type=checkbox], [type=radio]), select, textarea",e);i&&i.focus()}function z(t){const n=t.currentTarget;L(n)}function N(t){const n=t.currentTarget;L(n)}function bt(t){const n=t.currentTarget;R(n)}function xt(t){const n=t.currentTarget;q(n)}function kt(t){const n=t.currentTarget;R(n,t)}function mt(t){const n=t.currentTarget;q(n,t)}function wt(t){const n=t.currentTarget;Y(n)}function Ct(t){var i;const n=t.currentTarget,e=h("input",v(n));e&&((i=n.textContent)!=null&&i.includes("visibility"))&&(e.type=e.type==="password"?"text":"password")}function Tt(){const t=u(".field > label");for(let n=0;n<t.length;n++)o(t[n],"click",vt)}function Lt(){const t=u(".field > input:not([type=file], [type=color], [type=range])");for(let n=0;n<t.length;n++)o(t[n],"focus",z),o(t[n],"blur",N),L(t[n])}function At(){const t=u(".field > select");for(let n=0;n<t.length;n++)o(t[n],"focus",z),o(t[n],"blur",N)}function Et(){const t=u(".field > input[type=file]");for(let n=0;n<t.length;n++)o(t[n],"change",bt),R(t[n])}function St(){const t=u(".field > input[type=color]");for(let n=0;n<t.length;n++)o(t[n],"change",xt),q(t[n])}function Dt(){const t=u(".field.textarea > textarea");for(let n=0;n<t.length;n++)o(t[n],"focus",z),o(t[n],"blur",N),o(t[n],"input",wt),Y(t[n])}function It(){const t=u("input[type=password] ~ :is(i, a)");for(let n=0;n<t.length;n++)o(t[n],"click",Ct)}function L(t){T(t,"number")&&!t.value&&(t.value=""),X(t)}function R(t,n){if((n==null?void 0:n.key)==="Enter"){const i=O(t);if(!T(i,"file"))return;i.click();return}const e=F(t);T(e,"text")&&(e.value=t.files?Array.from(t.files).map(i=>i.name).join(", "):"",e.readOnly=!0,o(e,"keydown",kt,!1),L(e))}function q(t,n){if((n==null?void 0:n.key)==="Enter"){const i=O(t);if(!T(i,"color"))return;i.click();return}const e=F(t);T(e,"text")&&(e.readOnly=!0,e.value=t.value,o(e,"keydown",mt,!1),L(e))}function Y(t){X(t);const n=v(t);n.removeAttribute("style"),a(n,"min")&&n.style.setProperty("--_size",`${Math.min(192,Math.max(t.scrollHeight,n.offsetHeight))}px`)}function Mt(){Tt(),Lt(),At(),Et(),St(),Dt(),It()}function H(t){const n=t.target;!y(n,"input")&&!y(n,"select")||(n.type==="range"?(n.focus(),Q(n)):G())}function Pt(t){if(!S())return;const n=t.target,e=v(n);a(e,"vertical")&&document.body.classList.add("no-scroll")}function _t(t){if(!S())return;const n=t.target,e=v(n);a(e,"vertical")&&document.body.classList.remove("no-scroll")}function G(){const t=document.body,n=u(".slider > input[type=range]");n.length?o(t,"input",H,!1):_(t,"input",H,!1);for(let e=0;e<n.length;e++)Q(n[e])}function Ot(){const t=getComputedStyle(document.documentElement).getPropertyValue("--size")||"16px";return t.includes("%")?parseInt(t)*16/100:t.includes("em")?parseInt(t)*16:parseInt(t)}function Q(t){o(t,"focus",Pt),o(t,"blur",_t);const n=v(t),e=h("span",n),i=u("input",n);if(!i.length||!e)return;const s=Ot(),c=a(n,"max")?0:.25*s*100/i[0].offsetWidth,r=[],l=[];for(let x=0,at=i.length;x<at;x++){const j=parseFloat(i[x].min)||0,st=parseFloat(i[x].max)||100,K=parseFloat(i[x].value)||0,U=(K-j)*100/(st-j),ut=c/2-c*U/100;r.push(U+ut),l.push(K)}let f=r[0],b=0,A=100-b-f,k=l[0],w=l[1]||0;i.length>1&&(f=Math.abs(r[1]-r[0]),b=r[1]>r[0]?r[0]:r[1],A=100-b-f,w>k&&(k=l[1]||0,w=l[0])),n.style.setProperty("--_start",`${b}%`),n.style.setProperty("--_end",`${A}%`),n.style.setProperty("--_value1",`'${k}'`),n.style.setProperty("--_value2",`'${w}'`)}function Ft(){G()}const d={light:"",dark:""};function P(){var t;return(t=document==null?void 0:document.body)!=null&&t.classList.contains("dark")?"dark":"light"}function $t(){if(d.light&&d.dark)return d;const t=document.body,n=document.createElement("body");n.className="light",t.appendChild(n);const e=document.createElement("body");e.className="dark",t.appendChild(e);const i=getComputedStyle(n),s=getComputedStyle(e),c=["--primary","--on-primary","--primary-container","--on-primary-container","--secondary","--on-secondary","--secondary-container","--on-secondary-container","--tertiary","--on-tertiary","--tertiary-container","--on-tertiary-container","--error","--on-error","--error-container","--on-error-container","--background","--on-background","--surface","--on-surface","--surface-variant","--on-surface-variant","--outline","--outline-variant","--shadow","--scrim","--inverse-surface","--inverse-on-surface","--inverse-primary","--surface-dim","--surface-bright","--surface-container-lowest","--surface-container-low","--surface-container","--surface-container-high","--surface-container-highest"];for(let r=0,l=c.length;r<l;r++)d.light+=c[r]+":"+i.getPropertyValue(c[r])+";",d.dark+=c[r]+":"+s.getPropertyValue(c[r])+";";return t.removeChild(n),t.removeChild(e),d}function zt(t){const n=globalThis,e=document.body;if(!t||!n.materialDynamicColors)return $t();const i=P();return t.light&&t.dark?(d.light=t.light,d.dark=t.dark,e.setAttribute("style",t[i]),t):n.materialDynamicColors(t).then(s=>{const c=r=>{let l="";for(let f=0,b=Object.keys(r),A=b.length;f<A;f++){const k=b[f],w=r[k],x=k.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g,"$1-$2").toLowerCase();l+="--"+x+":"+w+";"}return l};return d.light=c(s.light),d.dark=c(s.dark),e.setAttribute("style",d[i]),d})}function J(t){const n=globalThis,e=document.body;if(!e)return t;if(!t)return P();t==="auto"&&(t=lt()?"dark":"light"),e.classList.remove("light","dark"),e.classList.add(t);const i=t==="light"?d.light:d.dark;return n.materialDynamicColors&&e.setAttribute("style",i),P()}const E=[];function tt(t){if(t.key==="Escape"){const n=t.currentTarget;et(n,n)}}function Nt(t){(h("[autofocus]",t)??t).focus()}function nt(t,n){p(yt(t.id),"active"),p(t,"active"),p(n,"active"),t.close(),E.pop();const e=E[E.length-1];e?e.focus():S()&&document.body.classList.remove("no-scroll")}async function Rt(t,n,e,i){!y(i,"button")&&!a(i,"button")&&!a(i,"chip")&&g(i,"active"),g(n,"active"),g(t,"active"),e?t.showModal():t.show(),await W(90),e||o(t,"keydown",tt,!1),E.push(t),S()&&document.body.classList.add("no-scroll"),Nt(t)}function qt(t){const n=t.currentTarget,e=F(n);y(e,"dialog")&&nt(e,n)}async function et(t,n){$();let e=O(n);const i=a(n,"active")||n.open,s=a(n,"modal");s||_(n,"keydown",tt,!1),a(e,"overlay")||(e=pt({class:"overlay"}),dt(e,n),await W(90)),s||o(e,"click",qt,!1),i?nt(n,e):Rt(n,e,s,t)}let D;function it(t){_(document.body,"click",it);const n=t.target,e=u("menu.active");for(let i=0;i<e.length;i++)ot(n,e[i],t)}function Bt(t){setTimeout(()=>{const n=h(".field > input",t);n?n.focus():t.focus()},90)}function ot(t,n,e){D&&clearTimeout(D),D=setTimeout(()=>{o(document.body,"click",it),y(document.activeElement,"input")||$();const i=a(n,"active"),s=(e==null?void 0:e.target)===t,c=!!t.closest("menu");if(!i&&c||i&&s){p(n,"active");return}p(u("menu.active"),"active"),g(n,"active"),Bt(n)},90)}let C;function jt(t){const n=t.currentTarget;p(n,"active"),C&&clearTimeout(C)}function Kt(t,n){$();const e=u(".snackbar.active");for(let i=0;i<e.length;i++)p(e[i],"active");g(t,"active"),o(t,"click",jt),C&&clearTimeout(C),n!==-1&&(C=setTimeout(()=>{p(t,"active")},n??6e3))}function Ut(t){const n=v(t);n&&p(u(":scope > .page",n),"active"),g(t,"active")}function Vt(t){Ht(t)}function Ht(t){const n=t.currentTarget,e=n.getBoundingClientRect(),i=Math.max(e.width,e.height),s=i/2,c=t.clientX-e.left-s,r=t.clientY-e.top-s,l=document.createElement("div");l.className="ripple-js";const f=document.createElement("div");f.style.inlineSize=f.style.blockSize=`${i}px`,f.style.left=`${c}px`,f.style.top=`${r}px`,f.addEventListener("animationend",()=>{l.remove()}),l.appendChild(f),n.appendChild(l)}function Zt(){const t=u(".slow-ripple, .ripple, .fast-ripple");for(let n=0;n<t.length;n++)o(t[n],"pointerdown",Vt)}const m=globalThis;let I,M;function Z(){I&&clearTimeout(I),I=setTimeout(async()=>await ct(),180)}async function B(t,n,e,i){if(!n&&(n=h(t.getAttribute("data-ui")),!n)){t.classList.toggle("active");return}if(ht(t),y(n,"dialog")){await et(t,n);return}if(y(n,"menu")){ot(t,n,i);return}if(a(n,"snackbar")){Kt(n,e);return}if(a(n,"page")){Ut(n);return}if(a(n,"active")){p(t,"active"),p(n,"active");return}g(n,"active")}function Wt(t){B(t.currentTarget,null,null,t)}function Xt(t){t.key==="Enter"&&B(t.currentTarget,null,null,t)}function rt(){m.ui||M||!m.MutationObserver||(M=new MutationObserver(Z),M.observe(document.body,{childList:!0,subtree:!0}),Z())}function Yt(){const t=u("[data-ui]");for(let n=0,e=t.length;n<e;n++)o(t[n],"click",Wt),y(t[n],"a")&&!t[n].getAttribute("href")&&o(t[n],"keydown",Xt)}function ct(t,n){if(t){if(t==="setup"){rt();return}if(t==="guid")return ft();if(t==="mode")return J(n);if(t==="theme")return zt(n);const e=h(t);if(!e)return;B(e,e,n)}Yt(),Mt(),Ft(),Zt()}function Gt(){var n;if(m.ui)return;const t=(n=m.document)==null?void 0:n.body;t&&!t.classList.contains("dark")&&!t.classList.contains("light")&&J("auto"),rt(),m.ui=ct}Gt();const Qt=m.ui;export{Qt as default,Qt as ui};
