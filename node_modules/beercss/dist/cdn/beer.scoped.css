:root {
  --scoped: 1;
  --size: 1rem;
  --font: <PERSON>, <PERSON><PERSON>, "Helvetica Neue", "Arial Nova", "Nimbus Sans", Noto Sans, Arial, sans-serif;
  --font-icon: "Material Symbols Outlined";
  --speed1: 0.1s;
  --speed2: 0.2s;
  --speed3: 0.3s;
  --speed4: 0.4s;
  --active: rgb(128 128 128 / 0.192);
  --overlay: rgb(0 0 0 / 0.5);
  --elevate1: 0 0.125rem 0.125rem 0 rgb(0 0 0 / 0.32);
  --elevate2: 0 0.25rem 0.5rem 0 rgb(0 0 0 / 0.4);
  --elevate3: 0 0.375rem 0.75rem 0 rgb(0 0 0 / 0.48);
  --top: env(safe-area-inset-top);
  --bottom: env(safe-area-inset-bottom);
  --left: env(safe-area-inset-left);
  --right: env(safe-area-inset-right);
}
:root,
body.light {
  --primary: #6750a4;
  --on-primary: #ffffff;
  --primary-container: #e9ddff;
  --on-primary-container: #22005d;
  --secondary: #625b71;
  --on-secondary: #ffffff;
  --secondary-container: #e8def8;
  --on-secondary-container: #1e192b;
  --tertiary: #7e5260;
  --on-tertiary: #ffffff;
  --tertiary-container: #ffd9e3;
  --on-tertiary-container: #31101d;
  --error: #ba1a1a;
  --on-error: #ffffff;
  --error-container: #ffdad6;
  --on-error-container: #410002;
  --background: #fffbff;
  --on-background: #1c1b1e;
  --surface: #fdf8fd;
  --on-surface: #1c1b1e;
  --surface-variant: #e7e0eb;
  --on-surface-variant: #49454e;
  --outline: #7a757f;
  --outline-variant: #cac4cf;
  --shadow: #000000;
  --scrim: #000000;
  --inverse-surface: #313033;
  --inverse-on-surface: #f4eff4;
  --inverse-primary: #cfbcff;
  --surface-dim: #ddd8dd;
  --surface-bright: #fdf8fd;
  --surface-container-lowest: #ffffff;
  --surface-container-low: #f7f2f7;
  --surface-container: #f2ecf1;
  --surface-container-high: #ece7eb;
  --surface-container-highest: #e6e1e6;
}
body.dark {
  --primary: #cfbcff;
  --on-primary: #381e72;
  --primary-container: #4f378a;
  --on-primary-container: #e9ddff;
  --secondary: #cbc2db;
  --on-secondary: #332d41;
  --secondary-container: #4a4458;
  --on-secondary-container: #e8def8;
  --tertiary: #efb8c8;
  --on-tertiary: #4a2532;
  --tertiary-container: #633b48;
  --on-tertiary-container: #ffd9e3;
  --error: #ffb4ab;
  --on-error: #690005;
  --error-container: #93000a;
  --on-error-container: #ffb4ab;
  --background: #1c1b1e;
  --on-background: #e6e1e6;
  --surface: #141316;
  --on-surface: #e6e1e6;
  --surface-variant: #49454e;
  --on-surface-variant: #cac4cf;
  --outline: #948f99;
  --outline-variant: #49454e;
  --shadow: #000000;
  --scrim: #000000;
  --inverse-surface: #e6e1e6;
  --inverse-on-surface: #313033;
  --inverse-primary: #6750a4;
  --surface-dim: #141316;
  --surface-bright: #3a383c;
  --surface-container-lowest: #0f0e11;
  --surface-container-low: #1c1b1e;
  --surface-container: #201f22;
  --surface-container-high: #2b292d;
  --surface-container-highest: #363438;
}
/* outlined icons */
@font-face {
  font-family: "Material Symbols Outlined";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("material-symbols-outlined.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-outlined.woff2") format("woff2");
}
/* rounded icons */
@font-face {
  font-family: "Material Symbols Rounded";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("material-symbols-rounded.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-rounded.woff2") format("woff2");
}
/* sharp icons */
@font-face {
  font-family: "Material Symbols Sharp";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("material-symbols-sharp.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-sharp.woff2") format("woff2");
}
/* subset of only required icons */
@font-face {
  font-family: "Material Symbols Subset";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src:
    url("material-symbols-subset.woff2") format("woff2"),
    url("https://cdn.jsdelivr.net/npm/beercss@3.11.11/dist/cdn/material-symbols-subset.woff2") format("woff2");
}
.beer * {
  -webkit-tap-highlight-color: transparent;
  position: relative;
  vertical-align: middle;
  color: inherit;
  margin: 0;
  padding: 0;
  border-radius: inherit;
  box-sizing: border-box;
}
body {
  color: var(--on-surface);
  background-color: var(--surface);
  overflow-x: hidden;
}
.beer label {
  font-size: 0.75rem;
  vertical-align: baseline;
}
.beer a,
.beer b,
.beer i,
.beer span,
.beer strong,
.beer em,
.beer code {
  vertical-align: baseline;
}
.beer a,
.beer button,
.beer .button {
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  border: none;
  font-family: inherit;
  outline: inherit;
  justify-content: center;
}
.beer a,
.beer button,
.beer .button,
.beer i,
.beer label {
  user-select: none;
}
body ::-webkit-scrollbar,
body ::-webkit-scrollbar-thumb,
body ::-webkit-scrollbar-button {
  background: none;
  inline-size: 0.4rem;
  block-size: 0.4rem;
}
body :is(:hover,:focus)::-webkit-scrollbar-thumb {
  background: var(--outline);
  border-radius: 1rem;
}
.beer * + :is(address, article, blockquote, code, .field, fieldset, form, .grid, h1, h2, h3, h4, h5, h6, nav, ol, p, pre, .row, section, aside, table, .tabs, ul) {
  margin-block-start: 1rem;
}
.beer :is(a, button, .button, .chip):focus-visible {
  outline: 0.125rem solid var(--primary);
  outline-offset: 0.25rem;
}
.beer :is(nav, .row, li).group > :focus-visible {
  z-index: 1;
}
.beer .transparent {
  background-color: transparent !important;
  box-shadow: none !important;
  color: inherit !important;
}
.beer .primary {
  background-color: var(--primary) !important;
  color: var(--on-primary) !important;
}
.beer .primary-text {
  color: var(--primary) !important;
}
.beer .primary-border {
  border-color: var(--primary) !important;
}
.beer .primary-container {
  background-color: var(--primary-container) !important;
  color: var(--on-primary-container) !important;
}
.beer .secondary {
  background-color: var(--secondary) !important;
  color: var(--on-secondary) !important;
}
.beer .secondary-text {
  color: var(--secondary) !important;
}
.beer .secondary-border {
  border-color: var(--secondary) !important;
}
.beer .secondary-container {
  background-color: var(--secondary-container) !important;
  color: var(--on-secondary-container) !important;
}
.beer .tertiary {
  background-color: var(--tertiary) !important;
  color: var(--on-tertiary) !important;
}
.beer .tertiary-text {
  color: var(--tertiary) !important;
}
.beer .tertiary-border {
  border-color: var(--tertiary) !important;
}
.beer .tertiary-container {
  background-color: var(--tertiary-container) !important;
  color: var(--on-tertiary-container) !important;
}
.beer .error {
  background-color: var(--error) !important;
  color: var(--on-error) !important;
}
.beer .error-text {
  color: var(--error) !important;
}
.beer .error-border {
  border-color: var(--error) !important;
}
.beer .error-container {
  background-color: var(--error-container) !important;
  color: var(--on-error-container) !important;
}
.beer .background {
  background-color: var(--background) !important;
  color: var(--on-background) !important;
}
.beer .surface,
.beer .surface-dim,
.beer .surface-bright,
.beer .surface-container-lowest,
.beer .surface-container-low,
.beer .surface-container,
.beer .surface-container-high,
.beer .surface-container-highest {
  background-color: var(--surface) !important;
  color: var(--on-surface) !important;
}
.beer .surface-variant {
  background-color: var(--surface-variant) !important;
  color: var(--on-surface-variant) !important;
}
.beer .inverse-surface {
  background-color: var(--inverse-surface);
  color: var(--inverse-on-surface);
}
.beer .inverse-primary {
  background-color: var(--inverse-primary);
  color: var(--primary);
}
.beer .inverse-primary-text {
  color: var(--inverse-primary) !important;
}
.beer .inverse-primary-border {
  border-color: var(--inverse-primary) !important;
}
.beer .surface-dim {
  background-color: var(--surface-dim) !important;
}
.beer .surface-bright {
  background-color: var(--surface-bright) !important;
}
.beer .surface-container-lowest {
  background-color: var(--surface-container-lowest) !important;
}
.beer .surface-container-low {
  background-color: var(--surface-container-low) !important;
}
.beer .surface-container {
  background-color: var(--surface-container) !important;
}
.beer .surface-container-high {
  background-color: var(--surface-container-high) !important;
}
.beer .surface-container-highest {
  background-color: var(--surface-container-highest) !important;
}
.beer .surface-container-low {
  background-color: var(--surface-container-low) !important;
}
.beer .black {
  background-color: #000 !important;
}
.beer .black-border {
  border-color: #000 !important;
}
.beer .black-text {
  color: #000 !important;
}
.beer .white {
  background-color: #FFF !important;
}
.beer .white-border {
  border-color: #FFF !important;
}
.beer .white-text {
  color: #FFF !important;
}
.beer .transparent-border {
  border-color: transparent !important;
}
.beer .transparent-text {
  color: transparent !important;
}
.beer .fill:not(i) {
  background-color: var(--surface-variant) !important;
  color: var(--on-surface-variant) !important;
}
.beer .middle-align {
  display: flex;
  align-items: center !important;
}
.beer .bottom-align {
  display: flex;
  align-items: flex-end !important;
}
.beer .top-align {
  display: flex;
  align-items: flex-start !important;
}
.beer .left-align {
  text-align: start;
  justify-content: flex-start !important;
}
.beer .right-align {
  text-align: end;
  justify-content: flex-end !important;
}
.beer .center-align {
  text-align: center;
  justify-content: center !important;
}
.beer [class*=blur],
.beer [class*=blur].light {
  --_blur: 1rem;
  -webkit-backdrop-filter: blur(var(--_blur));
  backdrop-filter: blur(var(--_blur));
  color: var(--on-surface);
  background-color: rgb(255 255 255 / 0.5) !important;
}
.beer .dark [class*=blur],
.beer [class*=blur].dark {
  background-color: rgb(0 0 0 / 0.5) !important;
}
.beer .small-blur {
  --_blur: 0.5rem;
}
.beer .large-blur {
  --_blur: 1.5rem;
}
.beer .red,
.beer .red6 {
  background-color: #F44336 !important;
}
.beer .red-border {
  border-color: #F44336 !important;
}
.beer .red-text {
  color: #F44336 !important;
}
.beer .red1 {
  background-color: #FFEBEE !important;
}
.beer .red2 {
  background-color: #FFCDD2 !important;
}
.beer .red3 {
  background-color: #EF9A9A !important;
}
.beer .red4 {
  background-color: #E57373 !important;
}
.beer .red5 {
  background-color: #EF5350 !important;
}
.beer .red7 {
  background-color: #E53935 !important;
}
.beer .red8 {
  background-color: #D32F2F !important;
}
.beer .red9 {
  background-color: #C62828 !important;
}
.beer .red10 {
  background-color: #B71C1C !important;
}
.beer .pink,
.beer .pink6 {
  background-color: #E91E63 !important;
}
.beer .pink-border {
  border-color: #E91E63 !important;
}
.beer .pink-text {
  color: #E91E63 !important;
}
.beer .pink1 {
  background-color: #FCE4EC !important;
}
.beer .pink2 {
  background-color: #F8BBD0 !important;
}
.beer .pink3 {
  background-color: #F48FB1 !important;
}
.beer .pink4 {
  background-color: #F06292 !important;
}
.beer .pink5 {
  background-color: #EC407A !important;
}
.beer .pink7 {
  background-color: #D81B60 !important;
}
.beer .pink8 {
  background-color: #C2185B !important;
}
.beer .pink9 {
  background-color: #AD1457 !important;
}
.beer .pink10 {
  background-color: #880E4F !important;
}
.beer .purple,
.beer .purple6 {
  background-color: #9C27B0 !important;
}
.beer .purple-border {
  border-color: #9C27B0 !important;
}
.beer .purple-text {
  color: #9C27B0 !important;
}
.beer .purple1 {
  background-color: #F3E5F5 !important;
}
.beer .purple2 {
  background-color: #E1BEE7 !important;
}
.beer .purple3 {
  background-color: #CE93D8 !important;
}
.beer .purple4 {
  background-color: #BA68C8 !important;
}
.beer .purple5 {
  background-color: #AB47BC !important;
}
.beer .purple7 {
  background-color: #8E24AA !important;
}
.beer .purple8 {
  background-color: #7B1FA2 !important;
}
.beer .purple9 {
  background-color: #6A1B9A !important;
}
.beer .purple10 {
  background-color: #4A148C !important;
}
.beer .deep-purple,
.beer .deep-purple6 {
  background-color: #673AB7 !important;
}
.beer .deep-purple-border {
  border-color: #673AB7 !important;
}
.beer .deep-purple-text {
  color: #673AB7 !important;
}
.beer .deep-purple1 {
  background-color: #EDE7F6 !important;
}
.beer .deep-purple2 {
  background-color: #D1C4E9 !important;
}
.beer .deep-purple3 {
  background-color: #B39DDB !important;
}
.beer .deep-purple4 {
  background-color: #9575CD !important;
}
.beer .deep-purple5 {
  background-color: #7E57C2 !important;
}
.beer .deep-purple7 {
  background-color: #5E35B1 !important;
}
.beer .deep-purple8 {
  background-color: #512DA8 !important;
}
.beer .deep-purple9 {
  background-color: #4527A0 !important;
}
.beer .deep-purple10 {
  background-color: #311B92 !important;
}
.beer .indigo,
.beer .indigo6 {
  background-color: #3F51B5 !important;
}
.beer .indigo-border {
  border-color: #3F51B5 !important;
}
.beer .indigo-text {
  color: #3F51B5 !important;
}
.beer .indigo1 {
  background-color: #E8EAF6 !important;
}
.beer .indigo2 {
  background-color: #C5CAE9 !important;
}
.beer .indigo3 {
  background-color: #9FA8DA !important;
}
.beer .indigo4 {
  background-color: #7986CB !important;
}
.beer .indigo5 {
  background-color: #5C6BC0 !important;
}
.beer .indigo7 {
  background-color: #3949AB !important;
}
.beer .indigo8 {
  background-color: #303F9F !important;
}
.beer .indigo9 {
  background-color: #283593 !important;
}
.beer .indigo10 {
  background-color: #1A237E !important;
}
.beer .blue,
.beer .blue6 {
  background-color: #2196F3 !important;
}
.beer .blue-border {
  border-color: #2196F3 !important;
}
.beer .blue-text {
  color: #2196F3 !important;
}
.beer .blue1 {
  background-color: #E3F2FD !important;
}
.beer .blue2 {
  background-color: #BBDEFB !important;
}
.beer .blue3 {
  background-color: #90CAF9 !important;
}
.beer .blue4 {
  background-color: #64B5F6 !important;
}
.beer .blue5 {
  background-color: #42A5F5 !important;
}
.beer .blue7 {
  background-color: #1E88E5 !important;
}
.beer .blue8 {
  background-color: #1976D2 !important;
}
.beer .blue9 {
  background-color: #1565C0 !important;
}
.beer .blue10 {
  background-color: #0D47A1 !important;
}
.beer .light-blue,
.beer .light-blue6 {
  background-color: #03A9F4 !important;
}
.beer .light-blue-border {
  border-color: #03A9F4 !important;
}
.beer .light-blue-text {
  color: #03A9F4 !important;
}
.beer .light-blue1 {
  background-color: #E1F5FE !important;
}
.beer .light-blue2 {
  background-color: #B3E5FC !important;
}
.beer .light-blue3 {
  background-color: #81D4FA !important;
}
.beer .light-blue4 {
  background-color: #4FC3F7 !important;
}
.beer .light-blue5 {
  background-color: #29B6F6 !important;
}
.beer .light-blue7 {
  background-color: #039BE5 !important;
}
.beer .light-blue8 {
  background-color: #0288D1 !important;
}
.beer .light-blue9 {
  background-color: #0277BD !important;
}
.beer .light-blue10 {
  background-color: #01579B !important;
}
.beer .cyan,
.beer .cyan6 {
  background-color: #00BCD4 !important;
}
.beer .cyan-border {
  border-color: #00BCD4 !important;
}
.beer .cyan-text {
  color: #00BCD4 !important;
}
.beer .cyan1 {
  background-color: #E0F7FA !important;
}
.beer .cyan2 {
  background-color: #B2EBF2 !important;
}
.beer .cyan3 {
  background-color: #80DEEA !important;
}
.beer .cyan4 {
  background-color: #4DD0E1 !important;
}
.beer .cyan5 {
  background-color: #26C6DA !important;
}
.beer .cyan7 {
  background-color: #00ACC1 !important;
}
.beer .cyan8 {
  background-color: #0097A7 !important;
}
.beer .cyan9 {
  background-color: #00838F !important;
}
.beer .cyan10 {
  background-color: #006064 !important;
}
.beer .teal,
.beer .teal6 {
  background-color: #009688 !important;
}
.beer .teal-border {
  border-color: #009688 !important;
}
.beer .teal-text {
  color: #009688 !important;
}
.beer .teal1 {
  background-color: #E0F2F1 !important;
}
.beer .teal2 {
  background-color: #B2DFDB !important;
}
.beer .teal3 {
  background-color: #80CBC4 !important;
}
.beer .teal4 {
  background-color: #4DB6AC !important;
}
.beer .teal5 {
  background-color: #26A69A !important;
}
.beer .teal7 {
  background-color: #00897B !important;
}
.beer .teal8 {
  background-color: #00796B !important;
}
.beer .teal9 {
  background-color: #00695C !important;
}
.beer .teal10 {
  background-color: #004D40 !important;
}
.beer .green,
.beer .green6 {
  background-color: #4CAF50 !important;
}
.beer .green-border {
  border-color: #4CAF50 !important;
}
.beer .green-text {
  color: #4CAF50 !important;
}
.beer .green1 {
  background-color: #E8F5E9 !important;
}
.beer .green2 {
  background-color: #C8E6C9 !important;
}
.beer .green3 {
  background-color: #A5D6A7 !important;
}
.beer .green4 {
  background-color: #81C784 !important;
}
.beer .green5 {
  background-color: #66BB6A !important;
}
.beer .green7 {
  background-color: #43A047 !important;
}
.beer .green8 {
  background-color: #388E3C !important;
}
.beer .green9 {
  background-color: #2E7D32 !important;
}
.beer .green10 {
  background-color: #1B5E20 !important;
}
.beer .light-green,
.beer .light-green6 {
  background-color: #8BC34A !important;
}
.beer .light-green-border {
  border-color: #8BC34A !important;
}
.beer .light-green-text {
  color: #8BC34A !important;
}
.beer .light-green1 {
  background-color: #F1F8E9 !important;
}
.beer .light-green2 {
  background-color: #DCEDC8 !important;
}
.beer .light-green3 {
  background-color: #C5E1A5 !important;
}
.beer .light-green4 {
  background-color: #AED581 !important;
}
.beer .light-green5 {
  background-color: #9CCC65 !important;
}
.beer .light-green7 {
  background-color: #7CB342 !important;
}
.beer .light-green8 {
  background-color: #689F38 !important;
}
.beer .light-green9 {
  background-color: #558B2F !important;
}
.beer .light-green10 {
  background-color: #33691E !important;
}
.beer .lime,
.beer .lime6 {
  background-color: #CDDC39 !important;
}
.beer .lime-border {
  border-color: #CDDC39 !important;
}
.beer .lime-text {
  color: #CDDC39 !important;
}
.beer .lime1 {
  background-color: #F9FBE7 !important;
}
.beer .lime2 {
  background-color: #F0F4C3 !important;
}
.beer .lime3 {
  background-color: #E6EE9C !important;
}
.beer .lime4 {
  background-color: #DCE775 !important;
}
.beer .lime5 {
  background-color: #D4E157 !important;
}
.beer .lime7 {
  background-color: #C0CA33 !important;
}
.beer .lime8 {
  background-color: #AFB42B !important;
}
.beer .lime9 {
  background-color: #9E9D24 !important;
}
.beer .lime10 {
  background-color: #827717 !important;
}
.beer .yellow,
.beer .yellow6 {
  background-color: #FFEB3B !important;
}
.beer .yellow-border {
  border-color: #FFEB3B !important;
}
.beer .yellow-text {
  color: #FFEB3B !important;
}
.beer .yellow1 {
  background-color: #FFFDE7 !important;
}
.beer .yellow2 {
  background-color: #FFF9C4 !important;
}
.beer .yellow3 {
  background-color: #FFF59D !important;
}
.beer .yellow4 {
  background-color: #FFF176 !important;
}
.beer .yellow5 {
  background-color: #FFEE58 !important;
}
.beer .yellow7 {
  background-color: #FDD835 !important;
}
.beer .yellow8 {
  background-color: #FBC02D !important;
}
.beer .yellow9 {
  background-color: #F9A825 !important;
}
.beer .yellow10 {
  background-color: #F57F17 !important;
}
.beer .amber,
.beer .amber6 {
  background-color: #FFC107 !important;
}
.beer .amber-border {
  border-color: #FFC107 !important;
}
.beer .amber-text {
  color: #FFC107 !important;
}
.beer .amber1 {
  background-color: #FFF8E1 !important;
}
.beer .amber2 {
  background-color: #FFECB3 !important;
}
.beer .amber3 {
  background-color: #FFE082 !important;
}
.beer .amber4 {
  background-color: #FFD54F !important;
}
.beer .amber5 {
  background-color: #FFCA28 !important;
}
.beer .amber7 {
  background-color: #FFB300 !important;
}
.beer .amber8 {
  background-color: #FFA000 !important;
}
.beer .amber9 {
  background-color: #FF8F00 !important;
}
.beer .amber10 {
  background-color: #FF6F00 !important;
}
.beer .orange,
.beer .orange6 {
  background-color: #FF9800 !important;
}
.beer .orange-border {
  border-color: #FF9800 !important;
}
.beer .orange-text {
  color: #FF9800 !important;
}
.beer .orange1 {
  background-color: #FFF3E0 !important;
}
.beer .orange2 {
  background-color: #FFE0B2 !important;
}
.beer .orange3 {
  background-color: #FFCC80 !important;
}
.beer .orange4 {
  background-color: #FFB74D !important;
}
.beer .orange5 {
  background-color: #FFA726 !important;
}
.beer .orange7 {
  background-color: #FB8C00 !important;
}
.beer .orange8 {
  background-color: #F57C00 !important;
}
.beer .orange9 {
  background-color: #EF6C00 !important;
}
.beer .orange10 {
  background-color: #E65100 !important;
}
.beer .deep-orange,
.beer .deep-orange6 {
  background-color: #FF5722 !important;
}
.beer .deep-orange-border {
  border-color: #FF5722 !important;
}
.beer .deep-orange-text {
  color: #FF5722 !important;
}
.beer .deep-orange1 {
  background-color: #FBE9E7 !important;
}
.beer .deep-orange2 {
  background-color: #FFCCBC !important;
}
.beer .deep-orange3 {
  background-color: #FFAB91 !important;
}
.beer .deep-orange4 {
  background-color: #FF8A65 !important;
}
.beer .deep-orange5 {
  background-color: #FF7043 !important;
}
.beer .deep-orange7 {
  background-color: #F4511E !important;
}
.beer .deep-orange8 {
  background-color: #E64A19 !important;
}
.beer .deep-orange9 {
  background-color: #D84315 !important;
}
.beer .deep-orange10 {
  background-color: #BF360C !important;
}
.beer .brown,
.beer .brown6 {
  background-color: #795548 !important;
}
.beer .brown-border {
  border-color: #795548 !important;
}
.beer .brown-text {
  color: #795548 !important;
}
.beer .brown1 {
  background-color: #EFEBE9 !important;
}
.beer .brown2 {
  background-color: #D7CCC8 !important;
}
.beer .brown3 {
  background-color: #BCAAA4 !important;
}
.beer .brown4 {
  background-color: #A1887F !important;
}
.beer .brown5 {
  background-color: #8D6E63 !important;
}
.beer .brown7 {
  background-color: #6D4C41 !important;
}
.beer .brown8 {
  background-color: #5D4037 !important;
}
.beer .brown9 {
  background-color: #4E342E !important;
}
.beer .brown10 {
  background-color: #3E2723 !important;
}
.beer .blue-grey,
.beer .blue-grey6 {
  background-color: #607D8B !important;
}
.beer .blue-grey-border {
  border-color: #607D8B !important;
}
.beer .blue-grey-text {
  color: #607D8B !important;
}
.beer .blue-grey1 {
  background-color: #ECEFF1 !important;
}
.beer .blue-grey2 {
  background-color: #CFD8DC !important;
}
.beer .blue-grey3 {
  background-color: #B0BEC5 !important;
}
.beer .blue-grey4 {
  background-color: #90A4AE !important;
}
.beer .blue-grey5 {
  background-color: #78909C !important;
}
.beer .blue-grey7 {
  background-color: #546E7A !important;
}
.beer .blue-grey8 {
  background-color: #455A64 !important;
}
.beer .blue-grey9 {
  background-color: #37474F !important;
}
.beer .blue-grey10 {
  background-color: #263238 !important;
}
.beer .grey,
.beer .grey6 {
  background-color: #9E9E9E !important;
}
.beer .grey-border {
  border-color: #9E9E9E !important;
}
.beer .grey-text {
  color: #9E9E9E !important;
}
.beer .grey1 {
  background-color: #FAFAFA !important;
}
.beer .grey2 {
  background-color: #F5F5F5 !important;
}
.beer .grey3 {
  background-color: #EEE !important;
}
.beer .grey4 {
  background-color: #E0E0E0 !important;
}
.beer .grey5 {
  background-color: #BDBDBD !important;
}
.beer .grey7 {
  background-color: #757575 !important;
}
.beer .grey8 {
  background-color: #616161 !important;
}
.beer .grey9 {
  background-color: #424242 !important;
}
.beer .grey10 {
  background-color: #212121 !important;
}
.beer .horizontal {
  display: inline-flex;
  flex-direction: row !important;
  gap: 1rem;
  inline-size: auto !important;
  max-inline-size: none !important;
}
.beer .horizontal > * {
  margin-block: 0 !important;
}
.beer .vertical {
  display: flex;
  flex-direction: column !important;
}
.beer :is(a, button, .button, .chip).vertical {
  display: inline-flex;
  gap: 0.25rem;
  block-size: auto !important;
  max-block-size: none !important;
  padding-block: 0.5rem;
}
.beer .vertical > * {
  margin-inline: 0 !important;
}
.beer .no-elevate {
  box-shadow: none !important;
}
.beer .small-elevate,
.beer .elevate {
  box-shadow: var(--elevate1) !important;
}
.beer .medium-elevate {
  box-shadow: var(--elevate2) !important;
}
.beer .large-elevate {
  box-shadow: var(--elevate3) !important;
}
.beer [class*=round] {
  --_round: 2rem;
  border-radius: var(--_round) !important;
}
.beer .small-round {
  --_round: 0.5rem;
}
.beer .large-round {
  --_round: 3.5rem;
}
.beer .no-round,
.beer .square,
.beer .top-round,
.beer .bottom-round,
.beer .left-round,
.beer .right-round {
  border-radius: 0.5rem !important;
}
.beer .top-round {
  border-start-start-radius: var(--_round) !important;
  border-start-end-radius: var(--_round) !important;
}
.beer .bottom-round {
  border-end-end-radius: var(--_round) !important;
  border-end-start-radius: var(--_round) !important;
}
.beer .left-round {
  border-start-start-radius: var(--_round) !important;
  border-end-start-radius: var(--_round) !important;
}
.beer .right-round {
  border-start-end-radius: var(--_round) !important;
  border-end-end-radius: var(--_round) !important;
}
.beer .circle:not(.extend) {
  border-radius: 50%;
}
.beer :is(.circle, .square):is(button, .button, .chip) {
  padding: 0;
  block-size: var(--_size);
  inline-size: var(--_size);
}
.beer :is(.circle, .square) > span {
  display: none;
}
.beer :is(.circle, .square).round {
  border-radius: 1rem !important;
}
.beer .border:not(table, .field, .list, menu, article) {
  box-sizing: border-box;
  border: 0.0625rem solid var(--outline);
  background-color: transparent;
  box-shadow: none;
}
.beer .no-border {
  border-color: transparent !important;
}
.beer .border:not(.extend, .circle, .square, .badge) {
  box-sizing: content-box;
}
.beer [class*=margin]:not(.left-margin, .right-margin, .top-margin, .bottom-margin, .horizontal-margin, .vertical-margin) {
  margin: var(--_margin) !important;
}
.beer [class*=margin] {
  --_margin: 1rem;
}
.beer .no-margin {
  --_margin: 0;
}
.beer .auto-margin {
  --_margin: auto;
}
.beer .tiny-margin {
  --_margin: 0.25rem;
}
.beer .small-margin {
  --_margin: 0.5rem;
}
.beer .large-margin {
  --_margin: 1.5rem;
}
.beer .left-margin,
.beer .horizontal-margin {
  margin-inline-start: var(--_margin) !important;
}
.beer .right-margin,
.beer .horizontal-margin {
  margin-inline-end: var(--_margin) !important;
}
.beer .top-margin,
.beer .vertical-margin {
  margin-block-start: var(--_margin) !important;
}
.beer .bottom-margin,
.beer .vertical-margin {
  margin-block-end: var(--_margin) !important;
}
.beer .no-opacity {
  opacity: 1 !important;
}
.beer .opacity {
  opacity: 0 !important;
}
.beer .small-opacity {
  opacity: 0.75 !important;
}
.beer .medium-opacity {
  opacity: 0.5 !important;
}
.beer .large-opacity {
  opacity: 0.25 !important;
}
.beer [class*=padding]:not(.left-padding, .right-padding, .top-padding, .bottom-padding, .horizontal-padding, .vertical-padding) {
  padding: var(--_padding) !important;
}
.beer [class*=padding] {
  --_padding: 1rem;
}
.beer .no-padding {
  --_padding: 0 !important;
}
.beer .tiny-padding {
  --_padding: 0.25rem !important;
}
.beer .small-padding {
  --_padding: 0.5rem !important;
}
.beer .large-padding {
  --_padding: 1.5rem !important;
}
.beer .left-padding,
.beer .horizontal-padding {
  padding-inline-start: var(--_padding) !important;
}
.beer .right-padding,
.beer .horizontal-padding {
  padding-inline-end: var(--_padding) !important;
}
.beer .top-padding,
.beer .vertical-padding {
  padding-block-start: var(--_padding) !important;
}
.beer .bottom-padding,
.beer .vertical-padding {
  padding-block-end: var(--_padding) !important;
}
.beer .front {
  z-index: 10 !important;
}
.beer .back {
  z-index: -10 !important;
}
.beer .left {
  inset-inline-start: 0;
}
.beer .right {
  inset-inline-end: 0;
}
.beer .top {
  inset-block-start: 0;
}
.beer .bottom {
  inset-block-end: 0;
}
.beer .center {
  inset-inline-start: 50%;
  transform: translateX(-50%);
}
.beer [dir=rtl] .center {
  transform: translateX(50%);
}
.beer .middle {
  inset-block-start: 50%;
  transform: translateY(-50%);
}
.beer .middle.center {
  transform: translate(-50%, -50%);
}
.beer [dir=rtl] .middle.center {
  transform: translate(50%, -50%);
}
.beer .ripple {
  --_duration: 600ms;
}
.beer .fast-ripple {
  --_duration: 200ms;
}
.beer .slow-ripple {
  --_duration: 1800ms;
}
.beer .ripple-js {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
}
.beer .ripple-js > div {
  position: absolute;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.3;
  transform: scale(0);
  animation: to-ripple var(--_duration) linear;
}
@keyframes to-ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
.beer .scroll {
  overflow: auto;
}
.beer .no-scroll {
  overflow: hidden;
}
.beer .shadow {
  background-color: #00000050;
}
.beer :is(.left-shadow, .right-shadow, .top-shadow, .bottom-shadow) {
  background-color: transparent !important;
}
.beer .left-shadow {
  background-image: linear-gradient(to right, black, transparent) !important;
}
.beer .right-shadow {
  background-image: linear-gradient(to left, black, transparent) !important;
}
.beer .bottom-shadow {
  background-image: linear-gradient(to top, black, transparent) !important;
}
.beer .top-shadow {
  background-image: linear-gradient(to bottom, black, transparent) !important;
}
.beer [class*=width] {
  max-inline-size: 100%;
}
.beer .auto-width {
  inline-size: auto;
}
.beer .small-width {
  inline-size: 12rem !important;
}
.beer .medium-width {
  inline-size: 24rem !important;
}
.beer .large-width {
  inline-size: 36rem !important;
}
.beer .auto-height {
  block-size: auto;
}
.beer .small-height {
  block-size: 12rem !important;
}
.beer .medium-height {
  block-size: 24rem !important;
}
.beer .large-height {
  block-size: 36rem !important;
}
.beer .wrap {
  display: block;
  white-space: normal;
}
.beer .no-wrap:not(menu) {
  display: flex;
  white-space: nowrap;
}
.beer .tiny-space:not(nav, .row, .grid, table, .tooltip, .list, menu) {
  block-size: 0.5rem;
}
.beer :is(.space, .small-space):not(nav, .row, .grid, table, .tooltip, .list, menu) {
  block-size: 1rem;
}
.beer .medium-space:not(nav, .row, .grid, table, .tooltip, .list, menu) {
  block-size: 2rem;
}
.beer .large-space:not(nav, .row, .grid, table, .tooltip, .list, menu) {
  block-size: 3rem;
}
.beer .responsive {
  inline-size: -webkit-fill-available;
  inline-size: -moz-available;
}
@media only screen and (max-width: 600px) {
  .beer :is(.m, .l):not(.s) {
    display: none !important;
  }
}
@media only screen and (min-width: 601px) and (max-width: 992px) {
  .beer :is(.s, .l):not(.m) {
    display: none !important;
  }
}
@media only screen and (min-width: 993px) {
  .beer :is(.m, .s):not(.l) {
    display: none !important;
  }
}
html {
  font-size: var(--size);
}
body {
  font-family: var(--font);
  font-size: 0.875rem;
  line-height: 1.5rem;
  letter-spacing: 0.0313rem;
}
.beer h1,
.beer h2,
.beer h3,
.beer h4,
.beer h5,
.beer h6 {
  font-weight: 400;
  display: block;
  align-items: center;
  line-height: normal;
}
.beer h1 {
  font-size: 3.5625rem;
}
.beer h2 {
  font-size: 2.8125rem;
}
.beer h3 {
  font-size: 2.25rem;
}
.beer h4 {
  font-size: 2rem;
}
.beer h5 {
  font-size: 1.75rem;
}
.beer h6 {
  font-size: 1.5rem;
}
.beer h1.small {
  font-size: 3.0625rem;
}
.beer h2.small {
  font-size: 2.3125rem;
}
.beer h3.small {
  font-size: 1.75rem;
}
.beer h4.small {
  font-size: 1.5rem;
}
.beer h5.small {
  font-size: 1.25rem;
}
.beer h6.small {
  font-size: 1rem;
}
.beer h1.large {
  font-size: 4.0625rem;
}
.beer h2.large {
  font-size: 3.3125rem;
}
.beer h3.large {
  font-size: 2.75rem;
}
.beer h4.large {
  font-size: 2.5rem;
}
.beer h5.large {
  font-size: 2.25rem;
}
.beer h6.large {
  font-size: 2rem;
}
.beer .link {
  color: var(--primary) !important;
}
.beer .inverse-link {
  color: var(--inverse-primary) !important;
}
.beer .truncate {
  overflow: hidden;
  white-space: nowrap !important;
  text-overflow: ellipsis;
  flex: inherit;
}
.beer .truncate > * {
  white-space: nowrap !important;
}
.beer .small-text {
  font-size: 0.75rem;
}
.beer .medium-text {
  font-size: 0.875rem;
}
.beer .large-text {
  font-size: 1rem;
}
.beer .upper {
  text-transform: uppercase;
}
.beer .lower {
  text-transform: lowercase;
}
.beer .capitalize {
  text-transform: capitalize;
}
.beer .bold {
  font-weight: bold;
}
.beer .overline {
  text-decoration: line-through;
}
.beer .underline {
  text-decoration: underline;
}
.beer .italic {
  font-style: italic;
}
.beer p {
  margin: 0.5rem 0;
}
.beer .no-line {
  line-height: normal;
}
.beer .tiny-line {
  line-height: 1.25rem;
}
.beer .small-line {
  line-height: 1.5rem;
}
.beer .medium-line {
  line-height: 1.75rem;
}
.beer .large-line {
  line-height: 2rem;
}
.beer .extra-line {
  line-height: 2.25rem;
}
.beer pre {
  border-radius: 0;
  background-color: var(--surface-container);
  white-space: pre-wrap;
  padding: 1rem;
  border-inline-start: 0.25rem solid var(--primary);
  font-family: inherit;
}
.beer blockquote {
  border-radius: 0;
  padding: 1rem;
  border-inline-start: 0.25rem solid var(--primary);
  font-family: inherit;
}
.beer code {
  border-radius: 0;
  background-color: var(--surface-container);
  white-space: pre-wrap;
  padding: 0.25rem;
}
.beer pre > code,
.beer blockquote > code {
  padding: 0;
}
.beer .scroll > code {
  white-space: pre;
}
.beer pre:has(> code){
  direction: ltr;
  text-align: start;
}
.beer :is(.wave, .chip, .button, button, nav.tabbed > a, .tabs > a, .toolbar > a):not(.slow-ripple, .ripple, .fast-ripple)::after {
  content: "";
  position: absolute;
  inset: 0;
  z-index: 1;
  border-radius: inherit;
  inline-size: 100%;
  block-size: 100%;
  background-position: center;
  background-image: radial-gradient(circle, currentColor 1%, transparent 1%);
  opacity: 0;
  transition: none;
}
.beer :is(.wave, .chip, .button, button, nav.tabbed > a, .tabs > a, .toolbar > a):not(.slow-ripple, .ripple, .fast-ripple):is(:focus-visible, :hover)::after {
  background-size: 15000%;
  opacity: 0.1;
  transition: background-size var(--speed2) linear;
}
.beer :is(.wave, .chip, .button, button, nav.tabbed > a, .tabs > a, .toolbar > a):not(.slow-ripple, .ripple, .fast-ripple):active::after {
  background-size: 5000%;
  opacity: 0;
  transition: none;
}
.beer .no-wave::after,
.beer .no-wave:is(:hover, :active)::after {
  display: none;
}
.beer .badge {
  --_x: 0;
  --_y: -100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  font-size: 0.6875rem;
  text-transform: none;
  z-index: 2;
  padding: 0 0.25rem;
  min-block-size: 1rem;
  min-inline-size: 1rem;
  background-color: var(--error);
  color: var(--on-error);
  line-height: normal;
  border-radius: 1rem;
  inset: 50% auto auto 50%;
  transform: translate(var(--_x, 50%), var(--_y, -50%));
  font-family: var(--font);
}
.beer .badge.top {
  --_y: -100%;
}
.beer .badge.bottom {
  --_y: 0;
}
.beer .badge.left {
  --_x: -100%;
}
.beer .badge.right {
  --_x: 0;
}
.beer .badge.border {
  border-color: var(--error);
  color: var(--error);
  background-color: var(--surface);
}
.beer .badge:is(.circle, .square) {
  text-align: center;
  inline-size: auto;
  block-size: auto;
  padding: 0 0.25rem;
  border-radius: 1rem;
}
.beer .badge.square {
  border-radius: 0;
}
.beer .badge.min > * {
  display: none;
}
.beer .badge.min {
  clip-path: circle(18.75% at 50% 50%);
}
.beer nav:is(.left, .right, .top, .bottom) > a > .badge,
.beer nav:is(.left, .right, .top, .bottom) > :is(ol, ul) > li > a > .badge {
  inset: 1rem auto auto 50%;
}
.beer .badge.none {
  inset: auto !important;
  transform: none;
  position: relative;
  margin: 0 0.125rem;
}
.beer :is(button, .button, .chip) > .badge.none {
  margin: 0 -0.5rem;
}
.beer header,
.beer footer {
  display: flex;
  justify-content: center;
  flex-direction: column;
  background-color: var(--surface-container);
  border-radius: 0;
  padding: 0 1rem;
}
.beer :is(nav.drawer, dialog, article) > :is(header, footer) {
  padding-inline: 0;
  inset: 0;
}
.beer header {
  min-block-size: 4rem;
}
.beer footer {
  min-block-size: 5rem;
}
.beer :is(header, footer, menu > *).fixed {
  position: sticky;
  inset: 0;
  z-index: 11;
  background-color: inherit;
}
.beer header.fixed {
  inset: calc(-1 * var(--_padding)) 0 0 0;
  margin-block-start: calc(-1 * var(--_padding));
}
.beer footer.fixed {
  inset: 0 0 calc(-1 * var(--_padding)) 0;
  margin-block-end: calc(-1 * var(--_padding));
}
.beer dialog > :is(header, footer) {
  background: none;
}
.beer dialog > header.fixed {
  background-color: inherit;
  padding: var(--top) 0 0 0;
  margin: calc(-1 * var(--top)) 0 0 0;
  transform: translateY(calc(-1 * (var(--top) + var(--_padding))));
}
.beer dialog > footer.fixed {
  background-color: inherit;
  padding: 0 0 var(--bottom) 0;
  margin: 0 0 calc(-1 * var(--bottom)) 0;
  transform: translateY(calc(var(--bottom) + var(--_padding)));
}
.beer :is(main, header, footer, section).responsive {
  max-inline-size: 75rem;
  margin: 0 auto;
}
.beer :is(main, header, footer, section).responsive.max {
  max-inline-size: 100%;
}
.beer:has(> main) > :is(header, footer).fixed {
  z-index: 12;
  transform: none;
  box-sizing: content-box;
  inset: 0;
}
.beer:has(> main) > header.fixed {
  padding-block-start: calc(var(--top) + var(--_top));
  margin-block-start: calc(-1 * var(--top) - var(--_top));
}
.beer:has(> main) > footer.fixed {
  padding-block-end: calc(var(--bottom) + var(--_bottom));
  margin-block-end: calc(-1 * var(--bottom) - var(--_bottom));
}
.beer :is(nav, .row) > header {
  background-color: inherit;
}
.beer nav:is(.left, .right) > header {
  transform: translateY(-0.5rem);
}
.beer nav.drawer:is(.left, .right) > header + * {
  margin-block-start: 0.5rem;
}
.beer dialog > nav.drawer > header + * {
  margin-block-start: 1rem;
}
.beer .button,
.beer button {
  --_padding: 1rem;
  --_size: 2.5rem;
  box-sizing: content-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  block-size: var(--_size);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--on-primary);
  padding: 0 var(--_padding);
  background-color: var(--primary);
  margin: 0 0.5rem;
  border-radius: var(--_size);
  transition: transform var(--speed3), border-radius var(--speed2), padding var(--speed3);
  user-select: none;
  gap: 0.5rem;
  line-height: normal;
}
.beer :is(button, .button).small {
  --_size: 2rem;
  --_padding: 0.75rem;
}
.beer :is(button, .button).large {
  --_size: 3rem;
  --_padding: 1.25rem;
}
.beer :is(.button, button):is(.extra, .extend) {
  --_size: 3.5rem;
  font-size: 1rem;
  --_padding: 1.5rem;
}
.beer :is(button, .button):is(.square, .circle) {
  --_padding: 0;
}
.beer :is(button, .button).border {
  border-color: var(--outline-variant);
  color: var(--primary);
}
.beer .extend > span {
  display: none;
}
.beer .extend:is(:hover, .active) {
  inline-size: auto;
  --_padding: 1.5rem;
  padding: 0 var(--_padding);
}
.beer .extend:is(:hover, .active) > i + span {
  display: inherit;
  margin-inline-start: var(--_padding);
}
.beer .extend:is(:hover, .active) > :is(img, svg) + span {
  display: inherit;
  margin-inline-start: calc(1rem + var(--_padding));
}
.beer :is(.button, button)[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}
.beer .button[disabled] {
  pointer-events: none;
}
.beer :is(.button, button)[disabled]::before,
.beer :is(.button, button)[disabled]::after {
  display: none;
}
.beer :is(.button, button):not(.chip, .extend).fill {
  background-color: var(--secondary-container) !important;
  color: var(--on-secondary-container) !important;
}
.beer :is(.button, button):not(.chip, .extend).active {
  background-color: var(--primary-container);
  color: var(--on-primary-container);
}
.beer :is(.button, button):not(.chip, .extend).fill.active {
  background-color: var(--secondary) !important;
  color: var(--on-secondary) !important;
}
.beer :is(.button, button):not(.chip, .extend).border.active {
  background-color: var(--inverse-surface) !important;
  color: var(--inverse-on-surface) !important;
  border-color: var(--inverse-surface) !important;
}
.beer :is(.button, button):not(.chip):active,
.beer :is(.button, button):not(.chip).active {
  border-radius: 0.5rem !important;
}
.beer article {
  --_padding: 1rem;
  box-shadow: var(--elevate1);
  background-color: var(--surface-container-low);
  color: var(--on-surface);
  padding: var(--_padding);
  border-radius: 0.75rem;
  display: block;
  transition: transform var(--speed3), border-radius var(--speed3), padding var(--speed3);
}
.beer article.small {
  block-size: 12rem;
}
.beer article.medium {
  block-size: 20rem;
}
.beer article.large {
  block-size: 32rem;
}
.beer article.border {
  box-shadow: none;
  border: 0.0625rem solid var(--outline-variant);
}
.beer .chip {
  --_padding: 0.75rem;
  --_size: 2rem;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  block-size: var(--_size);
  min-inline-size: var(--_size);
  font-size: 0.875rem;
  font-weight: 500;
  background-color: transparent;
  border: 0.0625rem solid var(--outline-variant);
  color: var(--on-surface-variant);
  padding: 0 var(--_padding);
  margin: 0 0.5rem;
  text-transform: none;
  border-radius: 0.5rem;
  transition: transform var(--speed3), border-radius var(--speed3), padding var(--speed3);
  user-select: none;
  gap: 0.5rem;
  line-height: normal;
  letter-spacing: normal;
}
.beer .chip.medium {
  --_size: 2.5rem;
  --_padding: 1rem;
}
.beer .chip.large {
  --_padding: 1.25rem;
  --_size: 3rem;
}
.beer .chip.fill {
  border: none;
}
.beer dialog {
  --_padding: 1.5rem;
  display: block;
  visibility: hidden;
  border: none;
  opacity: 0;
  position: fixed;
  box-shadow: var(--elevate2);
  color: var(--on-surface);
  background-color: var(--surface-container-high);
  padding: var(--_padding);
  z-index: 100;
  inset: 10% auto auto 50%;
  min-inline-size: 20rem;
  max-inline-size: 100%;
  max-block-size: 80%;
  overflow-x: hidden;
  overflow-y: auto;
  transition: all var(--speed3), 0s background-color;
  border-radius: 1.75rem;
  transform: translate(-50%, -4rem);
  outline: none;
}
.beer dialog.small {
  inline-size: 25%;
  block-size: 25%;
}
.beer dialog.medium {
  inline-size: 50%;
  block-size: 50%;
}
.beer dialog.large {
  inline-size: 75%;
  block-size: 75%;
}
.beer dialog:is(.active, [open]) {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, 0);
}
.beer dialog:popover-open {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, 0);
}
.beer dialog:is(.top, .right, .bottom, .left, .max) {
  --_padding: 1rem;
  padding: calc(var(--top) + var(--_padding)) calc(var(--right) + var(--_padding)) calc(var(--bottom) + var(--_padding)) calc(var(--left) + var(--_padding));
}
.beer dialog:is(.top, .bottom) {
  opacity: 1;
  block-size: auto;
  inline-size: 100%;
  min-inline-size: auto;
  max-block-size: 100%;
}
.beer dialog.top {
  inset: 0 auto auto 0;
  transform: translateY(-100%);
  border-radius: 0 0 1rem 1rem;
  padding-block-end: var(--_padding);
}
.beer dialog.bottom {
  inset: auto auto 0 0;
  transform: translateY(100%);
  border-radius: 1rem 1rem 0 0;
}
.beer dialog:is(.left, .right) {
  opacity: 1;
  inset: 0 auto auto 0;
  inline-size: auto;
  block-size: 100%;
  max-block-size: 100%;
  background-color: var(--surface);
}
.beer [dir=rtl] dialog.right,
.beer dialog.left {
  inset: 0 auto auto 0;
  border-radius: 0 1rem 1rem 0;
  transform: translateX(-100%);
}
.beer [dir=rtl] dialog.left,
.beer dialog.right {
  inset: 0 0 auto auto;
  border-radius: 1rem 0 0 1rem;
  transform: translateX(100%);
}
.beer dialog.max {
  inset: 0 auto auto 0;
  inline-size: 100%;
  block-size: 100%;
  max-inline-size: 100%;
  max-block-size: 100%;
  transform: translateY(4rem);
  background-color: var(--surface);
  border-radius: 0;
}
.beer dialog:not(.left, .right, .top, .bottom, .max) {
  --top: 0rem;
  --bottom: 0rem;
  --left: 0rem;
  --right: 0rem;
}
.beer dialog:is(.active, [open]):is(.left, .right, .top, .bottom, .max) {
  transform: translate(0, 0);
}
.beer dialog:popover-open:is(.left, .right, .top, .bottom, .max) {
  transform: translate(0, 0);
}
.beer dialog.small:is(.left, .right) {
  inline-size: 20rem;
}
.beer dialog.medium:is(.left, .right) {
  inline-size: 32rem;
}
.beer dialog.large:is(.left, .right) {
  inline-size: 44rem;
}
.beer dialog.small:is(.top, .bottom) {
  block-size: 16rem;
}
.beer dialog.medium:is(.top, .bottom) {
  block-size: 24rem;
}
.beer dialog.large:is(.top, .bottom) {
  block-size: 32rem;
}
.beer hr,
.beer [class*=divider] {
  all: unset;
  min-inline-size: 1.5rem;
  min-block-size: auto;
  block-size: 0.0625rem;
  background-color: var(--outline-variant);
  display: block;
}
.beer hr + *,
.beer [class*=divider] + * {
  margin: 0 !important;
}
.beer hr.medium,
.beer .medium-divider {
  margin: 1rem 0 !important;
}
.beer hr.large,
.beer .large-divider {
  margin: 1.5rem 0 !important;
}
.beer hr.small,
.beer .small-divider {
  margin: 0.5rem 0 !important;
}
.beer hr.vertical,
.beer .divider.vertical {
  min-inline-size: auto;
  min-block-size: 1.5rem;
  inline-size: 0.0625rem;
}
.beer summary,
.beer summary:focus {
  list-style-type: none;
  cursor: pointer;
  outline: none;
}
.beer summary::-webkit-details-marker {
  display: none;
}
.beer .field {
  --_size: 3rem;
  --_start: 1.2rem;
  block-size: var(--_size);
  margin-block-end: 2rem;
  border-radius: 0.25rem 0.25rem 0 0;
}
.beer .grid > * > .field {
  margin-block-end: 1rem;
}
.beer .grid > * > .field + .field {
  margin-block-start: 2rem;
}
.beer .grid.no-space > * > .field + .field {
  margin-block-start: 1rem;
}
.beer .grid.medium-space > * > .field + .field {
  margin-block-start: 2.5rem;
}
.beer .grid.large-space > * > .field + .field {
  margin-block-start: 3rem;
}
.beer .field.small {
  --_size: 2.5rem;
  --_start: 1rem;
}
.beer .field.large {
  --_size: 3.5rem;
  --_start: 1.4rem;
}
.beer .field.extra {
  --_size: 4rem;
  --_start: 1.6rem;
}
.beer .field.border {
  border-radius: 0.25rem;
}
.beer .field.round.small {
  border-radius: 1.25rem;
}
.beer .field.round {
  border-radius: 1.5rem;
}
.beer .field.round.large {
  border-radius: 1.75rem;
}
.beer .field.round.extra {
  border-radius: 2rem;
}
/* icon */
.beer .field > :is(i, img, svg, progress, a:not(.helper, .error)) {
  position: absolute;
  inset: 50% auto auto auto;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 0;
  inline-size: 1.5rem;
  block-size: 1.5rem;
}
.beer .field > :is(i, img, svg, progress, a:not(.helper, .error)),
.beer [dir=rtl] .field > :is(i, img, svg, progress, a:not(.helper, .error)):first-child {
  inset: 50% 1rem auto auto;
}
.beer .field > :is(i, img, svg, progress, a:not(.helper, .error)):first-child,
.beer [dir=rtl] .field > :is(i, img, svg, progress, a:not(.helper, .error)) {
  inset: 50% auto auto 1rem;
}
.beer .field.invalid > i {
  color: var(--error);
}
.beer .field > progress.circle {
  inset-block-start: calc(50% - 0.75rem) !important;
  border-width: 0.1875rem;
}
.beer .field > a:not(.helper, .error) {
  z-index: 10;
}
.beer .field > a > :is(i, img, svg, progress, a:not(.helper, .error)) {
  inline-size: 1.5rem;
  block-size: 1.5rem;
}
/* input, textarea and select */
.beer .field > :is(input, textarea, select) {
  all: unset;
  position: relative;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-radius: inherit;
  border: 0.0625rem solid transparent;
  padding: 0 0.9375rem;
  font-family: inherit;
  font-size: 1rem;
  inline-size: 100%;
  block-size: 100%;
  outline: none;
  z-index: 1;
  background: none;
  resize: none;
  text-align: start;
  cursor: text;
}
.beer input::-webkit-date-and-time-value {
  text-align: start;
}
.beer :is(input, select, textarea):is(:-webkit-autofill, :autofill) {
  -webkit-background-clip: text;
  -webkit-text-fill-color: var(--on-surface);
}
.beer .field > :is(input, textarea, select):focus {
  border: 0.125rem solid transparent;
  padding: 0 0.875rem;
}
.beer .field.min > textarea {
  overflow: hidden;
  position: absolute;
  inset: 0;
  max-block-size: 12rem;
}
.beer input[type=file],
.beer input[type=color],
.beer :not(.field) > input[type^=date],
.beer :not(.field) > input[type^=time],
.beer input::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  inset: 0;
  inline-size: 100%;
  block-size: 100%;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  z-index: 2 !important;
}
.beer input::-webkit-search-decoration,
.beer input::-webkit-search-cancel-button,
.beer input::-webkit-search-results-button,
.beer input::-webkit-search-results-decoration,
.beer input::-webkit-inner-spin-button,
.beer input::-webkit-outer-spin-button {
  display: none;
}
.beer input[type=number] {
  appearance: textfield;
}
.beer .field.border > :is(input, textarea, select) {
  border-color: var(--outline);
}
.beer .field.border > :is(input, textarea, select):focus {
  border-color: var(--primary);
}
.beer .field.round > :is(input, textarea, select) {
  padding-inline: 1.4376rem;
}
.beer .field.round > :is(input, textarea, select):focus {
  padding-inline: 1.375rem;
}
.beer .field.prefix > :is(input, textarea, select) {
  padding-inline-start: 2.9375rem;
}
.beer .field.prefix > .slider {
  margin-inline-start: 3.5rem;
}
.beer .field.prefix > :is(input, textarea, select):focus {
  padding-inline-start: 2.875rem;
}
.beer .field.suffix > :is(input, textarea, select) {
  padding-inline-end: 2.9375rem;
}
.beer .field.suffix > .slider {
  margin-inline-end: 3.5rem;
}
.beer .field.suffix > :is(input, textarea, select):focus {
  padding-inline-end: 2.875rem;
}
.beer .field:not(.border, .round) > :is(input, textarea, select) {
  border-block-end-color: var(--outline);
}
.beer .field:not(.border, .round) > :is(input, textarea, select):focus {
  border-block-end-color: var(--primary);
}
.beer .field.round:not(.border, .fill) > :is(input, textarea, select),
.beer .field.round:not(.border) > :is(input, textarea, select):focus {
  box-shadow: var(--elevate1);
}
.beer .field.round:not(.border, .fill) > :is(input, textarea, select):focus {
  box-shadow: var(--elevate2);
}
.beer .field.invalid:not(.border, .round) > :is(input, textarea, select),
.beer .field.invalid:not(.border, .round) > :is(input, textarea, select):focus {
  border-block-end-color: var(--error);
}
.beer .field.invalid.border > :is(input, textarea, select),
.beer .field.invalid.border > :is(input, textarea, select):focus {
  border-color: var(--error);
}
.beer .field:has(> :disabled) {
  opacity: 0.5;
  cursor: not-allowed;
}
.beer .field > :disabled {
  cursor: not-allowed;
}
.beer .field.textarea.small:not(.min) {
  --_size: 5rem;
}
.beer .field.textarea:not(.min) {
  --_size: 5.5rem;
}
.beer .field.textarea.large:not(.min) {
  --_size: 6rem;
}
.beer .field.textarea.extra:not(.min) {
  --_size: 6.5rem;
}
.beer .field > select {
  user-select: none;
}
.beer .field > select > option {
  background-color: var(--surface-container);
  color: var(--on-surface);
}
.beer .field.label > :is(input, select) {
  padding-block-start: 1rem;
}
.beer .field.label.border:not(.fill) > :is(input, select) {
  padding-block-start: 0;
}
.beer .field > textarea {
  padding-block-start: var(--_start);
  white-space: pre-wrap;
}
.beer .field > textarea:focus {
  padding-block-start: calc(var(--_start) - 0.06rem);
}
.beer .field:not(.label) > textarea,
.beer .field.border.label:not(.fill) > textarea {
  padding-block-start: calc(var(--_start) - 0.5rem);
}
.beer .field:not(.label) > textarea:focus,
.beer .field.border.label:not(.fill) > textarea:focus {
  padding-block-start: calc(var(--_start) - 0.56rem);
}
/* label */
.beer .field.label > label {
  position: absolute;
  inset: -0.5rem auto auto 1rem;
  display: flex;
  inline-size: calc(100% - 5rem);
  block-size: calc(var(--_size) + 1rem);
  line-height: calc(var(--_size) + 1rem);
  font-size: 1rem;
  transition: all 0.2s;
  gap: 0.25rem;
  white-space: nowrap;
}
.beer .field.label.textarea:not(.min) > label {
  block-size: calc(var(--_size) - 1.5rem);
  line-height: calc(var(--_size) - 1.5rem);
}
.beer [dir=rtl] .field.label > label {
  inset: -0.5rem 1rem auto auto;
}
.beer .field.label.border.prefix:not(.fill) > :is(label.active, :focus + label, [placeholder]:not(:placeholder-shown) + label, select + label) {
  inset-inline-start: 1rem;
}
.beer .field.label.round > label,
.beer .field.label.border.prefix.round:not(.fill) > :is(label.active, :focus + label, [placeholder]:not(:placeholder-shown) + label, select + label) {
  inset-inline-start: 1.5rem;
}
.beer .field.label.prefix > label {
  inset-inline-start: 3rem;
}
.beer .field.label > :is(label.active, :focus + label, [placeholder]:not(:placeholder-shown) + label, select + label) {
  block-size: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.75rem;
}
.beer .field.label.border:not(.fill) > :is(label.active, :focus + label, [placeholder]:not(:placeholder-shown) + label, select + label) {
  block-size: 1rem;
  line-height: 1rem;
}
.beer .field.label.border:not(.fill) > label::after {
  content: "";
  display: block;
  margin-block-start: 0.5rem;
  border-block-start: 0.0625rem solid var(--outline);
  block-size: 1rem;
  transition: none;
  flex: auto;
}
.beer .field.label.border:not(.fill) > :focus + label::after {
  border-block-start: 0.125rem solid var(--primary);
}
.beer .field.label.border:not(.fill) > :is(input, textarea):is(:focus, [placeholder]:not(:placeholder-shown), .active),
.beer .field.label.border:not(.fill) > select {
  clip-path: polygon(-2% -2%, 0.75rem -2%, 0.75rem 0.5rem, calc(100% - 5rem) 0.5rem, calc(100% - 5rem) -2%, 102% -2%, 102% 102%, -2% 102%);
}
.beer [dir=rtl] .field.label.border:not(.fill) > :is(input, textarea):is(:focus, [placeholder]:not(:placeholder-shown), .active),
.beer [dir=rtl] .field.label.border:not(.fill) > select {
  clip-path: polygon(-2% -2%, 5rem -2%, 5rem 0.5rem, calc(100% - 0.75rem) 0.5rem, calc(100% - 0.75rem) -2%, 102% -2%, 102% 102%, -2% 102%);
}
.beer .field.label.border.round:not(.fill) > :is(input, textarea):is(:focus, [placeholder]:not(:placeholder-shown), .active),
.beer .field.label.border.round:not(.fill) > select {
  clip-path: polygon(-2% -2%, 1.25rem -2%, 1.25rem 0.5rem, calc(100% - 5rem) 0.5rem, calc(100% - 5rem) -2%, 102% -2%, 102% 102%, -2% 102%);
}
.beer [dir=rtl] .field.label.border.round:not(.fill) > :is(input, textarea):is(:focus, [placeholder]:not(:placeholder-shown), .active),
.beer [dir=rtl] .field.label.border.round:not(.fill) > select {
  clip-path: polygon(-2% -2%, 5rem -2%, 5rem 0.5rem, calc(100% - 1.25rem) 0.5rem, calc(100% - 1.25rem) -2%, 102% -2%, 102% 102%, -2% 102%);
}
.beer .field.label > :focus + label {
  color: var(--primary);
}
.beer .field.label.invalid > label,
.beer .field.label.invalid > label::after {
  color: var(--error) !important;
  border-color: var(--error) !important;
}
.beer .field.label > label > a {
  block-size: inherit;
  line-height: inherit;
  inline-size: 1rem;
}
.beer .field.label > label > a > :is(i, img, svg) {
  block-size: 1rem;
  line-height: 1rem;
  inline-size: 1rem;
  font-size: 1rem;
}
/* helper */
.beer .field > :is(.helper, .error) {
  position: absolute;
  inset: auto auto 0 1rem;
  transform: translateY(100%);
  font-size: 0.75rem;
  background: none !important;
  padding-block-start: 0.125rem;
}
.beer [dir=rtl] .field > :is(.helper, .error) {
  inset: auto 1rem 0 auto;
}
.beer a.helper {
  color: var(--primary);
}
.beer .field > .error {
  color: var(--error) !important;
}
.beer .field.round > :is(.helper, .error) {
  inset-inline-start: 1.5rem;
}
.beer .field.invalid > .helper {
  display: none;
}
.beer table td > .field {
  margin: 0;
}
.beer fieldset {
  border-radius: 0.25rem;
  padding: 1rem;
  border: 0.0625rem solid var(--outline-variant);
}
.beer fieldset > legend {
  margin: 0 -0.25rem;
  padding: 0 0.25rem;
}
.beer fieldset > legend + * {
  margin-block-start: 0 !important;
}
.beer .grid {
  --_gap: 1rem;
  display: grid;
  grid-template-columns: repeat(12, calc(8.33% - var(--_gap) + (var(--_gap) / 12)));
  gap: var(--_gap);
  block-size: auto;
}
.beer .grid.no-space {
  --_gap: 0rem;
}
.beer .grid.medium-space {
  --_gap: 1.5rem;
}
.beer .grid.large-space {
  --_gap: 2rem;
}
.beer .grid > * {
  margin: 0;
}
.beer .s1 {
  grid-area: auto/span 1;
}
.beer .s2 {
  grid-area: auto/span 2;
}
.beer .s3 {
  grid-area: auto/span 3;
}
.beer .s4 {
  grid-area: auto/span 4;
}
.beer .s5 {
  grid-area: auto/span 5;
}
.beer .s6 {
  grid-area: auto/span 6;
}
.beer .s7 {
  grid-area: auto/span 7;
}
.beer .s8 {
  grid-area: auto/span 8;
}
.beer .s9 {
  grid-area: auto/span 9;
}
.beer .s10 {
  grid-area: auto/span 10;
}
.beer .s11 {
  grid-area: auto/span 11;
}
.beer .s12 {
  grid-area: auto/span 12;
}
@media only screen and (min-width: 601px) {
  .beer .m1 {
    grid-area: auto/span 1;
  }

  .beer .m2 {
    grid-area: auto/span 2;
  }

  .beer .m3 {
    grid-area: auto/span 3;
  }

  .beer .m4 {
    grid-area: auto/span 4;
  }

  .beer .m5 {
    grid-area: auto/span 5;
  }

  .beer .m6 {
    grid-area: auto/span 6;
  }

  .beer .m7 {
    grid-area: auto/span 7;
  }

  .beer .m8 {
    grid-area: auto/span 8;
  }

  .beer .m9 {
    grid-area: auto/span 9;
  }

  .beer .m10 {
    grid-area: auto/span 10;
  }

  .beer .m11 {
    grid-area: auto/span 11;
  }

  .beer .m12 {
    grid-area: auto/span 12;
  }
}
@media only screen and (min-width: 993px) {
  .beer .l1 {
    grid-area: auto/span 1;
  }

  .beer .l2 {
    grid-area: auto/span 2;
  }

  .beer .l3 {
    grid-area: auto/span 3;
  }

  .beer .l4 {
    grid-area: auto/span 4;
  }

  .beer .l5 {
    grid-area: auto/span 5;
  }

  .beer .l6 {
    grid-area: auto/span 6;
  }

  .beer .l7 {
    grid-area: auto/span 7;
  }

  .beer .l8 {
    grid-area: auto/span 8;
  }

  .beer .l9 {
    grid-area: auto/span 9;
  }

  .beer .l10 {
    grid-area: auto/span 10;
  }

  .beer .l11 {
    grid-area: auto/span 11;
  }

  .beer .l12 {
    grid-area: auto/span 12;
  }
}
.beer i,
.beer :is(.checkbox, .radio, .switch) > span::before,
.beer :is(.checkbox, .radio, .switch) > span > i {
  --_size: 1.5rem;
  font-family: var(--font-icon);
  font-weight: normal;
  font-style: normal;
  font-size: var(--_size);
  letter-spacing: normal;
  text-transform: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
  vertical-align: middle;
  text-align: center;
  overflow: hidden;
  inline-size: var(--_size);
  min-inline-size: var(--_size);
  block-size: var(--_size);
  min-block-size: var(--_size);
  box-sizing: content-box;
  line-height: normal;
  border-radius: 0;
}
.beer i.tiny {
  --_size: 1rem;
}
.beer .chip > i,
.beer i.small {
  --_size: 1.25rem;
}
.beer i.medium {
  --_size: 1.5rem;
}
.beer i.large {
  --_size: 1.75rem;
}
.beer i.extra {
  --_size: 2rem;
}
.beer i.fill,
.beer a.row:is(:hover, :focus) > i,
.beer .transparent:is(:hover, :focus) > i {
  font-variation-settings: "FILL" 1;
}
.beer i > :is(img, svg) {
  inline-size: 100%;
  block-size: 100%;
  background-size: 100%;
  border-radius: inherit;
  position: absolute;
  inset: 0 auto auto 0;
  padding: inherit;
}
.beer i[class*=fa-] {
  font-size: calc(var(--_size) * 0.85);
  line-height: normal;
  block-size: auto;
  min-block-size: auto;
}
.beer .absolute {
  position: absolute;
}
.beer .fixed {
  position: fixed;
}
.beer :is(.absolute, .fixed).left.right {
  inline-size: auto;
}
.beer :is(.absolute, .fixed).left.right.small {
  block-size: 20rem;
}
.beer :is(.absolute, .fixed).left.right.medium {
  block-size: 28rem;
}
.beer :is(.absolute, .fixed).left.right.large {
  block-size: 44rem;
}
.beer :is(.absolute, .fixed).top.bottom.small {
  inline-size: 20rem;
}
.beer :is(.absolute, .fixed).top.bottom.medium {
  inline-size: 28rem;
}
.beer :is(.absolute, .fixed).top.bottom.large {
  inline-size: 44rem;
}
.beer .list {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  flex: 1;
}
.beer .list > li,
.beer .list > li > details > summary,
.beer .list > li > a:only-child {
  all: unset;
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: center;
  align-self: normal;
  text-align: start;
  justify-content: flex-start;
  white-space: nowrap;
  gap: 1rem;
  min-block-size: 3.5rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  flex: 1;
}
.beer .list > li:has(ul, ol, details[open], a:only-child) {
  padding: 0;
}
.beer .list > li > .list {
  padding: 0 0 0 1rem;
}
.beer .list > li > *,
.beer .list > li > a:only-child > *,
.beer .list > li > details > summary > *  {
  margin: 0;
}
.beer .list > li > :is(details, .max),
.beer .list > li > a:only-child > .max,
.beer .list > li > details > summary > .max {
  flex: 1;
}
.beer .list.border > li:not(:last-child)::before,
.beer .list.border > li > details[open] > summary::before {
  content: '';
  position: absolute;
  background-color: var(--outline-variant);
  inset: auto 0 0 0;
  block-size: 0.0625rem;
  inline-size: auto;
}
.beer .list.no-space > li,
.beer .list.no-space > li > details > summary {
  min-block-size: 2.5rem;
}
.beer .list.medium-space > li,
.beer .list.medium-space > li > details > summary {
  min-block-size: 4.5rem;
}
.beer .list.large-space > li,
.beer .list.large-space > li > details > summary {
  min-block-size: 5.5rem;
}
.beer main {
  flex: 1;
  padding: 0.5rem;
  overflow-x: hidden;
}
.beer:has(> main) {
  --_top: 0rem;
  --_bottom: 0rem;
  --_left: 0rem;
  --_right: 0rem;
  display: flex;
  flex-direction: column;
  min-block-size: calc(100vh - var(--top) - var(--_top) - var(--bottom) - var(--_bottom));
  box-sizing: border-box;
  background-color: var(--surface);
  margin-block: calc(var(--top) + var(--_top)) calc(var(--bottom) + var(--_bottom));
  margin-inline: calc(var(--left) + var(--_left)) calc(var(--right) + var(--_right));
}
.beer:has(> nav.top:not(.s, .m, .l)) {
  --_top: 5rem;
}
.beer:has(> nav.bottom:not(.s, .m, .l)) {
  --_bottom: 5rem;
}
.beer:has(> nav.left:not(.s, .m, .l)) {
  --_left: 5rem;
}
.beer:has(> nav.right:not(.s, .m, .l)) {
  --_right: 5rem;
}
.beer:has(> nav.drawer.left:not(.s, .m, .l)) {
  --_left: 20rem;
}
.beer:has(> nav.drawer.right:not(.s, .m, .l)) {
  --_right: 20rem;
}
.beer :not(main):has(> aside) {
  overflow: auto;
}
.beer aside {
  z-index: 1;
}
.beer aside:not(.fixed, .absolute).right {
  float: right;
}
.beer aside:not(.fixed, .absolute).left {
  float: left;
}
@media only screen and (max-width: 600px) {
  .beer :has(> nav.bottom.s) {
    --_bottom: 5rem;
  }

  .beer :has(> nav.top.s) {
    --_top: 5rem;
  }

  .beer :has(> nav.left.s) {
    --_left: 5rem;
  }

  .beer :has(> nav.right.s) {
    --_right: 5rem;
  }

  .beer :has(> nav.drawer.left.s) {
    --_left: 20rem;
  }

  .beer :has(> nav.drawer.right.s) {
    --_right: 20rem;
  }
}
@media only screen and (min-width: 601px) and (max-width: 992px) {
  .beer :has(> nav.bottom.m) {
    --_bottom: 5rem;
  }

  .beer :has(> nav.top.m) {
    --_top: 5rem;
  }

  .beer :has(> nav.left.m) {
    --_left: 5rem;
  }

  .beer :has(> nav.right.m) {
    --_right: 5rem;
  }

  .beer :has(> nav.drawer.left.m) {
    --_left: 20rem;
  }

  .beer :has(> nav.drawer.right.m) {
    --_right: 20rem;
  }
}
@media only screen and (min-width: 993px) {
  .beer :has(> nav.bottom.l) {
    --_bottom: 5rem;
  }

  .beer :has(> nav.top.l) {
    --_top: 5rem;
  }

  .beer :has(> nav.left.l) {
    --_left: 5rem;
  }

  .beer :has(> nav.right.l) {
    --_right: 5rem;
  }

  .beer :has(> nav.drawer.left.l) {
    --_left: 20rem;
  }

  .beer :has(> nav.drawer.right.l) {
    --_right: 20rem;
  }
}
.beer svg {
  fill: currentcolor;
}
.beer :is(img, svg, video):is(.small, .medium, .large, .tiny, .extra, .round, .circle, .square, .responsive) {
  --_size: 3rem;
  object-fit: cover;
  object-position: center;
  transition: transform var(--speed3), border-radius var(--speed3), padding var(--speed3);
  block-size: var(--_size);
  inline-size: var(--_size);
}
.beer :is(img, svg, video).round {
  --_round: 0.5rem;
}
.beer :is(img, svg, video).tiny {
  --_size: 2rem;
}
.beer :is(img, svg, video).small {
  --_size: 2.5rem;
}
.beer :is(img, svg, video).large {
  --_size: 3.5rem;
}
.beer :is(img, svg, video).extra {
  --_size: 4rem;
}
.beer :is(img, svg, video).responsive {
  --_size: 100%;
  margin: 0 auto;
}
.beer :is(img, svg, video).responsive.tiny {
  inline-size: 100%;
  block-size: 4rem;
}
.beer :is(img, svg, video).responsive.small {
  inline-size: 100%;
  block-size: 8rem;
}
.beer :is(img, svg, video).responsive.medium {
  inline-size: 100%;
  block-size: 12rem;
}
.beer :is(img, svg, video).responsive.large {
  inline-size: 100%;
  block-size: 16rem;
}
.beer :is(img, svg, video).responsive.extra {
  inline-size: 100%;
  block-size: 20rem;
}
.beer :is(img, svg, video).responsive.round {
  --_round: 2rem;
}
.beer :is(img, svg, video).empty-state {
  max-inline-size: 100%;
  inline-size: 24rem;
}
.beer :is(button, .button, .chip):not(.transparent) > .responsive {
  border: 0.25rem solid transparent;
}
.beer :is(button, .button, .chip, .field) > :is(img, svg):not(.responsive),
.beer .tabs :is(img, svg):not(.responsive) {
  min-inline-size: 1.5rem;
  max-inline-size: 1.5rem;
  min-block-size: 1.5rem;
  max-block-size: 1.5rem;
}
.beer :is(button, .button, .chip) > .responsive:first-child {
  margin-inline-start: calc(-1 * var(--_padding));
}
.beer :is(button, .button, .chip) > .responsive:not(:first-child) {
  margin-inline-end: calc(-1 * var(--_padding));
}
.beer :is(button, .button, .chip, .circle, .square, .extend) > .responsive {
  --_size: inherit;
  margin: 0 auto;
}
.beer .extend > :is(.responsive, i) {
  margin: 0;
  position: absolute;
  inset-inline: 1rem;
  z-index: 1;
}
.beer .extend > .responsive {
  inset-inline: 0;
  inline-size: 3.5rem;
}
.beer .extend.border > .responsive {
  inline-size: 3.375rem;
}
.beer menu {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  box-shadow: var(--elevate2);
  background-color: var(--surface-container);
  z-index: 11;
  inset: auto auto 0 0;
  inline-size: 100%;
  max-block-size: 50vh;
  max-inline-size: none !important;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 0.875rem;
  font-weight: normal;
  text-transform: none;
  color: var(--on-surface);
  line-height: normal;
  text-align: start;
  border-radius: 0.25rem;
  transform: scale(0.8) translateY(120%);
  transition: all var(--speed2), 0s background-color;
  justify-content: flex-start;
}
.beer [dir=rtl] menu {
  inset: auto 0 0 auto;
}
.beer menu.no-wrap {
  inline-size: max-content;
  white-space: nowrap !important;
}
.beer menu.active,
.beer :not(menu, [data-ui]):focus-within > menu,
.beer menu > li:hover > menu,
.beer menu > li > menu:hover {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(100%);
}
.beer menu.active.top,
.beer :not(menu, [data-ui]):focus-within > menu.top,
.beer menu > li:hover > menu.top,
.beer menu > li > menu.top:hover {
  transform: scale(1) translateY(-100%);
}
.beer menu * {
  white-space: inherit !important;
}
.beer menu > li,
.beer menu > li > a:only-child {
  all: unset;
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: center;
  align-self: normal;
  text-align: start;
  justify-content: inherit;
  white-space: nowrap;
  gap: 1rem;
  padding: 0.5rem 1rem;
  min-block-size: 3rem;
  flex: 1;
  margin: 0 !important;
  cursor: pointer;
}
.beer menu > li:is(:hover, :focus, .active) {
  background-color: var(--active);
}
.beer menu > li > :is(.max, .field),
.beer menu > li > a:only-child > .max,
.beer menu > li:has(.field, a:only-child) {
  flex: 1;
  padding: 0;
  margin: 0;
}
.beer menu.min {
  inset: 0 0 auto 0;
  transform: none !important;
  background-color: var(--surface-variant) !important;
  color: var(--on-surface-variant) !important;
}
.beer [dir=rtl] menu.min.right,
.beer menu.min.left,
.beer menu.top.left {
  inset: 0 0 auto auto;
}
.beer [dir=rtl] menu.min.left,
.beer menu.min.right,
.beer menu.top,
.beer menu.top.right {
  inset: 0 auto auto 0;
}
.beer menu.max {
  position: fixed;
  inset: 0;
  block-size: 100%;
  max-block-size: none;
  min-block-size: auto;
  z-index: 100;
  transform: none !important;
  background-color: var(--surface-variant) !important;
  color: var(--on-surface-variant) !important;
  border-radius: 0;
}
.beer menu.no-wrap:is(.min, .max) {
  min-inline-size: 16rem;
}
.beer [dir=rtl] menu.right,
.beer [dir=rtl] menu.top.min.right,
.beer menu.left,
.beer menu.top.min.left {
  inset: auto 0 0 auto;
}
.beer [dir=rtl] menu.left,
.beer [dir=rtl] menu.top.min.left,
.beer menu.right,
.beer menu.top.min {
  inset: auto auto 0 0;
}
.beer menu.top {
  transform: scale(0.8) translateY(-120%);
}
.beer menu:has(menu) {
  --_child: 1;
  --_type: 0;
  overflow: unset;
  white-space: nowrap;
  inline-size: auto;
  min-inline-size: 12rem;
  max-block-size: none;
}
.beer menu > li > :is(menu, menu.right),
.beer [dir=rtl] menu > li > menu.left {
  inset: auto auto calc(3rem * (var(--_child) - var(--_type))) 100%;
}
.beer [dir=rtl] menu > li > :is(menu, menu.right),
.beer menu > li > menu.left {
  inset: auto 100% calc(3rem * (var(--_child) - var(--_type))) auto;
}
.beer menu > li > :is(menu.top, menu.top.right),
.beer [dir=rtl] menu > li > menu.top.left {
  inset: calc(3rem * (var(--_child) - var(--_type))) auto auto 100%;
}
.beer [dir=rtl] menu > li > :is(menu.top, menu.top.right),
.beer menu > li > menu.top.left {
  inset: calc(3rem * (var(--_child) - var(--_type))) 100% auto auto;
}
.beer menu.no-space > li {
  min-block-size: 2.5rem;
}
.beer menu.medium-space > li {
  min-block-size: 3.5rem;
}
.beer menu.large-space > li {
  min-block-size: 4rem;
}
.beer menu.border > li:not(:last-child)::before {
  content: '';
  position: absolute;
  background-color: var(--outline-variant);
  inset: auto 0 0 0;
  block-size: 0.0625rem;
  inline-size: auto;
}
.beer menu.transparent {
  margin: 0 -1rem !important;
  padding: 0.5rem;
}
.beer menu.transparent > li {
  background-color: inherit;
  box-shadow: none;
  padding: 0;
}
.beer menu > li:nth-last-child(2) {
  --_child: 2;
}
.beer menu > li:nth-last-child(3) {
  --_child: 3;
}
.beer menu > li:nth-last-child(4) {
  --_child: 4;
}
.beer menu > li:nth-last-child(5) {
  --_child: 5;
}
.beer menu > li:nth-last-child(6) {
  --_child: 6;
}
.beer menu > li:nth-last-child(7) {
  --_child: 7;
}
.beer menu > li:nth-last-child(8) {
  --_child: 8;
}
.beer menu > li:nth-last-child(9) {
  --_child: 9;
}
.beer menu > li:nth-last-child(10) {
  --_child: 10;
}
.beer menu > li:nth-last-child(11) {
  --_child: 11;
}
.beer menu > li:nth-last-of-type(2) {
  --_type: 1;
}
.beer menu > li:nth-last-of-type(3) {
  --_type: 2;
}
.beer menu > li:nth-last-of-type(4) {
  --_type: 3;
}
.beer menu > li:nth-last-of-type(5) {
  --_type: 4;
}
.beer menu > li:nth-last-of-type(6) {
  --_type: 5;
}
.beer menu > li:nth-last-of-type(7) {
  --_type: 6;
}
.beer menu > li:nth-last-of-type(8) {
  --_type: 7;
}
.beer menu > li:nth-last-of-type(9) {
  --_type: 8;
}
.beer menu > li:nth-last-of-type(10) {
  --_type: 9;
}
.beer menu > li:nth-last-of-type(11) {
  --_type: 10;
}
.beer nav > :is(ol, ul),
.beer nav > :is(ol, ul) > li {
  all: unset;
}
.beer nav,
.beer .row,
.beer a.row,
.beer nav.drawer > :is(a, label),
.beer nav.drawer > :is(ol, ul) > li > :is(a, label) {
  display: flex;
  align-items: center;
  align-self: normal;
  text-align: start;
  justify-content: flex-start;
  white-space: nowrap;
  gap: 1rem;
  border-radius: 0;
}
.beer a.row,
.beer nav.row {
  min-block-size: 3rem;
  margin: 0;
}
.beer :is(nav, .row, .max) > :only-child,
.beer nav > :is(ol, ul) > li > :only-child {
  margin: 0;
}
.beer :is(nav, .row) > :not(ul, ol) {
  margin: 0;
  white-space: normal;
  flex: none;
}
.beer :is(nav, .row).min {
  display: inline-flex;
}
.beer :is(nav, .row, li).no-space {
  gap: 0;
}
.beer :is(nav, .row, li).tiny-space {
  gap: 0.5rem;
}
.beer :is(nav, .row, li).medium-space {
  gap: 1.5rem;
}
.beer :is(nav, .row, li).large-space {
  gap: 2rem;
}
.beer :is(nav, .row) > .max,
.beer :is(nav, .row) > :is(ol, ul) > .max,
.beer nav.drawer > :is(a, label) > .max,
.beer nav.drawer > :is(ol, ul) > li > :is(a, label) > .max {
  flex: 1;
}
.beer :is(nav, .row).wrap {
  display: flex;
  flex-wrap: wrap;
}
.beer :is(header, footer) > :is(nav, .row) {
  min-block-size: inherit;
}
.beer nav:is(.left, .right, .top, .bottom) {
  border: 0;
  position: fixed;
  color: var(--on-surface);
  transform: none;
  z-index: 100;
  block-size: auto;
  inline-size: auto;
  text-align: center;
  padding: calc(var(--top) + 0.5rem) calc(var(--right) + 1rem) calc(var(--bottom) + 0.5rem) calc(var(--left) + 1rem);
  margin: 0;
}
.beer nav:is(.left, .right) {
  justify-content: flex-start;
  flex-direction: column;
  background-color: var(--surface);
}
.beer nav:is(.top, .bottom) {
  justify-content: center;
  flex-direction: row;
  background-color: var(--surface-container);
}
.beer nav.top {
  block-size: calc(var(--top) + 5rem);
  inset: 0 0 auto 0;
  padding-block-end: 0.5rem;
}
.beer nav.left,
.beer [dir=rtl] nav.right {
  inline-size: calc(var(--left) + 5rem);
  inset: 0 auto 0 0;
  padding-inline-end: 1rem;
}
.beer [dir=rtl] nav.right {
  padding-inline-end: calc(var(--left) + 1rem);
}
.beer nav.right,
.beer [dir=rtl] nav.left {
  inline-size: calc(var(--right) + 5rem);
  inset: 0 0 0 auto;
  padding-inline-start: 1rem;
}
.beer [dir=rtl] nav.left {
  padding-inline-start: calc(var(--right) + 1rem);
}
.beer nav.bottom {
  min-block-size: calc(var(--bottom) + 5rem);
  inset: auto 0 0 0;
  padding-block-start: 0.5rem;
}
.beer nav.drawer,
.beer [dir=rtl] nav.drawer {
  flex-direction: column;
  align-items: normal;
  inline-size: 20rem;
  gap: 0;
}
.beer nav.drawer:not(.left, .right, .top, .bottom) {
  padding: 0.5rem 1rem;
}
.beer dialog > nav.drawer:not(.left, .right, .top, .bottom) {
  padding: 0 1rem;
  background-color: inherit;
}
.beer nav.drawer:is(.min, .max) {
  inline-size: auto;
}
.beer nav.drawer.max {
  inline-size: 100%;
}
.beer nav.drawer > :is(a, label),
.beer nav.drawer > :is(ol, ul) > li > :is(a, label),
.beer :is(a.row, nav.row):is(.wave, .slow-ripple, .ripple, .fast-ripple) {
  padding: 0.75rem;
  font-size: inherit;
}
.beer nav.drawer > a,
.beer nav.drawer > :is(ol, ul) > li > a {
  border-radius: 2rem;
}
.beer nav.drawer > a:is(:hover, .active),
.beer nav.drawer > :is(ol, ul) > li > a:is(:hover, .active) {
  background-color: var(--secondary-container);
}
.beer nav.drawer > a:is(:hover, :focus, .active) > i,
.beer nav.drawer > :is(ol, ul) > li > a:is(:hover, :focus, .active) > i {
  font-variation-settings: "FILL" 1;
}
.beer nav > :is(ol, ul) {
  all: inherit;
  flex: auto;
}
.beer nav:not(.left, .right, .bottom, .top) > :is(ol, ul) {
  padding: 0;
}
.beer nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip),
.beer nav:is(.left, .right, .top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip) {
  align-self: center;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  line-height: normal;
}
.beer nav:is(.top, .bottom):not(.drawer) > a:not(.button, .chip),
.beer nav:is(.top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip) {
  inline-size: 3.5rem;
}
.beer nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip) > i,
.beer nav:is(.left, .right, .top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip) > i {
  padding: 0.25rem;
  border-radius: 2rem;
  transition: padding var(--speed1) linear;
  margin: 0 auto;
}
.beer nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip):is(:hover, :focus, .active) > i,
.beer nav:is(.left, .right, .top, .bottom):not(.drawer) > :is(ol, ul) > li > a:not(.button, .chip):is(:hover, :focus, .active) > i {
  background-color: var(--secondary-container);
  color: var(--on-secondary-container);
  padding: 0.25rem 1rem;
  font-variation-settings: "FILL" 1;
}
.beer nav:is(.left, .right, .top, .bottom):not(.drawer) > a:not(.button, .chip).active {
  background: none !important;
  color: currentColor !important;
}
.beer :is(nav, .row):is(.left-align, .top-align, .vertical) {
  justify-content: flex-start;
}
.beer :is(nav, .row):is(.right-align, .bottom-align) {
  justify-content: flex-end;
}
.beer :is(nav, .row):is(.center-align, .middle-align) {
  justify-content: center;
}
.beer :is(nav, .row):is(.left-align, .top-align, .vertical).vertical {
  align-items: flex-start;
}
.beer :is(nav, .row):is(.right-align, .bottom-align).vertical {
  align-items: flex-end;
}
.beer :is(nav, .row):is(.center-align, .middle-align).vertical {
  align-items: center;
}
.beer :is(.drawer, .vertical) > :is(li, [class*=divider], hr):not(.vertical),
.beer :is(.drawer, .vertical) > :is(ol, ul) > li:not(.vertical) {
  align-self: stretch;
}
.beer nav:not(.left, .right) > .space {
  inline-size: 0.5rem;
}
.beer nav:not(.left, .right) > .medium-space {
  inline-size: 1rem;
}
.beer nav:not(.left, .right) > .large-space {
  inline-size: 1.5rem;
}
.beer nav.tabbed {
  background-color: var(--surface-container);
  border-radius: 4rem !important;
  gap: 0rem;
  block-size: 4rem;
}
.beer nav.tabbed.small {
  block-size: 3rem;
}
.beer nav.tabbed.large {
  block-size: 5rem;
}
.beer nav.tabbed > a {
  border-radius: inherit;
  block-size: inherit;
  display: inline-flex;
  align-items: center;
  padding-inline: 1rem;
  gap: 0.5rem;
  font-size: 1rem;
  flex: 1;
}
.beer nav.tabbed > a.active {
  background-color: var(--primary-container);
}
.beer nav.toolbar {
  display: inline-flex;
  justify-content: space-around;
  border-radius: 2rem;
  background-color: var(--surface-container);
  color: var(--on-surface);
  padding: 0 1rem;
  gap: 0.5rem;
  min-block-size: 4rem;
  min-inline-size: 4rem;
}
.beer nav.toolbar > a {
  display: inline-flex;
  gap: 0.5rem;
  min-inline-size: 2.5rem;
  min-block-size: 2.5rem;
  border-radius: 1.75rem;
}
.beer nav.toolbar > a:has(> :not(i)) {
  padding: 0 1rem;
}
.beer nav.toolbar > a.active {
  background-color: var(--secondary-container);
  color: var(--on-secondary-container);
}
.beer nav.toolbar.fill {
  background-color: var(--primary-container) !important;
  color: var(--on-primary-container) !important;
}
.beer nav.toolbar.fill > a.active {
  background-color: var(--surface-container) !important;
  color: var(--on-surface) !important;
}
.beer nav.toolbar.vertical {
  flex-direction: column !important;
  min-inline-size: 4rem;
  padding: 1rem 0;
  align-self: center;
  align-items: center !important;
}
.beer nav.toolbar.vertical > a {
  inline-size: 2.5rem;
  block-size: 2.5rem;
}
.beer nav.toolbar.vertical > a > :is(div, span):not(.badge, .tooltip) {
  display: none;
}
.beer nav.toolbar.max {
  border-radius: 0;
  display: flex;
}
.beer nav.group {
  background: none !important;
}
.beer nav.group:is(.connected, .split) {
  gap: 0.125rem;
}
.beer nav.group:not(.split) > :is(.button, button):not(.chip, .fill, .border).active {
  background-color: var(--primary);
  color: var(--on-primary);
}
.beer nav.group:not(.split) > :is(.button, button):not(.chip, .fill, .border) {
  background-color: var(--surface-container);
  color: var(--on-surface-container);
}
.beer nav.group:is(.connected, .split) > :is(.button, button):not(.chip).active,
.beer nav.split > :is(.button, button):active {
  border-radius: 2rem !important;
}
.beer :not(nav) > :is(ul, ol) {
  all: revert;
}
.beer :is(.scroll, .no-scroll, .no-space, .tabs, .tabbed) > :focus-visible {
  outline: 0.125rem solid var(--primary);
  outline-offset: -0.125rem;
}
.beer nav.split > :is(.button, button):not(.chip, .fill, .border) {
  background-color: var(--primary);
  color: var(--on-primary);
}
.beer nav.primary > :is(button, .button),
.beer nav:not(.toolbar, .tabbed, .drawer, .group).primary-container > a:is(:hover, :focus, .active) > i,
.beer nav.drawer.primary-container > a:is(:hover, :focus, .active),
.beer nav:not(.split).primary-container > :is(a, button, .button).active,
.beer :is(a, button, .button):not(.extend).primary-container.active {
  background-color: var(--primary) !important;
  color: var(--on-primary) !important;
}
.beer nav.primary-container > :is(button, .button),
.beer nav:not(.toolbar, .tabbed, .drawer, .group).primary > a:is(:hover, :focus, .active) > i,
.beer nav.drawer.primary > a:is(:hover, :focus, .active),
.beer nav:not(.split).primary > :is(a, button, .button).active,
.beer :is(a, button, .button):not(.extend).primary.active {
  background-color: var(--primary-container) !important;
  color: var(--on-primary-container) !important;
}
.beer nav.secondary > :is(button, .button),
.beer nav:not(.toolbar, .tabbed, .drawer, .group).secondary-container > a:is(:hover, :focus, .active) > i,
.beer nav.drawer.secondary-container > a:is(:hover, :focus, .active),
.beer nav:not(.split).secondary-container > :is(a, button, .button).active,
.beer :is(a, button, .button):not(.extend).secondary-container.active {
  background-color: var(--secondary) !important;
  color: var(--on-secondary) !important;
}
.beer nav.secondary-container > :is(button, .button),
.beer nav:not(.toolbar, .tabbed, .drawer, .group).secondary > a:is(:hover, :focus, .active) > i,
.beer nav.drawer.secondary > a:is(:hover, :focus, .active),
.beer nav:not(.split).secondary > :is(a, button, .button).active,
.beer :is(a, button, .button):not(.extend).secondary.active {
  background-color: var(--secondary-container) !important;
  color: var(--on-secondary-container) !important;
}
.beer nav.tertiary > :is(button, .button),
.beer nav:not(.toolbar, .tabbed, .drawer, .group).tertiary-container > a:is(:hover, :focus, .active) > i,
.beer nav.drawer.tertiary-container > a:is(:hover, :focus, .active),
.beer nav:not(.split).tertiary-container > :is(a, button, .button).active,
.beer :is(a, button, .button):not(.extend).tertiary-container.active {
  background-color: var(--tertiary) !important;
  color: var(--on-tertiary) !important;
}
.beer nav.tertiary-container > :is(button, .button),
.beer nav:not(.toolbar, .tabbed, .drawer, .group).tertiary > a:is(:hover, :focus, .active) > i,
.beer nav.drawer.tertiary > a:is(:hover, :focus, .active),
.beer nav:not(.split).tertiary > :is(a, button, .button).active,
.beer :is(a, button, .button):not(.extend).tertiary.active {
  background-color: var(--tertiary-container) !important;
  color: var(--on-tertiary-container) !important;
}
@media only screen and (max-width: 600px) {
  .beer nav.top,
  .beer nav.bottom {
    justify-content: space-around;
  }
}
.beer .overlay,
.beer dialog::backdrop {
  display: block !important;
  opacity: 0;
  visibility: hidden;
  position: fixed;
  inset: 0;
  color: var(--on-surface);
  background-color: var(--overlay);
  z-index: 100;
  transition: all var(--speed3), 0s background-color;
}
.beer .overlay.active {
  opacity: 1;
  visibility: visible;
}
.beer dialog:popover-open::backdrop {
  opacity: 1;
  visibility: visible;
}
.beer .overlay + dialog::backdrop,
.beer .snackbar::backdrop {
  display: none;
}
.beer [popover] {
  border: 0;
}
.beer .page {
  --_transform: translate(0, 0);
  opacity: 0;
  position: absolute;
  display: none;
}
.beer .page.active {
  opacity: 1;
  position: inherit;
  display: inherit;
  animation: var(--speed4) to-page ease;
}
.beer .page.active.top {
  --_transform: translate(0, -4rem);
}
.beer .page.active.bottom {
  --_transform: translate(0, 4rem);
}
.beer .page.active.left {
  --_transform: translate(-4rem, 0);
}
.beer .page.active.right {
  --_transform: translate(4rem, 0);
}
@keyframes to-page {
  from {
    opacity: 0;
    transform: var(--_transform);
  }

  to {
    opacity: 1;
    transform: translate(0, 0);
  }
}
.beer progress {
  position: relative;
  inline-size: 100%;
  block-size: 0.5rem;
  color: var(--primary);
  background: var(--primary-container);
  border-radius: 1rem;
  flex: none;
  border: none;
  overflow: hidden;
  writing-mode: horizontal-tb;
  direction: ltr;
  -webkit-appearance: none;
}
.beer progress.small {
  inline-size: 4rem;
}
.beer progress.medium {
  inline-size: 8rem;
}
.beer progress.large {
  inline-size: 12rem;
}
.beer progress:not(.circle, [value])::after {
  content: "";
  position: absolute;
  inset: 0;
  inline-size: 100%;
  block-size: 100%;
  clip-path: none;
  background: currentcolor;
  animation: 1.6s to-linear ease infinite;
}
.beer progress:not(.circle, [value])::-moz-progress-bar {
  animation: 1.6s to-linear ease infinite;
}
.beer progress:not(.circle, [value])::-webkit-progress-value {
  animation: 1.6s to-linear ease infinite;
}
.beer progress::-webkit-progress-bar {
  background: none;
}
.beer progress::-webkit-progress-value {
  background: currentcolor;
}
.beer progress::-moz-progress-bar {
  background: currentcolor;
}
.beer progress.circle {
  display: inline-block;
  inline-size: 2.5rem;
  block-size: 2.5rem;
  border-radius: 50%;
  border-width: 0.3rem;
  border-style: solid;
  border-color: currentcolor;
  animation: 1.6s to-circular linear infinite;
  background: none;
  flex: none;
}
.beer progress.circle::-moz-progress-bar {
  background: none;
}
.beer progress.circle.small {
  inline-size: 1.5rem;
  block-size: 1.5rem;
  border-width: 0.2rem;
}
.beer progress.circle.large {
  inline-size: 3.5rem;
  block-size: 3.5rem;
  border-width: 0.4rem;
}
.beer :is(nav, .row, .field) > progress:not(.circle, .small, .medium, .large) {
  flex: auto;
}
.beer progress.max {
  display: unset;
  position: absolute;
  inline-size: 100% !important;
  block-size: 100% !important;
  color: var(--active);
  background: none;
  inset: 0;
  border-radius: inherit;
  animation: none;
  writing-mode: horizontal-tb;
}
.beer progress:is(.horizontal, .vertical, .max) {
  display: unset;
  inline-size: 100% !important;
}
.beer progress.vertical {
  writing-mode: vertical-lr;
}
.beer progress.max.vertical {
  transform: rotate(-180deg);
}
.beer progress.max + * {
  margin-block-start: 0;
}
.beer :is(.button, button, .chip) > progress.circle {
  color: inherit;
}
@supports (-moz-appearance:none) {
  .beer progress.max.vertical {
    transform: none;
  }
}
@keyframes to-linear {
  0% {
    margin-inline-start: 0%;
    inline-size: 0%;
  }

  50% {
    margin-inline-start: 0%;
    inline-size: 100%;
  }

  100% {
    margin-inline-start: 100%;
    inline-size: 0%;
  }
}
@keyframes to-circular {
  0% {
    transform: rotate(0deg);
    clip-path: polygon(50% 50%, 0% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%);
  }

  20% {
    clip-path: polygon(50% 50%, 0% 0%, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%);
  }

  30% {
    clip-path: polygon(50% 50%, 0% 0%, 50% 0%, 100% 0%, 100% 50%, 100% 50%, 100% 50%, 100% 50%, 100% 50%);
  }

  40% {
    clip-path: polygon(50% 50%, 0% 0%, 50% 0%, 100% 0%, 100% 50%, 100% 100%, 100% 100%, 100% 100%, 100% 100%);
  }

  50% {
    clip-path: polygon(50% 50%, 50% 0%, 50% 0%, 100% 0%, 100% 50%, 100% 100%, 50% 100%, 50% 100%, 50% 100%);
  }

  60% {
    clip-path: polygon(50% 50%, 100% 50%, 100% 50%, 100% 50%, 100% 50%, 100% 100%, 50% 100%, 0% 100%, 0% 100%);
  }

  70% {
    clip-path: polygon(50% 50%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 0% 100%, 0% 50%);
  }

  80% {
    clip-path: polygon(50% 50%, 0% 100%, 0% 100%, 0% 100%, 0% 100%, 0% 100%, 0% 100%, 0% 100%, 0% 50%);
  }

  90% {
    transform: rotate(360deg);
    clip-path: polygon(50% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%);
  }

  100% {
    clip-path: polygon(50% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%);
  }
}
/* checkbox, radio, switch */
.beer .checkbox,
.beer .radio,
.beer .switch {
  --_size: 1.5rem;
  direction: ltr;
  inline-size: auto;
  block-size: auto;
  line-height: normal;
  white-space: nowrap;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}
.beer :is(.checkbox, .radio, .switch).small {
  --_size: 1rem;
}
.beer :is(.checkbox, .radio, .switch).large {
  --_size: 2rem;
}
.beer :is(.checkbox, .radio, .switch).extra {
  --_size: 2.5rem;
}
.beer :is(.checkbox, .radio) > input {
  inline-size: var(--_size);
  block-size: var(--_size);
  opacity: 0;
}
.beer .switch > input {
  inline-size: 3.25rem;
  block-size: 2rem;
  opacity: 0;
}
.beer :is(.checkbox, .radio, .switch) > span {
  display: inline-flex;
  align-items: center;
  color: var(--on-surface);
  font-size: 0.875rem;
}
.beer :is(.checkbox, .radio) > span:not(:empty) {
  padding-inline-start: 0.25rem;
}
.beer :is(.checkbox, .radio, .switch) > span::before,
.beer :is(.checkbox, .radio, .switch) > span > i,
.beer :is(.checkbox, .radio) > span::after {
  --_size: inherit;
  content: '';
  inline-size: var(--_size);
  block-size: var(--_size);
  box-sizing: border-box;
  margin: 0 auto;
  outline: none;
  color: var(--primary);
  position: absolute;
  inset: auto auto auto calc(var(--_size) * -1);
  border-radius: 50%;
  user-select: none;
  z-index: 1;
}
.beer .switch > span::before,
.beer .switch.icon > span > i {
  position: absolute;
  inset: 50% auto auto 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--speed2);
  font-size: calc(var(--_size) - 0.5rem);
  user-select: none;
  min-inline-size: var(--_size);
  min-block-size: var(--_size);
  content: "";
  color: var(--surface-variant);
  background-color: var(--outline);
}
.beer .switch > span::before,
.beer .switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(0.6);
}
.beer .switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(1);
}
.beer .checkbox > span::before {
  content: "check_box_outline_blank";
}
.beer .checkbox > input:checked + span::before {
  content: "check_box";
  font-variation-settings: "FILL" 1;
}
.beer .checkbox > input:indeterminate + span::before {
  content: "indeterminate_check_box";
}
.beer .radio > span::before {
  content: "radio_button_unchecked";
}
.beer .radio > input:checked + span::before {
  content: "radio_button_checked";
}
.beer :is(.radio, .checkbox, .switch).icon > span::before {
  content: "" !important;
  font-variation-settings: unset !important;
}
.beer :is(.checkbox, .radio) > span::after {
  transition: all var(--speed1);
  background-color: currentColor;
  box-shadow: 0 0 0 0 currentColor;
  opacity: 0;
}
.beer :is(.checkbox, .radio):is(:hover) > input:not(:disabled) + span::after,
.beer :is(.checkbox, .radio) > input:not(:disabled):is(:focus) + span::after {
  box-shadow: 0 0 0 0.5rem currentColor;
  opacity: 0.1;
}
.beer .switch > input:not(:disabled):is(:focus, :hover) + span::before,
.beer .switch.icon > input:not(:disabled):is(:focus, :hover) + span > i {
  box-shadow: 0 0 0 0.5rem var(--active);
}
.beer :is(.checkbox, .radio) > input:checked + span::before,
.beer :is(.checkbox, .radio).icon > input:checked + span > i {
  color: var(--primary);
}
.beer .icon > input:checked + span > i:first-child,
.beer .icon > span > i:last-child {
  opacity: 0;
}
.beer .icon > input:checked + span > i:last-child,
.beer .icon > span > i:first-child {
  opacity: 1;
}
.beer .switch > input:checked + span::after {
  border: none;
  background-color: var(--primary);
}
.beer .switch > input:checked + span::before,
.beer .switch.icon > input:checked + span > i {
  content: "check";
  color: var(--primary);
  background-color: var(--on-primary);
  transform: translate(-1.75rem, -50%) scale(1);
}
.beer .switch > input:active:not(:disabled) + span::before,
.beer .switch.icon > input:active:not(:disabled) + span > i {
  transform: translate(-3rem, -50%) scale(1.2);
}
.beer .switch > input:active:checked:not(:disabled) + span::before,
.beer .switch.icon > input:active:checked:not(:disabled) + span > i {
  transform: translate(-1.75rem, -50%) scale(1.2);
}
.beer :is(.checkbox, .radio, .switch) > input:disabled + span {
  opacity: 0.5;
  cursor: not-allowed;
}
.beer .switch > span::after {
  content: "";
  position: absolute;
  inset: 50% auto auto 0;
  background-color: var(--active);
  border: 0.125rem solid var(--outline);
  box-sizing: border-box;
  inline-size: 3.25rem;
  block-size: 2rem;
  border-radius: 2rem;
  transform: translate(-3.25rem, -50%);
}
.beer .field > :is(nav, .row) {
  flex-grow: 1;
  padding: 0 1rem;
}
.beer .field.round > :is(nav, .row) {
  flex-grow: 1;
  padding: 0 1.5rem;
}
.beer [dir=rtl] .switch {
  transform: scale(-1);
}
.beer [dir=rtl] .switch > span::before,
.beer [dir=rtl] .switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(-0.6);
}
.beer [dir=rtl] .switch.icon > span > i {
  transform: translate(-3rem, -50%) scale(-1);
}
.beer [dir=rtl] .switch > input:checked + span::before,
.beer [dir=rtl] .switch.icon > input:checked + span > i {
  transform: translate(-1.75rem, -50%) scale(-1);
}
.beer .switch > :focus-visible + span::after {
  outline: 0.125rem solid var(--primary);
  outline-offset: 0.25rem;
}
.beer :is(.checkbox, .radio) > :focus-visible + span::before {
  outline: 0.125rem solid var(--primary);
  outline-offset: 0.375rem;
}
.beer .slider {
  --_start: 0%;
  --_end: 0%;
  --_value1: "";
  --_value2: "";
  --_track: 1rem;
  --_thumb: max(2.5rem, calc(var(--_track) + 0.5rem));
  display: flex;
  align-items: center !important;
  inline-size: auto;
  block-size: var(--_thumb);
  flex: none;
  direction: ltr;
  margin: 0 1.25rem;
}
.beer [dir=rtl] .slider {
  transform: scaleX(-1);
}
.beer .slider.vertical {
  flex-direction: row !important;
  margin: 0.5rem auto !important;
  padding: 50% 0;
  transform: rotate(-90deg);
  inline-size: 100%;
}
.beer .slider.tiny {
  --_track: 1rem;
}
.beer .slider.small {
  --_track: 1.5rem;
}
.beer .slider.medium {
  --_track: 2.5rem;
}
.beer .slider.large {
  --_track: 3.5rem;
}
.beer .slider.extra {
  --_track: 6rem;
}
.beer .slider > input {
  appearance: none;
  box-shadow: none;
  border: none;
  outline: none;
  pointer-events: none;
  inline-size: 100%;
  block-size: var(--_track);
  background: none;
  z-index: 1;
  padding: 0;
  margin: 0;
  transform: rotate(0deg);
}
.beer .slider > input:only-of-type {
  pointer-events: all;
}
.beer .slider > input + input {
  position: absolute;
}
.beer .slider > input::-webkit-slider-thumb {
  appearance: none;
  box-shadow: none;
  border: none;
  outline: none;
  pointer-events: all;
  block-size: var(--_thumb);
  inline-size: 0.25rem;
  border-radius: 0.25rem;
  background: var(--primary);
  cursor: grab;
  margin: 0;
  z-index: 1;
}
.beer .slider > input::-webkit-slider-thumb:active {
  cursor: grabbing;
}
.beer .slider > input::-moz-range-thumb {
  appearance: none;
  box-shadow: none;
  border: none;
  outline: none;
  pointer-events: all;
  block-size: 2.75rem;
  inline-size: 0.25rem;
  border-radius: 0.25rem;
  background: var(--primary);
  cursor: grab;
  margin: 0;
}
.beer .slider > input::-moz-range-thumb:active {
  cursor: grabbing;
}
.beer .slider > input:not(:disabled):is(:focus)::-webkit-slider-thumb {
  transform: scaleX(0.6);
}
.beer .slider > input:not(:disabled):is(:focus)::-moz-range-thumb {
  transform: scaleX(0.6);
}
.beer .slider > input:disabled {
  cursor: not-allowed;
  opacity: 1;
}
.beer .slider > input:disabled::-webkit-slider-thumb {
  background: var(--outline);
  cursor: not-allowed;
}
.beer .slider > input:disabled::-moz-range-thumb {
  background: var(--outline);
  cursor: not-allowed;
}
.beer .slider > input:disabled ~ span {
  background: var(--outline);
}
.beer .slider > span {
  position: absolute;
  block-size: var(--_track);
  border-radius: 1rem 0 0 1rem;
  background: var(--primary);
  color: var(--on-primary);
  z-index: 0;
  inset: calc(50% - (var(--_track) / 2)) var(--_end) auto var(--_start);
  clip-path: polygon(0 0, calc(100% - 0.5rem) 0, calc(100% - 0.5rem) 100%, 0 100%);
}
.beer .slider > input[type=range] + input[type=range] ~ span {
  border-radius: 0;
  clip-path: polygon(0.5rem 0, max(0.5rem, calc(100% - 0.5rem)) 0, max(0.5rem, calc(100% - 0.5rem)) 100%, 0.5rem 100%);
}
.beer .field > .slider {
  inline-size: 100%;
}
.beer .slider::before {
  content: "";
  position: absolute;
  inline-size: 100%;
  block-size: var(--_track);
  border-radius: 1rem;
  background: var(--secondary-container);
  clip-path: polygon(calc(var(--_start) - 0.5rem) 0, 0 0, 0 100%, calc(var(--_start) - 0.5rem) 100%, calc(var(--_start) - 0.5rem) 0, calc(100% - var(--_end) + 0.5rem) 0, 100% 0, 100% 100%, calc(100% - var(--_end) + 0.5rem) 100%, calc(100% - var(--_end) + 0.5rem) 0);
}
.beer .slider:has(> [disabled])::before {
  background: var(--outline-variant);
}
.beer .slider:has([disabled]) {
  opacity: 0.62;
}
.beer .slider > span > i {
  position: absolute;
  block-size: auto;
  inset: 0 auto 0 0.5rem;
  color: currentColor;
  z-index: 1;
}
.beer .slider:not(.medium, .large, .extra) > span > i {
  display: none;
}
.beer .slider.vertical > i {
  transform: rotate(90deg);
}
.beer .slider > .tooltip {
  visibility: hidden !important;
  opacity: 0 !important;
  inset: 0 auto auto calc(100% - var(--_end));
  border-radius: 2rem;
  transition: top var(--speed2) ease, opacity var(--speed2) ease;
  transform: translate(-50%, -50%) !important;
  padding: 0.75rem 1rem;
}
.beer [dir=rtl] .slider > .tooltip {
  transform: translate(-50%, -50%) scaleX(-1) !important;
}
.beer .slider > .tooltip + .tooltip {
  inset: 0.25rem calc(100% - var(--_start)) auto auto;
  transform: translate(50%, -50%) !important;
}
.beer [dir=rtl] .slider > .tooltip + .tooltip {
  transform: translate(50%, -50%) scaleX(-1) !important;
}
.beer .slider > .tooltip::before {
  content: var(--_value1);
}
.beer .slider > .tooltip + .tooltip::before {
  content: var(--_value2);
}
.beer .slider > :focus ~ .tooltip {
  inset-block-start: -1rem !important;
  opacity: 1 !important;
  visibility: visible !important;
}
.beer .slider.vertical > .tooltip {
  inset-block-start: auto !important;
  margin-block-start: calc(-1 * var(--_thumb)) !important;
  block-size: 2.5rem;
  inline-size: 2.5rem;
  transform: rotate(90deg) translate(-75%, 50%) !important;
}
.beer .slider.vertical > .tooltip + .tooltip {
  transform: rotate(90deg) translate(-75%, -50%) !important;
}
.beer :is(nav, .row, .field) > .slider:not(.circle, .small, .medium, .large) {
  flex: auto;
}
.beer .slider.max,
.beer .slider.max.vertical,
.beer .slider.max > input,
.beer .slider.max.vertical > input {
  all: unset;
  margin: 0 !important;
  position: absolute;
  color: var(--primary);
  inset: 0;
  border-radius: inherit;
  overflow: hidden;
  z-index: 2;
  cursor: grab;
  inline-size: 100%;
  block-size: 100%;
}
.beer .slider.max::before {
  display: none;
}
.beer .slider.max.vertical > input {
  writing-mode: vertical-lr;
  transform: rotate(-180deg);
}
.beer .slider.max > input::-webkit-slider-thumb {
  opacity: 0;
  inline-size: 1rem;
  block-size: 100vh;
  transform: none !important;
}
.beer .slider.max > input::-moz-range-thumb {
  opacity: 0;
  inline-size: 1rem;
  block-size: 100vh;
  transform: none !important;
}
.beer .slider.max > span {
  block-size: auto !important;
  inset: 0 var(--_end) 0 var(--_start);
  clip-path: none;
  background: currentcolor;
  color: inherit;
  border-radius: 0;
}
.beer .slider.max.vertical > span {
  inset: var(--_end) 0 var(--_start) 0;
}
.beer .slider > input:focus-visible::-webkit-slider-thumb {
  outline: 0.1875rem solid var(--primary);
  outline-offset: 0.25rem;
}
.beer .slider > input:focus-visible::-moz-range-thumb {
  outline: 0.1875rem solid var(--primary);
  outline-offset: 0.25rem;
}
.beer .slider.max > input:focus-visible {
  outline: 0.1875rem solid var(--primary);
  outline-offset: -0.125rem;
}
@media (pointer: coarse) {
  .beer .slider > :hover ~ .tooltip {
    inset-block-start: -1rem !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
}
.beer .snackbar {
  position: fixed;
  inset: auto auto 6rem 50%;
  inline-size: 80%;
  block-size: auto;
  z-index: 200;
  visibility: hidden;
  display: flex;
  box-shadow: var(--elevate2);
  color: var(--inverse-on-surface);
  background-color: var(--inverse-surface);
  padding: 1rem;
  cursor: pointer;
  text-align: start;
  align-items: center;
  border-radius: 0.25rem;
  gap: 0.5rem;
  transition: all var(--speed2);
  transform: translate(-50%, 1rem);
  opacity: 0;
}
.beer .snackbar.top {
  inset: 6rem auto auto 50%;
}
.beer .snackbar:is(.active) {
  visibility: visible;
  transform: translate(-50%, 0);
  opacity: 1;
}
.beer .snackbar:popover-open {
  visibility: visible;
  transform: translate(-50%, 0);
  opacity: 1;
}
.beer .snackbar > .max {
  flex: auto;
}
@media only screen and (min-width: 993px) {
  .beer .snackbar {
    inline-size: 40%;
  }
}
.beer table {
  inline-size: 100%;
  border-spacing: 0;
  font-size: 0.875rem;
  text-align: start;
}
.beer .scroll > table,
.beer table :is(thead, tbody, tfoot, tr, th, td) {
  background-color: inherit;
  color: inherit;
}
.beer :is(th, td) {
  inline-size: auto;
  text-align: inherit;
  padding: 0.5rem;
  border-radius: 0;
}
.beer :is(th, td) > * {
  vertical-align: middle;
}
.beer table.border > tbody > tr:not(:last-child) > td,
.beer thead > tr > th {
  border-block-end: 0.0625rem solid var(--outline);
}
.beer tfoot > tr > th {
  border-block-start: 0.0625rem solid var(--outline);
}
.beer table.stripes > tbody > tr:nth-child(odd) {
  background-color: var(--active);
}
.beer table.no-space :is(th, td) {
  padding: 0;
}
.beer table.medium-space :is(th, td) {
  padding: 0.75rem;
}
.beer table.large-space :is(th, td) {
  padding: 1rem;
}
.beer table > .fixed,
.beer th.fixed {
  position: sticky;
  z-index: 1;
  inset-block-start: 0;
}
.beer tfoot.fixed,
.beer tfoot th.fixed {
  inset-block-end: 0;
}
.beer :is(td, th).min {
  inline-size: 0.1%;
  white-space: nowrap;
}
.beer .tabs {
  display: flex;
  white-space: nowrap;
  border-block-end: 0.0625rem solid var(--surface-variant);
  border-radius: 0;
}
.beer .tabs:not(.left-align, .right-align, .center-align) {
  justify-content: space-around;
}
.beer .tabs > a {
  display: flex;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--on-surface-variant);
  padding: 0.5rem 1rem;
  text-align: center;
  min-block-size: 3rem;
  inline-size: 100%;
  gap: 0.25rem;
}
.beer .tabs.small > a {
  min-block-size: 2rem;
}
.beer .tabs.large > a {
  min-block-size: 4rem;
}
.beer .tabs > a.active,
.beer .tabs > a.active > i {
  color: var(--primary);
}
.beer .tabs > a.active::before {
  content: '';
  position: absolute;
  inset: auto 0 0 0;
  block-size: 0.125rem;
  background-color: var(--primary);
}
.beer .tabs.min > a.active::before {
  margin: 0 auto;
  max-inline-size: min(100%, 4rem);
}
.beer .tabs:is(.left-align, .center-align, .right-align) > a {
  inline-size: auto;
}
.beer .tooltip {
  --_space: -0.5rem;
  visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--inverse-surface);
  color: var(--inverse-on-surface);
  font-size: 0.75rem;
  text-align: center;
  border-radius: 0.25rem;
  padding: 0.5rem;
  position: absolute;
  z-index: 200;
  inset: 0 auto auto 50%;
  inline-size: auto;
  white-space: nowrap;
  font-weight: 500;
  opacity: 0;
  transition: all var(--speed2);
  line-height: normal;
  transform: translate(-50%, -100%) scale(0.9);
}
.beer .tooltip.left {
  inset: 50% auto auto 0;
  transform: translate(-100%, -50%) scale(0.9);
}
.beer .tooltip.right {
  inset: 50% 0 auto auto;
  transform: translate(100%, -50%) scale(0.9);
}
.beer .tooltip.bottom {
  inset: auto auto 0 50%;
  transform: translate(-50%, 100%) scale(0.9);
}
.beer .tooltip.small {
  inline-size: 8rem;
  white-space: normal;
}
.beer .tooltip.medium {
  inline-size: 12rem;
  white-space: normal;
}
.beer .tooltip.large {
  inline-size: 16rem;
  white-space: normal;
}
.beer :hover > .tooltip {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -100%) scale(1);
}
.beer :hover > .tooltip.left {
  transform: translate(-100%, -50%) scale(1);
}
.beer :hover > .tooltip.right {
  transform: translate(100%, -50%) scale(1);
}
.beer :hover > .tooltip.bottom {
  transform: translate(-50%, 100%) scale(1);
}
.beer .tooltip.no-space {
  --_space: 0;
}
.beer .tooltip.medium-space {
  --_space: -1rem;
}
.beer .tooltip.large-space {
  --_space: -1.5rem;
}
.beer .tooltip:not(.left, .right, .bottom) {
  margin-block-start: var(--_space) !important;
}
.beer .tooltip.left,
.beer .tooltip.right {
  margin-inline: var(--_space) !important;
}
.beer .tooltip.bottom {
  margin-block-end: var(--_space) !important;
}
.beer menu:active ~ .tooltip,
.beer :is(button, .button):focus > menu ~ .tooltip,
.beer .field > :focus ~ menu ~ .tooltip {
  visibility: hidden;
}
.beer .slider > .tooltip {
  --_space: -1.25rem;
}
.beer .slider.vertical > .tooltip {
  --_space: -0.75rem;
}
.beer .slider.vertical > .tooltip:is(.left, .right) {
  --_space: -0.5rem;
}
.beer .tooltip.max {
  display: block;
  font-size: inherit;
  white-space: normal;
  text-align: start;
  inline-size: 20rem;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: var(--elevate2);
}
