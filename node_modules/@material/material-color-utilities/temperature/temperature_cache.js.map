{"version": 3, "file": "temperature_cache.js", "sourceRoot": "", "sources": ["temperature_cache.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,0DAA0D;AAE1D,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAClC,OAAO,KAAK,UAAU,MAAM,yBAAyB,CAAC;AACtD,OAAO,KAAK,SAAS,MAAM,wBAAwB,CAAC;AAEpD;;;;;GAKG;AACH,MAAM,OAAO,gBAAgB;IAC3B,YAAmB,KAAU;QAAV,UAAK,GAAL,KAAK,CAAK;QAE7B,oBAAe,GAAU,EAAE,CAAC;QAC5B,mBAAc,GAAU,EAAE,CAAC;QAC3B,oBAAe,GAAG,IAAI,GAAG,EAAe,CAAC;QACzC,kCAA6B,GAAW,CAAC,GAAG,CAAC;QAC7C,oBAAe,GAAa,IAAI,CAAC;IAND,CAAC;IAQjC,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC;QAC3E,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,SAAS,GAAG,EAAE;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7B,IAAI,sBAAsB,GAAG,GAAG,CAAC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,GAAG,GAAG,SAAS,CAAC,kBAAkB,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACvD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAChC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC;YAC5C,QAAQ,GAAG,IAAI,CAAC;YAChB,sBAAsB,IAAI,SAAS,CAAC;SACrC;QACD,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,MAAM,QAAQ,GAAG,sBAAsB,GAAG,SAAS,CAAC;QACpD,IAAI,cAAc,GAAG,GAAG,CAAC;QACzB,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC9C,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,EAAE;YACnC,MAAM,GAAG,GAAG,SAAS,CAAC,kBAAkB,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;YAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAChC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC;YAC5C,cAAc,IAAI,SAAS,CAAC;YAE5B,MAAM,6BAA6B,GAAG,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC;YAClE,IAAI,cAAc,GAAG,cAAc,IAAI,6BAA6B,CAAC;YACrE,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,+DAA+D;YAC/D,mEAAmE;YACnE,sEAAsE;YACtE,qCAAqC;YACrC,EAAE;YACF,qEAAqE;YACrE,uEAAuE;YACvE,cAAc;YACd,OAAO,cAAc,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,EAAE;gBACrD,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpB,MAAM,6BAA6B,GAC/B,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,QAAQ,CAAC,CAAC;gBAClD,cAAc,GAAG,cAAc,IAAI,6BAA6B,CAAC;gBACjE,WAAW,EAAE,CAAC;aACf;YACD,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,EAAE,CAAC;YACZ,IAAI,SAAS,GAAG,GAAG,EAAE;gBACnB,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,EAAE;oBACnC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACrB;gBACD,MAAM;aACP;SACF;QAED,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7B,6DAA6D;QAC7D,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,KAAK,GAAG,CAAC,EAAE;gBAChB,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;aAClC;YACD,IAAI,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE;gBAC7B,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;aAClC;YACD,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SACxC;QAED,sDAAsD;QACtD,MAAM,gBAAgB,GAAG,KAAK,GAAG,gBAAgB,GAAG,CAAC,CAAC;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,OAAO,KAAK,GAAG,CAAC,EAAE;gBAChB,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;aAClC;YACD,IAAI,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE;gBAC7B,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;aAClC;YACD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SAChC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACH,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,EAAE;YAChC,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAE,CAAC;QAEvD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAE,CAAC;QACvD,MAAM,KAAK,GAAG,WAAW,GAAG,WAAW,CAAC;QACxC,MAAM,0BAA0B,GAC5B,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACvE,MAAM,QAAQ,GAAG,0BAA0B,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;QACtE,MAAM,MAAM,GAAG,0BAA0B,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;QACpE,MAAM,mBAAmB,GAAG,GAAG,CAAC;QAChC,IAAI,aAAa,GAAG,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAExD,MAAM,sBAAsB,GAAG,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACnE,yEAAyE;QACzE,8CAA8C;QAC9C,KAAK,IAAI,SAAS,GAAG,GAAG,EAAE,SAAS,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,EAAE;YAC9D,MAAM,GAAG,GAAG,SAAS,CAAC,qBAAqB,CACvC,QAAQ,GAAG,mBAAmB,GAAG,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACtD,SAAS;aACV;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACvD,MAAM,YAAY,GACd,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAE,GAAG,WAAW,CAAC,GAAG,KAAK,CAAC;YACjE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,sBAAsB,GAAG,YAAY,CAAC,CAAC;YAC9D,IAAI,KAAK,GAAG,aAAa,EAAE;gBACzB,aAAa,GAAG,KAAK,CAAC;gBACtB,MAAM,GAAG,cAAc,CAAC;aACzB;SACF;QACD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,GAAQ;QAC1B,MAAM,KAAK,GACP,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAE,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAE,CAAC;QAC5E,MAAM,qBAAqB,GACvB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAE,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAE,CAAC;QACnE,uEAAuE;QACvE,qEAAqE;QACrE,IAAI,KAAK,KAAK,GAAG,EAAE;YACjB,OAAO,GAAG,CAAC;SACZ;QACD,OAAO,qBAAqB,GAAG,KAAK,CAAC;IACvC,CAAC;IAED,0EAA0E;IAC1E,IAAI,wBAAwB;QAC1B,IAAI,IAAI,CAAC,6BAA6B,IAAI,GAAG,EAAE;YAC7C,OAAO,IAAI,CAAC,6BAA6B,CAAC;SAC3C;QAED,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC5C,CAAC;IAED,0EAA0E;IAC1E,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,EAAE;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAe,CAAC;QACjD,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;YACvB,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9D;QACD,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;QACzC,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC,cAAc,CAAC;SAC5B;QACD,MAAM,IAAI,GAAU,EAAE,CAAC;QACvB,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,EAAE,GAAG,IAAI,GAAG,EAAE;YAC5C,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACrE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACvB;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,8EAA8E;IAC9E,MAAM,CAAC,SAAS,CAAC,KAAa,EAAE,CAAS,EAAE,CAAS;QAClD,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,OAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;SACjC;QACD,OAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,MAAM,CAAC,cAAc,CAAC,KAAU;QAC9B,MAAM,GAAG,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,SAAS,CAAC,qBAAqB,CACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,CAAC,GAAG;YACpB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;gBACzB,IAAI,CAAC,GAAG,CACJ,SAAS,CAAC,qBAAqB,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAChE,CAAC;QACV,OAAO,WAAW,CAAC;IACrB,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This file is automatically generated. Do not modify it.\n\nimport {Hct} from '../hct/hct.js';\nimport * as colorUtils from '../utils/color_utils.js';\nimport * as mathUtils from '../utils/math_utils.js';\n\n/**\n * Design utilities using color temperature theory.\n *\n * Analogous colors, complementary color, and cache to efficiently, lazily,\n * generate data for calculations when needed.\n */\nexport class TemperatureCache {\n  constructor(public input: Hct) {}\n\n  hctsByTempCache: Hct[] = [];\n  hctsByHueCache: Hct[] = [];\n  tempsByHctCache = new Map<Hct, number>();\n  inputRelativeTemperatureCache: number = -1.0;\n  complementCache: Hct|null = null;\n\n  get hctsByTemp(): Hct[] {\n    if (this.hctsByTempCache.length > 0) {\n      return this.hctsByTempCache;\n    }\n\n    const hcts = this.hctsByHue.concat([this.input]);\n    const temperaturesByHct = this.tempsByHct;\n    hcts.sort((a, b) => temperaturesByHct.get(a)! - temperaturesByHct.get(b)!);\n    this.hctsByTempCache = hcts;\n    return hcts;\n  }\n\n  get warmest(): Hct {\n    return this.hctsByTemp[this.hctsByTemp.length - 1];\n  }\n\n  get coldest(): Hct {\n    return this.hctsByTemp[0];\n  }\n\n  /**\n   * A set of colors with differing hues, equidistant in temperature.\n   *\n   * In art, this is usually described as a set of 5 colors on a color wheel\n   * divided into 12 sections. This method allows provision of either of those\n   * values.\n   *\n   * Behavior is undefined when [count] or [divisions] is 0.\n   * When divisions < count, colors repeat.\n   *\n   * [count] The number of colors to return, includes the input color.\n   * [divisions] The number of divisions on the color wheel.\n   */\n  analogous(count = 5, divisions = 12): Hct[] {\n    const startHue = Math.round(this.input.hue);\n    const startHct = this.hctsByHue[startHue];\n    let lastTemp = this.relativeTemperature(startHct);\n    const allColors = [startHct];\n\n    let absoluteTotalTempDelta = 0.0;\n    for (let i = 0; i < 360; i++) {\n      const hue = mathUtils.sanitizeDegreesInt(startHue + i);\n      const hct = this.hctsByHue[hue];\n      const temp = this.relativeTemperature(hct);\n      const tempDelta = Math.abs(temp - lastTemp);\n      lastTemp = temp;\n      absoluteTotalTempDelta += tempDelta;\n    }\n    let hueAddend = 1;\n    const tempStep = absoluteTotalTempDelta / divisions;\n    let totalTempDelta = 0.0;\n    lastTemp = this.relativeTemperature(startHct);\n    while (allColors.length < divisions) {\n      const hue = mathUtils.sanitizeDegreesInt(startHue + hueAddend);\n      const hct = this.hctsByHue[hue];\n      const temp = this.relativeTemperature(hct);\n      const tempDelta = Math.abs(temp - lastTemp);\n      totalTempDelta += tempDelta;\n\n      const desiredTotalTempDeltaForIndex = allColors.length * tempStep;\n      let indexSatisfied = totalTempDelta >= desiredTotalTempDeltaForIndex;\n      let indexAddend = 1;\n      // Keep adding this hue to the answers until its temperature is\n      // insufficient. This ensures consistent behavior when there aren't\n      // [divisions] discrete steps between 0 and 360 in hue with [tempStep]\n      // delta in temperature between them.\n      //\n      // For example, white and black have no analogues: there are no other\n      // colors at T100/T0. Therefore, they should just be added to the array\n      // as answers.\n      while (indexSatisfied && allColors.length < divisions) {\n        allColors.push(hct);\n        const desiredTotalTempDeltaForIndex =\n            ((allColors.length + indexAddend) * tempStep);\n        indexSatisfied = totalTempDelta >= desiredTotalTempDeltaForIndex;\n        indexAddend++;\n      }\n      lastTemp = temp;\n      hueAddend++;\n      if (hueAddend > 360) {\n        while (allColors.length < divisions) {\n          allColors.push(hct);\n        }\n        break;\n      }\n    }\n\n    const answers = [this.input];\n\n    // First, generate analogues from rotating counter-clockwise.\n    const increaseHueCount = Math.floor((count - 1) / 2.0);\n    for (let i = 1; i < (increaseHueCount + 1); i++) {\n      let index = 0 - i;\n      while (index < 0) {\n        index = allColors.length + index;\n      }\n      if (index >= allColors.length) {\n        index = index % allColors.length;\n      }\n      answers.splice(0, 0, allColors[index]);\n    }\n\n    // Second, generate analogues from rotating clockwise.\n    const decreaseHueCount = count - increaseHueCount - 1;\n    for (let i = 1; i < (decreaseHueCount + 1); i++) {\n      let index = i;\n      while (index < 0) {\n        index = allColors.length + index;\n      }\n      if (index >= allColors.length) {\n        index = index % allColors.length;\n      }\n      answers.push(allColors[index]);\n    }\n\n    return answers;\n  }\n\n  /**\n   * A color that complements the input color aesthetically.\n   *\n   * In art, this is usually described as being across the color wheel.\n   * History of this shows intent as a color that is just as cool-warm as the\n   * input color is warm-cool.\n   */\n  get complement(): Hct {\n    if (this.complementCache != null) {\n      return this.complementCache;\n    }\n\n    const coldestHue = this.coldest.hue;\n    const coldestTemp = this.tempsByHct.get(this.coldest)!;\n\n    const warmestHue = this.warmest.hue;\n    const warmestTemp = this.tempsByHct.get(this.warmest)!;\n    const range = warmestTemp - coldestTemp;\n    const startHueIsColdestToWarmest =\n        TemperatureCache.isBetween(this.input.hue, coldestHue, warmestHue);\n    const startHue = startHueIsColdestToWarmest ? warmestHue : coldestHue;\n    const endHue = startHueIsColdestToWarmest ? coldestHue : warmestHue;\n    const directionOfRotation = 1.0;\n    let smallestError = 1000.0;\n    let answer = this.hctsByHue[Math.round(this.input.hue)];\n\n    const complementRelativeTemp = 1.0 - this.inputRelativeTemperature;\n    // Find the color in the other section, closest to the inverse percentile\n    // of the input color. This is the complement.\n    for (let hueAddend = 0.0; hueAddend <= 360.0; hueAddend += 1.0) {\n      const hue = mathUtils.sanitizeDegreesDouble(\n          startHue + directionOfRotation * hueAddend);\n      if (!TemperatureCache.isBetween(hue, startHue, endHue)) {\n        continue;\n      }\n      const possibleAnswer = this.hctsByHue[Math.round(hue)];\n      const relativeTemp =\n          (this.tempsByHct.get(possibleAnswer)! - coldestTemp) / range;\n      const error = Math.abs(complementRelativeTemp - relativeTemp);\n      if (error < smallestError) {\n        smallestError = error;\n        answer = possibleAnswer;\n      }\n    }\n    this.complementCache = answer;\n    return this.complementCache;\n  }\n\n  /**\n   * Temperature relative to all colors with the same chroma and tone.\n   * Value on a scale from 0 to 1.\n   */\n  relativeTemperature(hct: Hct): number {\n    const range =\n        this.tempsByHct.get(this.warmest)! - this.tempsByHct.get(this.coldest)!;\n    const differenceFromColdest =\n        this.tempsByHct.get(hct)! - this.tempsByHct.get(this.coldest)!;\n    // Handle when there's no difference in temperature between warmest and\n    // coldest: for example, at T100, only one color is available, white.\n    if (range === 0.0) {\n      return 0.5;\n    }\n    return differenceFromColdest / range;\n  }\n\n  /** Relative temperature of the input color. See [relativeTemperature]. */\n  get inputRelativeTemperature(): number {\n    if (this.inputRelativeTemperatureCache >= 0.0) {\n      return this.inputRelativeTemperatureCache;\n    }\n\n    this.inputRelativeTemperatureCache = this.relativeTemperature(this.input);\n    return this.inputRelativeTemperatureCache;\n  }\n\n  /** A Map with keys of HCTs in [hctsByTemp], values of raw temperature. */\n  get tempsByHct(): Map<Hct, number> {\n    if (this.tempsByHctCache.size > 0) {\n      return this.tempsByHctCache;\n    }\n    const allHcts = this.hctsByHue.concat([this.input]);\n    const temperaturesByHct = new Map<Hct, number>();\n    for (const e of allHcts) {\n      temperaturesByHct.set(e, TemperatureCache.rawTemperature(e));\n    }\n    this.tempsByHctCache = temperaturesByHct;\n    return temperaturesByHct;\n  }\n\n  /**\n   * HCTs for all hues, with the same chroma/tone as the input.\n   * Sorted ascending, hue 0 to 360.\n   */\n  get hctsByHue(): Hct[] {\n    if (this.hctsByHueCache.length > 0) {\n      return this.hctsByHueCache;\n    }\n    const hcts: Hct[] = [];\n    for (let hue = 0.0; hue <= 360.0; hue += 1.0) {\n      const colorAtHue = Hct.from(hue, this.input.chroma, this.input.tone);\n      hcts.push(colorAtHue);\n    }\n    this.hctsByHueCache = hcts;\n    return this.hctsByHueCache;\n  }\n\n  /** Determines if an angle is between two other angles, rotating clockwise. */\n  static isBetween(angle: number, a: number, b: number): boolean {\n    if (a < b) {\n      return a <= angle && angle <= b;\n    }\n    return a <= angle || angle <= b;\n  }\n\n  /**\n   * Value representing cool-warm factor of a color.\n   * Values below 0 are considered cool, above, warm.\n   *\n   * Color science has researched emotion and harmony, which art uses to select\n   * colors. Warm-cool is the foundation of analogous and complementary colors.\n   * See:\n   * - Li-Chen Ou's Chapter 19 in Handbook of Color Psychology (2015).\n   * - Josef Albers' Interaction of Color chapters 19 and 21.\n   *\n   * Implementation of Ou, Woodcock and Wright's algorithm, which uses\n   * L*a*b* / LCH color space.\n   * Return value has these properties:\n   * - Values below 0 are cool, above 0 are warm.\n   * - Lower bound: -0.52 - (chroma ^ 1.07 / 20). L*a*b* chroma is infinite.\n   *   Assuming max of 130 chroma, -9.66.\n   * - Upper bound: -0.52 + (chroma ^ 1.07 / 20). L*a*b* chroma is infinite.\n   *   Assuming max of 130 chroma, 8.61.\n   */\n  static rawTemperature(color: Hct): number {\n    const lab = colorUtils.labFromArgb(color.toInt());\n    const hue = mathUtils.sanitizeDegreesDouble(\n        Math.atan2(lab[2], lab[1]) * 180.0 / Math.PI);\n    const chroma = Math.sqrt((lab[1] * lab[1]) + (lab[2] * lab[2]));\n    const temperature = -0.5 +\n        0.02 * Math.pow(chroma, 1.07) *\n            Math.cos(\n                mathUtils.sanitizeDegreesDouble(hue - 50.0) * Math.PI / 180.0,\n            );\n    return temperature;\n  }\n}\n"]}