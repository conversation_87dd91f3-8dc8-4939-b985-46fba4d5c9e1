{"version": 3, "file": "dislike_analyzer.js", "sourceRoot": "", "sources": ["dislike_analyzer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAElC,uEAAuE;AACvE,4EAA4E;AAC5E,yCAAyC;AACzC,EAAE;AACF,oCAAoC;AAEpC;;;;;;;;GAQG;AACH,MAAM,OAAO,eAAe;IAC1B;;;;;;;OAOG;IACH,MAAM,CAAC,UAAU,CAAC,GAAQ;QACxB,MAAM,SAAS,GACX,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;QAChE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAE/C,OAAO,SAAS,IAAI,YAAY,IAAI,UAAU,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,aAAa,CAAC,GAAQ;QAC3B,IAAI,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACnC,OAAO,GAAG,CAAC,IAAI,CACX,GAAG,CAAC,GAAG,EACP,GAAG,CAAC,MAAM,EACV,IAAI,CACP,CAAC;SACH;QAED,OAAO,GAAG,CAAC;IACb,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\n\n// material_color_utilities is designed to have a consistent API across\n// platforms and modular components that can be moved around easily. Using a\n// class as a namespace facilitates this.\n//\n// tslint:disable:class-as-namespace\n\n/**\n * Check and/or fix universally disliked colors.\n * Color science studies of color preference indicate universal distaste for\n * dark yellow-greens, and also show this is correlated to distate for\n * biological waste and rotting food.\n *\n * See <PERSON> and <PERSON>, 2010 or <PERSON><PERSON><PERSON> and <PERSON>'s Chapter 21 in Handbook\n * of Color Psychology (2015).\n */\nexport class DislikeAnalyzer {\n  /**\n   * Returns true if a color is disliked.\n   *\n   * @param hct A color to be judged.\n   * @return Whether the color is disliked.\n   *\n   * Disliked is defined as a dark yellow-green that is not neutral.\n   */\n  static isDisliked(hct: Hct): boolean {\n    const huePasses =\n        Math.round(hct.hue) >= 90.0 && Math.round(hct.hue) <= 111.0;\n    const chromaPasses = Math.round(hct.chroma) > 16.0;\n    const tonePasses = Math.round(hct.tone) < 65.0;\n\n    return huePasses && chromaPasses && tonePasses;\n  }\n\n  /**\n   * If a color is disliked, lighten it to make it likable.\n   *\n   * @param hct A color to be judged.\n   * @return A new color if the original color is disliked, or the original\n   *   color if it is acceptable.\n   */\n  static fixIfDisliked(hct: Hct): Hct {\n    if (DislikeAnalyzer.isDisliked(hct)) {\n      return Hct.from(\n          hct.hue,\n          hct.chroma,\n          70.0,\n      );\n    }\n\n    return hct;\n  }\n}\n"]}