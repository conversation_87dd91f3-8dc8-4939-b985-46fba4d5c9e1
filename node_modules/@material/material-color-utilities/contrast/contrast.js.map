{"version": 3, "file": "contrast.js", "sourceRoot": "", "sources": ["contrast.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,uEAAuE;AACvE,4EAA4E;AAC5E,yCAAyC;AACzC,EAAE;AACF,oCAAoC;AAEpC,OAAO,KAAK,KAAK,MAAM,yBAAyB,CAAC;AACjD,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAE/C;;;;;;;;;;;GAWG;AACH,MAAM,OAAO,QAAQ;IACnB;;;;;OAKG;IACH,MAAM,CAAC,YAAY,CAAC,KAAa,EAAE,KAAa;QAC9C,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5C,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5C,OAAO,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,EAAU,EAAE,EAAU;QACrC,MAAM,OAAO,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1C,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC;SACb;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC3C,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;QAC7C,IAAI,YAAY,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE;YACxC,OAAO,CAAC,CAAC,CAAC;SACX;QAED,4EAA4E;QAC5E,2CAA2C;QAC3C,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QACnD,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,GAAG,EAAE;YACxC,OAAO,CAAC,CAAC,CAAC;SACX;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,MAAM,CAAC,IAAY,EAAE,KAAa;QACvC,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC;SACb;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;QAC7C,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAEvD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;QAC7C,IAAI,YAAY,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE;YACxC,OAAO,CAAC,CAAC,CAAC;SACX;QAED,4EAA4E;QAC5E,2CAA2C;QAC3C,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;QAClD,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,GAAG,EAAE;YACxC,OAAO,CAAC,CAAC,CAAC;SACX;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,aAAa,CAAC,IAAY,EAAE,KAAa;QAC9C,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAClD,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,YAAY,CAAC,IAAY,EAAE,KAAa;QAC7C,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// material_color_utilities is designed to have a consistent API across\n// platforms and modular components that can be moved around easily. Using a\n// class as a namespace facilitates this.\n//\n// tslint:disable:class-as-namespace\n\nimport * as utils from '../utils/color_utils.js';\nimport * as math from '../utils/math_utils.js';\n\n/**\n * Utility methods for calculating contrast given two colors, or calculating a\n * color given one color and a contrast ratio.\n *\n * Contrast ratio is calculated using XYZ's Y. When linearized to match human\n * perception, Y becomes HC<PERSON>'s tone and L*a*b*'s' L*. Informally, this is the\n * lightness of a color.\n *\n * Methods refer to tone, T in the the HCT color space.\n * Tone is equivalent to L* in the L*a*b* color space, or L in the LCH color\n * space.\n */\nexport class Contrast {\n  /**\n   * Returns a contrast ratio, which ranges from 1 to 21.\n   *\n   * @param toneA Tone between 0 and 100. Values outside will be clamped.\n   * @param toneB Tone between 0 and 100. Values outside will be clamped.\n   */\n  static ratioOfTones(toneA: number, toneB: number): number {\n    toneA = math.clampDouble(0.0, 100.0, toneA);\n    toneB = math.clampDouble(0.0, 100.0, toneB);\n    return Contrast.ratioOfYs(utils.yFromLstar(toneA), utils.yFromLstar(toneB));\n  }\n\n  static ratioOfYs(y1: number, y2: number): number {\n    const lighter = y1 > y2 ? y1 : y2;\n    const darker = (lighter === y2) ? y1 : y2;\n    return (lighter + 5.0) / (darker + 5.0);\n  }\n\n  /**\n   * Returns a tone >= tone parameter that ensures ratio parameter.\n   * Return value is between 0 and 100.\n   * Returns -1 if ratio cannot be achieved with tone parameter.\n   *\n   * @param tone Tone return value must contrast with.\n   * Range is 0 to 100. Invalid values will result in -1 being returned.\n   * @param ratio Contrast ratio of return value and tone.\n   * Range is 1 to 21, invalid values have undefined behavior.\n   */\n  static lighter(tone: number, ratio: number): number {\n    if (tone < 0.0 || tone > 100.0) {\n      return -1.0;\n    }\n\n    const darkY = utils.yFromLstar(tone);\n    const lightY = ratio * (darkY + 5.0) - 5.0;\n    const realContrast = Contrast.ratioOfYs(lightY, darkY);\n    const delta = Math.abs(realContrast - ratio);\n    if (realContrast < ratio && delta > 0.04) {\n      return -1;\n    }\n\n    // Ensure gamut mapping, which requires a 'range' on tone, will still result\n    // the correct ratio by darkening slightly.\n    const returnValue = utils.lstarFromY(lightY) + 0.4;\n    if (returnValue < 0 || returnValue > 100) {\n      return -1;\n    }\n    return returnValue;\n  }\n\n  /**\n   * Returns a tone <= tone parameter that ensures ratio parameter.\n   * Return value is between 0 and 100.\n   * Returns -1 if ratio cannot be achieved with tone parameter.\n   *\n   * @param tone Tone return value must contrast with.\n   * Range is 0 to 100. Invalid values will result in -1 being returned.\n   * @param ratio Contrast ratio of return value and tone.\n   * Range is 1 to 21, invalid values have undefined behavior.\n   */\n  static darker(tone: number, ratio: number): number {\n    if (tone < 0.0 || tone > 100.0) {\n      return -1.0;\n    }\n\n    const lightY = utils.yFromLstar(tone);\n    const darkY = ((lightY + 5.0) / ratio) - 5.0;\n    const realContrast = Contrast.ratioOfYs(lightY, darkY);\n\n    const delta = Math.abs(realContrast - ratio);\n    if (realContrast < ratio && delta > 0.04) {\n      return -1;\n    }\n\n    // Ensure gamut mapping, which requires a 'range' on tone, will still result\n    // the correct ratio by darkening slightly.\n    const returnValue = utils.lstarFromY(darkY) - 0.4;\n    if (returnValue < 0 || returnValue > 100) {\n      return -1;\n    }\n    return returnValue;\n  }\n\n  /**\n   * Returns a tone >= tone parameter that ensures ratio parameter.\n   * Return value is between 0 and 100.\n   * Returns 100 if ratio cannot be achieved with tone parameter.\n   *\n   * This method is unsafe because the returned value is guaranteed to be in\n   * bounds for tone, i.e. between 0 and 100. However, that value may not reach\n   * the ratio with tone. For example, there is no color lighter than T100.\n   *\n   * @param tone Tone return value must contrast with.\n   * Range is 0 to 100. Invalid values will result in 100 being returned.\n   * @param ratio Desired contrast ratio of return value and tone parameter.\n   * Range is 1 to 21, invalid values have undefined behavior.\n   */\n  static lighterUnsafe(tone: number, ratio: number): number {\n    const lighterSafe = Contrast.lighter(tone, ratio);\n    return (lighterSafe < 0.0) ? 100.0 : lighterSafe;\n  }\n\n  /**\n   * Returns a tone >= tone parameter that ensures ratio parameter.\n   * Return value is between 0 and 100.\n   * Returns 100 if ratio cannot be achieved with tone parameter.\n   *\n   * This method is unsafe because the returned value is guaranteed to be in\n   * bounds for tone, i.e. between 0 and 100. However, that value may not reach\n   * the [ratio with [tone]. For example, there is no color darker than T0.\n   *\n   * @param tone Tone return value must contrast with.\n   * Range is 0 to 100. Invalid values will result in 0 being returned.\n   * @param ratio Desired contrast ratio of return value and tone parameter.\n   * Range is 1 to 21, invalid values have undefined behavior.\n   */\n  static darkerUnsafe(tone: number, ratio: number): number {\n    const darkerSafe = Contrast.darker(tone, ratio);\n    return (darkerSafe < 0.0) ? 0.0 : darkerSafe;\n  }\n}"]}