{"version": 3, "file": "blend.js", "sourceRoot": "", "sources": ["blend.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,0DAA0D;AAE1D,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAClC,OAAO,KAAK,UAAU,MAAM,yBAAyB,CAAC;AACtD,OAAO,KAAK,SAAS,MAAM,wBAAwB,CAAC;AAEpD,uEAAuE;AACvE,4EAA4E;AAC5E,yCAAyC;AACzC,EAAE;AACF,oCAAoC;AAEpC;;GAEG;AACH,MAAM,OAAO,KAAK;IAChB;;;;;;;;;;OAUG;IACH,MAAM,CAAC,SAAS,CAAC,WAAmB,EAAE,WAAmB;QACvD,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACvC,MAAM,iBAAiB,GACnB,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;QAChE,MAAM,SAAS,GAAG,SAAS,CAAC,qBAAqB,CAC7C,OAAO,CAAC,GAAG;YACX,eAAe,GAAG,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3E,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IACnE,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,MAAM,CAAC,IAAY,EAAE,EAAU,EAAE,MAAc;QACpD,MAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CACpB,MAAM,CAAC,GAAG,EACV,OAAO,CAAC,MAAM,EACd,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CACjC,CAAC;QACF,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAY,EAAE,EAAU,EAAE,MAAc;QACtD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;QACxB,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;QACxB,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;QACxB,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;QAC7C,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;QAC7C,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;QAC7C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;IACpD,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This file is automatically generated. Do not modify it.\n\nimport {Cam16} from '../hct/cam16.js';\nimport {Hct} from '../hct/hct.js';\nimport * as colorUtils from '../utils/color_utils.js';\nimport * as mathUtils from '../utils/math_utils.js';\n\n// material_color_utilities is designed to have a consistent API across\n// platforms and modular components that can be moved around easily. Using a\n// class as a namespace facilitates this.\n//\n// tslint:disable:class-as-namespace\n\n/**\n * Functions for blending in HCT and CAM16.\n */\nexport class Blend {\n  /**\n   * Blend the design color's HCT hue towards the key color's HCT\n   * hue, in a way that leaves the original color recognizable and\n   * recognizably shifted towards the key color.\n   *\n   * @param designColor ARGB representation of an arbitrary color.\n   * @param sourceColor ARGB representation of the main theme color.\n   * @return The design color with a hue shifted towards the\n   * system's color, a slightly warmer/cooler variant of the design\n   * color's hue.\n   */\n  static harmonize(designColor: number, sourceColor: number): number {\n    const fromHct = Hct.fromInt(designColor);\n    const toHct = Hct.fromInt(sourceColor);\n    const differenceDegrees =\n        mathUtils.differenceDegrees(fromHct.hue, toHct.hue);\n    const rotationDegrees = Math.min(differenceDegrees * 0.5, 15.0);\n    const outputHue = mathUtils.sanitizeDegreesDouble(\n        fromHct.hue +\n        rotationDegrees * mathUtils.rotationDirection(fromHct.hue, toHct.hue));\n    return Hct.from(outputHue, fromHct.chroma, fromHct.tone).toInt();\n  }\n\n  /**\n   * Blends hue from one color into another. The chroma and tone of\n   * the original color are maintained.\n   *\n   * @param from ARGB representation of color\n   * @param to ARGB representation of color\n   * @param amount how much blending to perform; 0.0 >= and <= 1.0\n   * @return from, with a hue blended towards to. Chroma and tone\n   * are constant.\n   */\n  static hctHue(from: number, to: number, amount: number): number {\n    const ucs = Blend.cam16Ucs(from, to, amount);\n    const ucsCam = Cam16.fromInt(ucs);\n    const fromCam = Cam16.fromInt(from);\n    const blended = Hct.from(\n        ucsCam.hue,\n        fromCam.chroma,\n        colorUtils.lstarFromArgb(from),\n    );\n    return blended.toInt();\n  }\n\n  /**\n   * Blend in CAM16-UCS space.\n   *\n   * @param from ARGB representation of color\n   * @param to ARGB representation of color\n   * @param amount how much blending to perform; 0.0 >= and <= 1.0\n   * @return from, blended towards to. Hue, chroma, and tone will\n   * change.\n   */\n  static cam16Ucs(from: number, to: number, amount: number): number {\n    const fromCam = Cam16.fromInt(from);\n    const toCam = Cam16.fromInt(to);\n    const fromJ = fromCam.jstar;\n    const fromA = fromCam.astar;\n    const fromB = fromCam.bstar;\n    const toJ = toCam.jstar;\n    const toA = toCam.astar;\n    const toB = toCam.bstar;\n    const jstar = fromJ + (toJ - fromJ) * amount;\n    const astar = fromA + (toA - fromA) * amount;\n    const bstar = fromB + (toB - fromB) * amount;\n    return Cam16.fromUcs(jstar, astar, bstar).toInt();\n  }\n}\n"]}