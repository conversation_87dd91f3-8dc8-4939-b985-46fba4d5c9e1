{"version": 3, "file": "score.js", "sourceRoot": "", "sources": ["score.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAiB/C,MAAM,qBAAqB,GAAG;IAC5B,OAAO,EAAE,CAAC;IACV,iBAAiB,EAAE,UAAU;IAC7B,MAAM,EAAE,IAAI,EAAG,2BAA2B;CAC3C,CAAC;AAEF,SAAS,OAAO,CAAC,CAA4B,EAAE,CAA4B;IACzE,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;QACrB,OAAO,CAAC,CAAC,CAAC;KACX;SAAM,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;QAC5B,OAAO,CAAC,CAAC;KACV;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,OAAO,KAAK;IAQhB,gBAAuB,CAAC;IAExB;;;;;;;;;;;;OAYG;IACH,MAAM,CAAC,KAAK,CACV,kBAAuC,EAAE,OAAsB;QAE/D,MAAM,EAAC,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAC,GAAG,EAAC,GAAG,qBAAqB,EAAE,GAAG,OAAO,EAAC,CAAC;QACpF,6EAA6E;QAC7E,eAAe;QACf,MAAM,SAAS,GAAU,EAAE,CAAC;QAC5B,MAAM,aAAa,GAAG,IAAI,KAAK,CAAS,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,KAAK,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,kBAAkB,CAAC,OAAO,EAAE,EAAE;YAC7D,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChC,aAAa,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC;YACjC,aAAa,IAAI,UAAU,CAAC;SAC7B;QAED,2EAA2E;QAC3E,MAAM,qBAAqB,GAAG,IAAI,KAAK,CAAS,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClC,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC;YACtD,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBACxC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBAC/C,qBAAqB,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC;aAClD;SACF;QAED,oEAAoE;QACpE,gEAAgE;QAChE,MAAM,SAAS,GAAG,IAAI,KAAK,EAA6B,CAAC;QACzD,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;YAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,MAAM,UAAU,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,IAAI,UAAU,IAAI,KAAK,CAAC,yBAAyB,CAAC,EAAE;gBACjG,SAAS;aACV;YAED,MAAM,eAAe,GAAG,UAAU,GAAG,KAAK,GAAG,KAAK,CAAC,iBAAiB,CAAC;YACrE,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC;YAC9G,MAAM,WAAW,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,YAAY,CAAC;YACtE,MAAM,KAAK,GAAG,eAAe,GAAG,WAAW,CAAC;YAC5C,SAAS,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,KAAK,EAAC,CAAC,CAAC;SAC9B;QACD,uDAAuD;QACvD,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAExB,2EAA2E;QAC3E,yEAAyE;QACzE,wEAAwE;QACxE,qBAAqB;QACrB,MAAM,YAAY,GAAU,EAAE,CAAC;QAC/B,KAAK,IAAI,iBAAiB,GAAG,EAAE,EAAE,iBAAiB,IAAI,EAAE,EAAE,iBAAiB,EAAE,EAAE;YAC7E,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YACxB,KAAK,MAAM,EAAC,GAAG,EAAC,IAAI,SAAS,EAAE;gBAC7B,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACjD,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;gBAC5E,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,YAAY,EAAE;oBACjB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACxB;gBACD,IAAI,YAAY,CAAC,MAAM,IAAI,OAAO;oBAAE,MAAM;aAC3C;YACD,IAAI,YAAY,CAAC,MAAM,IAAI,OAAO;gBAAE,MAAM;SAC3C;QACD,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAChC;QACD,KAAK,MAAM,SAAS,IAAI,YAAY,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;SAChC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;;AA9FuB,mBAAa,GAAG,IAAI,CAAC,CAAE,YAAY;AACnC,uBAAiB,GAAG,GAAG,CAAC;AACxB,yBAAmB,GAAG,GAAG,CAAC;AAC1B,yBAAmB,GAAG,GAAG,CAAC;AAC1B,mBAAa,GAAG,GAAG,CAAC;AACpB,+BAAyB,GAAG,IAAI,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\nimport * as math from '../utils/math_utils.js';\n\n/**\n * Default options for ranking colors based on usage counts.\n * desired: is the max count of the colors returned.\n * fallbackColorARGB: Is the default color that should be used if no\n *                    other colors are suitable.\n * filter: controls if the resulting colors should be filtered to not include\n *         hues that are not used often enough, and colors that are effectively\n *         grayscale.\n */\ndeclare interface ScoreOptions {\n  desired?: number;\n  fallbackColorARGB?: number;\n  filter?: boolean;\n}\n\nconst SCORE_OPTION_DEFAULTS = {\n  desired: 4,  // 4 colors matches what Android wallpaper picker.\n  fallbackColorARGB: 0xff4285f4,  // Google Blue.\n  filter: true,  // Avoid unsuitable colors.\n};\n\nfunction compare(a: {hct: Hct, score: number}, b: {hct: Hct, score: number}): number {\n  if (a.score > b.score) {\n    return -1;\n  } else if (a.score < b.score) {\n    return 1;\n  }\n  return 0;\n}\n\n/**\n *  Given a large set of colors, remove colors that are unsuitable for a UI\n *  theme, and rank the rest based on suitability.\n *\n *  Enables use of a high cluster count for image quantization, thus ensuring\n *  colors aren't muddied, while curating the high cluster count to a much\n *  smaller number of appropriate choices.\n */\nexport class Score {\n  private static readonly TARGET_CHROMA = 48.0;  // A1 Chroma\n  private static readonly WEIGHT_PROPORTION = 0.7;\n  private static readonly WEIGHT_CHROMA_ABOVE = 0.3;\n  private static readonly WEIGHT_CHROMA_BELOW = 0.1;\n  private static readonly CUTOFF_CHROMA = 5.0;\n  private static readonly CUTOFF_EXCITED_PROPORTION = 0.01;\n\n  private constructor() {}\n\n  /**\n   * Given a map with keys of colors and values of how often the color appears,\n   * rank the colors based on suitability for being used for a UI theme.\n   *\n   * @param colorsToPopulation map with keys of colors and values of how often\n   *     the color appears, usually from a source image.\n   * @param {ScoreOptions} options optional parameters.\n   * @return Colors sorted by suitability for a UI theme. The most suitable\n   *     color is the first item, the least suitable is the last. There will\n   *     always be at least one color returned. If all the input colors\n   *     were not suitable for a theme, a default fallback color will be\n   *     provided, Google Blue.\n   */\n  static score(\n    colorsToPopulation: Map<number, number>, options?: ScoreOptions):\n      number[] {\n    const {desired, fallbackColorARGB, filter} = {...SCORE_OPTION_DEFAULTS, ...options};\n    // Get the HCT color for each Argb value, while finding the per hue count and\n    // total count.\n    const colorsHct: Hct[] = [];\n    const huePopulation = new Array<number>(360).fill(0);\n    let populationSum = 0;\n    for (const [argb, population] of colorsToPopulation.entries()) {\n      const hct = Hct.fromInt(argb);\n      colorsHct.push(hct);\n      const hue = Math.floor(hct.hue);\n      huePopulation[hue] += population;\n      populationSum += population;\n    }\n\n    // Hues with more usage in neighboring 30 degree slice get a larger number.\n    const hueExcitedProportions = new Array<number>(360).fill(0.0);\n    for (let hue = 0; hue < 360; hue++) {\n      const proportion = huePopulation[hue] / populationSum;\n      for (let i = hue - 14; i < hue + 16; i++) {\n        const neighborHue = math.sanitizeDegreesInt(i);\n        hueExcitedProportions[neighborHue] += proportion;\n      }\n    }\n\n    // Scores each HCT color based on usage and chroma, while optionally\n    // filtering out values that do not have enough chroma or usage.\n    const scoredHct = new Array<{hct: Hct, score: number}>();\n    for (const hct of colorsHct) {\n      const hue = math.sanitizeDegreesInt(Math.round(hct.hue));\n      const proportion = hueExcitedProportions[hue];\n      if (filter && (hct.chroma < Score.CUTOFF_CHROMA || proportion <= Score.CUTOFF_EXCITED_PROPORTION)) {\n        continue;\n      }\n\n      const proportionScore = proportion * 100.0 * Score.WEIGHT_PROPORTION;\n      const chromaWeight = hct.chroma < Score.TARGET_CHROMA ? Score.WEIGHT_CHROMA_BELOW : Score.WEIGHT_CHROMA_ABOVE;\n      const chromaScore = (hct.chroma - Score.TARGET_CHROMA) * chromaWeight;\n      const score = proportionScore + chromaScore;\n      scoredHct.push({hct, score});\n    }\n    // Sorted so that colors with higher scores come first.\n    scoredHct.sort(compare);\n\n    // Iterates through potential hue differences in degrees in order to select\n    // the colors with the largest distribution of hues possible. Starting at\n    // 90 degrees(maximum difference for 4 colors) then decreasing down to a\n    // 15 degree minimum.\n    const chosenColors: Hct[] = [];\n    for (let differenceDegrees = 90; differenceDegrees >= 15; differenceDegrees--) {\n      chosenColors.length = 0;\n      for (const {hct} of scoredHct) {\n        const duplicateHue = chosenColors.find(chosenHct => {\n          return math.differenceDegrees(hct.hue, chosenHct.hue) < differenceDegrees;\n        });\n        if (!duplicateHue) {\n          chosenColors.push(hct);\n        }\n        if (chosenColors.length >= desired) break;\n      }\n      if (chosenColors.length >= desired) break;\n    }\n    const colors: number[] = [];\n    if (chosenColors.length === 0) {\n      colors.push(fallbackColorARGB);\n    }\n    for (const chosenHct of chosenColors) {\n      colors.push(chosenHct.toInt());\n    }\n    return colors;\n  }\n}\n"]}