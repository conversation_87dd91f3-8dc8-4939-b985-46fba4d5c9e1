{"name": "@material/material-color-utilities", "version": "0.2.7", "publishConfig": {"access": "public"}, "description": "Algorithms and utilities that power the Material Design 3 (M3) color system, including choosing theme colors from images and creating tones of colors; all in a new color space.", "keywords": ["material", "material design", "material3", "material you", "color extraction", "theming"], "repository": {"type": "git", "url": "https://github.com/material-foundation/material-color-utilities.git", "directory": "typescript"}, "author": "Material Eng", "license": "Apache-2.0", "bugs": {"url": "https://github.com/material-foundation/material-color-utilities/issues"}, "homepage": "https://github.com/material-foundation/material-color-utilities/tree/main/typescript", "type": "module", "files": ["**/*.js", "**/*.js.map", "**/*.d.ts", "!**/*_test.*", "!.wireit/**"], "main": "index.js", "module": "index.js", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "default": "./index.js"}}, "scripts": {"build": "wireit", "build:ts": "wireit", "prepublishOnly": "npm run build"}, "wireit": {"build": {"dependencies": ["build:ts"]}, "build:ts": {"command": "tsc --pretty", "files": ["tsconfig.json", "**/*.ts", "!**/*.d.ts"], "output": [".tsbuildinfo", "**/*.js", "**/*.js.map", "**/*.d.ts", "!*.js", "!types/"], "clean": "if-file-deleted"}}, "devDependencies": {"@types/jasmine": "^3.10.3", "@types/node": "^18.7.17", "jasmine": "^4.0.2", "typescript": "^4.5.5", "wireit": "^0.9.5"}}