{"version": 3, "file": "tonal_palette.js", "sourceRoot": "", "sources": ["tonal_palette.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAElC;;;GAGG;AACH,MAAM,OAAO,YAAY;IAGvB;;;OAGG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY;QACzB,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9B,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,OAAO,CAAC,GAAQ;QACrB,OAAO,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAW,EAAE,MAAc;QACjD,OAAO,IAAI,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,YAAY,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;IACjF,CAAC;IAED,YAA6B,GAAW,EAAW,MAAc,EAAW,QAAa;QAA5D,QAAG,GAAH,GAAG,CAAQ;QAAW,WAAM,GAAN,MAAM,CAAQ;QAAW,aAAQ,GAAR,QAAQ,CAAK;QA5BxE,UAAK,GAAG,IAAI,GAAG,EAAkB,CAAC;IA4ByC,CAAC;IAErF,MAAM,CAAC,cAAc,CAAC,GAAW,EAAE,MAAc;QACvD,MAAM,SAAS,GAAG,IAAI,CAAC;QACvB,IAAI,gBAAgB,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QACxD,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;QAC/D,wEAAwE;QACxE,UAAU;QACV,EAAE;QACF,gEAAgE;QAChE,uEAAuE;QACvE,aAAa;QACb,KAAK,IAAI,KAAK,GAAG,GAAG,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,IAAI,GAAG,EAAE;YAChD,sEAAsE;YACtE,yEAAyE;YACzE,uEAAuE;YACvE,mCAAmC;YACnC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;gBAC9D,OAAO,gBAAgB,CAAC;aACzB;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG,KAAK,CAAC,CAAC;YACxD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;YACrD,IAAI,WAAW,GAAG,aAAa,EAAE;gBAC/B,aAAa,GAAG,WAAW,CAAC;gBAC5B,gBAAgB,GAAG,MAAM,CAAC;aAC3B;YAED,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG,KAAK,CAAC,CAAC;YAC7D,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;YAC/D,IAAI,gBAAgB,GAAG,aAAa,EAAE;gBACpC,aAAa,GAAG,gBAAgB,CAAC;gBACjC,gBAAgB,GAAG,WAAW,CAAC;aAChC;SACF;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,IAAI,CAAC,IAAY;QACf,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;YACrD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,IAAY;QACjB,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtC,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\n\n/**\n *  A convenience class for retrieving colors that are constant in hue and\n *  chroma, but vary in tone.\n */\nexport class TonalPalette {\n  private readonly cache = new Map<number, number>();\n\n  /**\n   * @param argb ARGB representation of a color\n   * @return Tones matching that color's hue and chroma.\n   */\n  static fromInt(argb: number): TonalPalette {\n    const hct = Hct.fromInt(argb);\n    return TonalPalette.fromHct(hct);\n  }\n\n  /**\n   * @param hct Hct\n   * @return Tones matching that color's hue and chroma.\n   */\n  static fromHct(hct: Hct) {\n    return new TonalPalette(hct.hue, hct.chroma, hct);\n  }\n\n  /**\n   * @param hue HCT hue\n   * @param chroma HCT chroma\n   * @return Tones matching hue and chroma.\n   */\n  static fromHueAndChroma(hue: number, chroma: number): TonalPalette {\n    return new TonalPalette(hue, chroma, TonalPalette.createKeyColor(hue, chroma));\n  }\n\n  private constructor(readonly hue: number, readonly chroma: number, readonly keyColor: Hct) {}\n\n  private static createKeyColor(hue: number, chroma: number): Hct {\n    const startTone = 50.0;\n    let smallestDeltaHct = Hct.from(hue, chroma, startTone);\n    let smallestDelta = Math.abs(smallestDeltaHct.chroma - chroma);\n    // Starting from T50, check T+/-delta to see if they match the requested\n    // chroma.\n    //\n    // Starts from T50 because T50 has the most chroma available, on\n    // average. Thus it is most likely to have a direct answer and minimize\n    // iteration.\n    for (let delta = 1.0; delta < 50.0; delta += 1.0) {\n      // Termination condition rounding instead of minimizing delta to avoid\n      // case where requested chroma is 16.51, and the closest chroma is 16.49.\n      // Error is minimized, but when rounded and displayed, requested chroma\n      // is 17, key color's chroma is 16.\n      if (Math.round(chroma) === Math.round(smallestDeltaHct.chroma)) {\n        return smallestDeltaHct;\n      }\n\n      const hctAdd = Hct.from(hue, chroma, startTone + delta);\n      const hctAddDelta = Math.abs(hctAdd.chroma - chroma);\n      if (hctAddDelta < smallestDelta) {\n        smallestDelta = hctAddDelta;\n        smallestDeltaHct = hctAdd;\n      }\n\n      const hctSubtract = Hct.from(hue, chroma, startTone - delta);\n      const hctSubtractDelta = Math.abs(hctSubtract.chroma - chroma);\n      if (hctSubtractDelta < smallestDelta) {\n        smallestDelta = hctSubtractDelta;\n        smallestDeltaHct = hctSubtract;\n      }\n    }\n\n    return smallestDeltaHct;\n  }\n\n  /**\n   * @param tone HCT tone, measured from 0 to 100.\n   * @return ARGB representation of a color with that tone.\n   */\n  tone(tone: number): number {\n    let argb = this.cache.get(tone);\n    if (argb === undefined) {\n      argb = Hct.from(this.hue, this.chroma, tone).toInt();\n      this.cache.set(tone, argb);\n    }\n    return argb;\n  }\n\n  /**\n   * @param tone HCT tone.\n   * @return HCT representation of a color with that tone.\n   */\n  getHct(tone: number): Hct {\n    return Hct.fromInt(this.tone(tone));\n  }\n}\n"]}