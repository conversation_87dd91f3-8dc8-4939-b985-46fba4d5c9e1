{"version": 3, "file": "core_palette.js", "sourceRoot": "", "sources": ["core_palette.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAElC,OAAO,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAchD;;;;GAIG;AACH,MAAM,OAAO,WAAW;IAQtB;;OAEG;IACH,MAAM,CAAC,EAAE,CAAC,IAAY;QACpB,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,IAAY;QAC3B,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAyB;QACzC,OAAO,WAAW,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,MAAyB;QAChD,OAAO,WAAW,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAClC,OAAgB,EAChB,MAAyB;QAE3B,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACzD,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;SACnB;QACD,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACpD,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;SACnB;QACD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACjD,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;SACtB;QACD,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACnD,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;SACnB;QACD,IAAI,MAAM,CAAC,cAAc,EAAE;YACzB,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC1D,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;SACnB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,YAAoB,IAAY,EAAE,SAAkB;QAClD,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;QACpB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACvE,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACvE;aAAM;YACL,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YACjD,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\n\nimport {TonalPalette} from './tonal_palette.js';\n\n/**\n * Set of colors to generate a [CorePalette] from\n */\nexport interface CorePaletteColors {\n  primary: number;\n  secondary?: number;\n  tertiary?: number;\n  neutral?: number;\n  neutralVariant?: number;\n  error?: number;\n}\n\n/**\n * An intermediate concept between the key color for a UI theme, and a full\n * color scheme. 5 sets of tones are generated, all except one use the same hue\n * as the key color, and all vary in chroma.\n */\nexport class CorePalette {\n  a1: TonalPalette;\n  a2: TonalPalette;\n  a3: TonalPalette;\n  n1: TonalPalette;\n  n2: TonalPalette;\n  error: TonalPalette;\n\n  /**\n   * @param argb ARGB representation of a color\n   */\n  static of(argb: number): CorePalette {\n    return new CorePalette(argb, false);\n  }\n\n  /**\n   * @param argb ARGB representation of a color\n   */\n  static contentOf(argb: number): CorePalette {\n    return new CorePalette(argb, true);\n  }\n\n  /**\n   * Create a [CorePalette] from a set of colors\n   */\n  static fromColors(colors: CorePaletteColors): CorePalette {\n    return CorePalette.createPaletteFromColors(false, colors);\n  }\n\n  /**\n   * Create a content [CorePalette] from a set of colors\n   */\n  static contentFromColors(colors: CorePaletteColors): CorePalette {\n    return CorePalette.createPaletteFromColors(true, colors);\n  }\n\n  private static createPaletteFromColors(\n      content: boolean,\n      colors: CorePaletteColors,\n  ) {\n    const palette = new CorePalette(colors.primary, content);\n    if (colors.secondary) {\n      const p = new CorePalette(colors.secondary, content);\n      palette.a2 = p.a1;\n    }\n    if (colors.tertiary) {\n      const p = new CorePalette(colors.tertiary, content);\n      palette.a3 = p.a1;\n    }\n    if (colors.error) {\n      const p = new CorePalette(colors.error, content);\n      palette.error = p.a1;\n    }\n    if (colors.neutral) {\n      const p = new CorePalette(colors.neutral, content);\n      palette.n1 = p.n1;\n    }\n    if (colors.neutralVariant) {\n      const p = new CorePalette(colors.neutralVariant, content);\n      palette.n2 = p.n2;\n    }\n    return palette;\n  }\n\n  private constructor(argb: number, isContent: boolean) {\n    const hct = Hct.fromInt(argb);\n    const hue = hct.hue;\n    const chroma = hct.chroma;\n    if (isContent) {\n      this.a1 = TonalPalette.fromHueAndChroma(hue, chroma);\n      this.a2 = TonalPalette.fromHueAndChroma(hue, chroma / 3);\n      this.a3 = TonalPalette.fromHueAndChroma(hue + 60, chroma / 2);\n      this.n1 = TonalPalette.fromHueAndChroma(hue, Math.min(chroma / 12, 4));\n      this.n2 = TonalPalette.fromHueAndChroma(hue, Math.min(chroma / 6, 8));\n    } else {\n      this.a1 = TonalPalette.fromHueAndChroma(hue, Math.max(48, chroma));\n      this.a2 = TonalPalette.fromHueAndChroma(hue, 16);\n      this.a3 = TonalPalette.fromHueAndChroma(hue + 60, 24);\n      this.n1 = TonalPalette.fromHueAndChroma(hue, 4);\n      this.n2 = TonalPalette.fromHueAndChroma(hue, 8);\n    }\n    this.error = TonalPalette.fromHueAndChroma(25, 84);\n  }\n}\n"]}