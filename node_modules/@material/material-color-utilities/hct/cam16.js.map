{"version": 3, "file": "cam16.js", "sourceRoot": "", "sources": ["cam16.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAK,KAAK,MAAM,yBAAyB,CAAC;AACjD,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAE/C,OAAO,EAAC,iBAAiB,EAAC,MAAM,yBAAyB,CAAC;AAE1D;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,KAAK;IAChB;;;;;;;;;;;;;;;;;;;OAmBG;IACH,YACa,GAAW,EAAW,MAAc,EAAW,CAAS,EACxD,CAAS,EAAW,CAAS,EAAW,CAAS,EACjD,KAAa,EAAW,KAAa,EAAW,KAAa;QAF7D,QAAG,GAAH,GAAG,CAAQ;QAAW,WAAM,GAAN,MAAM,CAAQ;QAAW,MAAC,GAAD,CAAC,CAAQ;QACxD,MAAC,GAAD,CAAC,CAAQ;QAAW,MAAC,GAAD,CAAC,CAAQ;QAAW,MAAC,GAAD,CAAC,CAAQ;QACjD,UAAK,GAAL,KAAK,CAAQ;QAAW,UAAK,GAAL,KAAK,CAAQ;QAAW,UAAK,GAAL,KAAK,CAAQ;IAAG,CAAC;IAE9E;;;;OAIG;IACH,QAAQ,CAAC,KAAY;QACnB,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACpC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACpC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACvD,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY;QACzB,OAAO,KAAK,CAAC,0BAA0B,CAAC,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,0BAA0B,CAC7B,IAAY,EAAE,iBAAoC;QACpD,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;QACtC,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,CAAC,GAAG,UAAU,GAAG,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,UAAU,GAAG,KAAK,CAAC;QACvE,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QAC3D,MAAM,CAAC,GAAG,UAAU,GAAG,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,UAAU,GAAG,KAAK,CAAC;QAEvE,MAAM,EAAE,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;QACtD,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;QACvD,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;QAEvD,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAC1C,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAC1C,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAE1C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;QAC1E,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;QAC1E,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;QAE1E,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QAC3D,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QAC3D,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QAE3D,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAC/C,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;QACrC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QACrD,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,MAAM,WAAW,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QAC9C,MAAM,GAAG,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;YAC/C,WAAW,IAAI,GAAG,CAAM,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;gBACrB,WAAW,CAAC;QAC1C,MAAM,UAAU,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;QAE3C,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QACtC,MAAM,CAAC,GAAG,KAAK;YACX,IAAI,CAAC,GAAG,CACJ,EAAE,GAAG,iBAAiB,CAAC,EAAE,EACzB,iBAAiB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;YACxD,CAAC,iBAAiB,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QAC5D,MAAM,QAAQ,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC/C,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACzE,MAAM,EAAE,GACJ,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QAC3E,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC/D,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QACvC,MAAM,CAAC,GAAG,IAAI;YACV,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;QAC5E,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1D,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE3C,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC5C,OAAO,KAAK,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,0BAA0B,CAC7B,CAAS,EAAE,CAAS,EAAE,CAAS,EAC/B,iBAAoC;QACtC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;YACxD,CAAC,iBAAiB,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QAC5D,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QACvC,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,IAAI;YACV,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;QAC5E,MAAM,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;QACzC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1D,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3C,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,OAAO,CAAC,KAAa,EAAE,KAAa,EAAE,KAAa;QACxD,OAAO,KAAK,CAAC,0BAA0B,CACnC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,0BAA0B,CAC7B,KAAa,EAAE,KAAa,EAAE,KAAa,EAC3C,iBAAoC;QACtC,MAAM,CAAC,GAAG,KAAK,CAAC;QAChB,MAAM,CAAC,GAAG,KAAK,CAAC;QAChB,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC;QAChD,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,GAAG,EAAE;YACX,CAAC,IAAI,KAAK,CAAC;SACZ;QACD,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QAC9C,OAAO,KAAK,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC;IACtE,CAAC;IAED;;;;OAIG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,iBAAoC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;YACjD,GAAG,CAAC,CAAC;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAE5C,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CACd,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAClE,GAAG,GAAG,GAAG,CAAC,CAAC;QACf,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;QAE1C,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,iBAAiB,CAAC,EAAE;YAC3B,IAAI,CAAC,GAAG,CACJ,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,GAAG,GAAG,iBAAiB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACzE,MAAM,EAAE,GACJ,IAAI,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QAC3E,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QAEtC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE5B,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YACnC,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QACrD,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;QACvB,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;QACvB,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QACzD,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QACzD,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QAE1D,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,iBAAiB,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,iBAAiB,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,iBAAiB,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjC,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE1C,MAAM,CAAC,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;QAC9D,MAAM,CAAC,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;QAC9D,MAAM,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;QAE/D,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,8EAA8E;IAC9E,UAAU;IACV,MAAM,CAAC,0BAA0B,CAC7B,CAAS,EAAE,CAAS,EAAE,CAAS,EAC/B,iBAAoC;QACtC,0CAA0C;QAE1C,MAAM,EAAE,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;QACtD,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;QACvD,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;QAEvD,sBAAsB;QACtB,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAC1C,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAC1C,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAE1C,uBAAuB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QACzD,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QACzD,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QAEzD,oBAAoB;QACpB,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAC/C,sBAAsB;QACtB,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;QAErC,uBAAuB;QACvB,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QACrD,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAE/C,MAAM;QACN,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,MAAM,WAAW,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;YAC/C,WAAW,IAAI,GAAG,CAAM,CAAC,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;gBACnB,WAAW,CAAC;QAC1C,MAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;QAEzC,+BAA+B;QAC/B,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QAEtC,iCAAiC;QACjC,MAAM,CAAC,GAAG,KAAK;YACX,IAAI,CAAC,GAAG,CACJ,EAAE,GAAG,iBAAiB,CAAC,EAAE,EACzB,iBAAiB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;YACxD,CAAC,iBAAiB,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE9D,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACjD,MAAM,IAAI,GACN,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACrE,MAAM,EAAE,GACJ,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QACzE,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC/D,qCAAqC;QACrC,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QACvC,MAAM,CAAC,GAAG,IAAI;YACV,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;QAE5E,uBAAuB;QACvB,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QAClD,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3C,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,4DAA4D;IAC5D,sBAAsB,CAAC,iBAAoC;QACzD,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACnD,GAAG,CAAC,CAAC;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAE5C,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CACd,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAClE,GAAG,GAAG,GAAG,CAAC,CAAC;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;QAExC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,iBAAiB,CAAC,EAAE;YAC3B,IAAI,CAAC,GAAG,CACJ,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,GAAG,GAAG,iBAAiB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACzE,MAAM,EAAE,GACJ,IAAI,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QAE3E,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAExC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE5B,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC;YACjC,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QACnD,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;QACvB,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;QACvB,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QACzD,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QACzD,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QAE1D,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,iBAAiB,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,iBAAiB,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,iBAAiB,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjC,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE1C,MAAM,CAAC,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;QAC9D,MAAM,CAAC,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;QAC9D,MAAM,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,CAAC;QAE/D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as utils from '../utils/color_utils.js';\nimport * as math from '../utils/math_utils.js';\n\nimport {ViewingConditions} from './viewing_conditions.js';\n\n/**\n * CAM16, a color appearance model. Colors are not just defined by their hex\n * code, but rather, a hex code and viewing conditions.\n *\n * CAM16 instances also have coordinates in the CAM16-UCS space, called J*, a*,\n * b*, or jstar, astar, bstar in code. CAM16-UCS is included in the CAM16\n * specification, and should be used when measuring distances between colors.\n *\n * In traditional color spaces, a color can be identified solely by the\n * observer's measurement of the color. Color appearance models such as CAM16\n * also use information about the environment where the color was\n * observed, known as the viewing conditions.\n *\n * For example, white under the traditional assumption of a midday sun white\n * point is accurately measured as a slightly chromatic blue by CAM16. (roughly,\n * hue 203, chroma 3, lightness 100)\n */\nexport class Cam16 {\n  /**\n   * All of the CAM16 dimensions can be calculated from 3 of the dimensions, in\n   * the following combinations:\n   *      -  {j or q} and {c, m, or s} and hue\n   *      - jstar, astar, bstar\n   * Prefer using a static method that constructs from 3 of those dimensions.\n   * This constructor is intended for those methods to use to return all\n   * possible dimensions.\n   *\n   * @param hue\n   * @param chroma informally, colorfulness / color intensity. like saturation\n   *     in HSL, except perceptually accurate.\n   * @param j lightness\n   * @param q brightness; ratio of lightness to white point's lightness\n   * @param m colorfulness\n   * @param s saturation; ratio of chroma to white point's chroma\n   * @param jstar CAM16-UCS J coordinate\n   * @param astar CAM16-UCS a coordinate\n   * @param bstar CAM16-UCS b coordinate\n   */\n  constructor(\n      readonly hue: number, readonly chroma: number, readonly j: number,\n      readonly q: number, readonly m: number, readonly s: number,\n      readonly jstar: number, readonly astar: number, readonly bstar: number) {}\n\n  /**\n   * CAM16 instances also have coordinates in the CAM16-UCS space, called J*,\n   * a*, b*, or jstar, astar, bstar in code. CAM16-UCS is included in the CAM16\n   * specification, and is used to measure distances between colors.\n   */\n  distance(other: Cam16): number {\n    const dJ = this.jstar - other.jstar;\n    const dA = this.astar - other.astar;\n    const dB = this.bstar - other.bstar;\n    const dEPrime = Math.sqrt(dJ * dJ + dA * dA + dB * dB);\n    const dE = 1.41 * Math.pow(dEPrime, 0.63);\n    return dE;\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return CAM16 color, assuming the color was viewed in default viewing\n   *     conditions.\n   */\n  static fromInt(argb: number): Cam16 {\n    return Cam16.fromIntInViewingConditions(argb, ViewingConditions.DEFAULT);\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @param viewingConditions Information about the environment where the color\n   *     was observed.\n   * @return CAM16 color.\n   */\n  static fromIntInViewingConditions(\n      argb: number, viewingConditions: ViewingConditions): Cam16 {\n    const red = (argb & 0x00ff0000) >> 16;\n    const green = (argb & 0x0000ff00) >> 8;\n    const blue = (argb & 0x000000ff);\n    const redL = utils.linearized(red);\n    const greenL = utils.linearized(green);\n    const blueL = utils.linearized(blue);\n    const x = 0.41233895 * redL + 0.35762064 * greenL + 0.18051042 * blueL;\n    const y = 0.2126 * redL + 0.7152 * greenL + 0.0722 * blueL;\n    const z = 0.01932141 * redL + 0.11916382 * greenL + 0.95034478 * blueL;\n\n    const rC = 0.401288 * x + 0.650173 * y - 0.051461 * z;\n    const gC = -0.250268 * x + 1.204414 * y + 0.045854 * z;\n    const bC = -0.002079 * x + 0.048952 * y + 0.953127 * z;\n\n    const rD = viewingConditions.rgbD[0] * rC;\n    const gD = viewingConditions.rgbD[1] * gC;\n    const bD = viewingConditions.rgbD[2] * bC;\n\n    const rAF = Math.pow((viewingConditions.fl * Math.abs(rD)) / 100.0, 0.42);\n    const gAF = Math.pow((viewingConditions.fl * Math.abs(gD)) / 100.0, 0.42);\n    const bAF = Math.pow((viewingConditions.fl * Math.abs(bD)) / 100.0, 0.42);\n\n    const rA = (math.signum(rD) * 400.0 * rAF) / (rAF + 27.13);\n    const gA = (math.signum(gD) * 400.0 * gAF) / (gAF + 27.13);\n    const bA = (math.signum(bD) * 400.0 * bAF) / (bAF + 27.13);\n\n    const a = (11.0 * rA + -12.0 * gA + bA) / 11.0;\n    const b = (rA + gA - 2.0 * bA) / 9.0;\n    const u = (20.0 * rA + 20.0 * gA + 21.0 * bA) / 20.0;\n    const p2 = (40.0 * rA + 20.0 * gA + bA) / 20.0;\n    const atan2 = Math.atan2(b, a);\n    const atanDegrees = (atan2 * 180.0) / Math.PI;\n    const hue = atanDegrees < 0 ? atanDegrees + 360.0 :\n        atanDegrees >= 360      ? atanDegrees - 360.0 :\n                                  atanDegrees;\n    const hueRadians = (hue * Math.PI) / 180.0;\n\n    const ac = p2 * viewingConditions.nbb;\n    const j = 100.0 *\n        Math.pow(\n            ac / viewingConditions.aw,\n            viewingConditions.c * viewingConditions.z);\n    const q = (4.0 / viewingConditions.c) * Math.sqrt(j / 100.0) *\n        (viewingConditions.aw + 4.0) * viewingConditions.fLRoot;\n    const huePrime = hue < 20.14 ? hue + 360 : hue;\n    const eHue = 0.25 * (Math.cos((huePrime * Math.PI) / 180.0 + 2.0) + 3.8);\n    const p1 =\n        (50000.0 / 13.0) * eHue * viewingConditions.nc * viewingConditions.ncb;\n    const t = (p1 * Math.sqrt(a * a + b * b)) / (u + 0.305);\n    const alpha = Math.pow(t, 0.9) *\n        Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73);\n    const c = alpha * Math.sqrt(j / 100.0);\n    const m = c * viewingConditions.fLRoot;\n    const s = 50.0 *\n        Math.sqrt((alpha * viewingConditions.c) / (viewingConditions.aw + 4.0));\n    const jstar = ((1.0 + 100.0 * 0.007) * j) / (1.0 + 0.007 * j);\n    const mstar = (1.0 / 0.0228) * Math.log(1.0 + 0.0228 * m);\n    const astar = mstar * Math.cos(hueRadians);\n    const bstar = mstar * Math.sin(hueRadians);\n\n    return new Cam16(hue, c, j, q, m, s, jstar, astar, bstar);\n  }\n\n  /**\n   * @param j CAM16 lightness\n   * @param c CAM16 chroma\n   * @param h CAM16 hue\n   */\n  static fromJch(j: number, c: number, h: number): Cam16 {\n    return Cam16.fromJchInViewingConditions(j, c, h, ViewingConditions.DEFAULT);\n  }\n\n  /**\n   * @param j CAM16 lightness\n   * @param c CAM16 chroma\n   * @param h CAM16 hue\n   * @param viewingConditions Information about the environment where the color\n   *     was observed.\n   */\n  static fromJchInViewingConditions(\n      j: number, c: number, h: number,\n      viewingConditions: ViewingConditions): Cam16 {\n    const q = (4.0 / viewingConditions.c) * Math.sqrt(j / 100.0) *\n        (viewingConditions.aw + 4.0) * viewingConditions.fLRoot;\n    const m = c * viewingConditions.fLRoot;\n    const alpha = c / Math.sqrt(j / 100.0);\n    const s = 50.0 *\n        Math.sqrt((alpha * viewingConditions.c) / (viewingConditions.aw + 4.0));\n    const hueRadians = (h * Math.PI) / 180.0;\n    const jstar = ((1.0 + 100.0 * 0.007) * j) / (1.0 + 0.007 * j);\n    const mstar = (1.0 / 0.0228) * Math.log(1.0 + 0.0228 * m);\n    const astar = mstar * Math.cos(hueRadians);\n    const bstar = mstar * Math.sin(hueRadians);\n    return new Cam16(h, c, j, q, m, s, jstar, astar, bstar);\n  }\n\n  /**\n   * @param jstar CAM16-UCS lightness.\n   * @param astar CAM16-UCS a dimension. Like a* in L*a*b*, it is a Cartesian\n   *     coordinate on the Y axis.\n   * @param bstar CAM16-UCS b dimension. Like a* in L*a*b*, it is a Cartesian\n   *     coordinate on the X axis.\n   */\n  static fromUcs(jstar: number, astar: number, bstar: number): Cam16 {\n    return Cam16.fromUcsInViewingConditions(\n        jstar, astar, bstar, ViewingConditions.DEFAULT);\n  }\n\n  /**\n   * @param jstar CAM16-UCS lightness.\n   * @param astar CAM16-UCS a dimension. Like a* in L*a*b*, it is a Cartesian\n   *     coordinate on the Y axis.\n   * @param bstar CAM16-UCS b dimension. Like a* in L*a*b*, it is a Cartesian\n   *     coordinate on the X axis.\n   * @param viewingConditions Information about the environment where the color\n   *     was observed.\n   */\n  static fromUcsInViewingConditions(\n      jstar: number, astar: number, bstar: number,\n      viewingConditions: ViewingConditions): Cam16 {\n    const a = astar;\n    const b = bstar;\n    const m = Math.sqrt(a * a + b * b);\n    const M = (Math.exp(m * 0.0228) - 1.0) / 0.0228;\n    const c = M / viewingConditions.fLRoot;\n    let h = Math.atan2(b, a) * (180.0 / Math.PI);\n    if (h < 0.0) {\n      h += 360.0;\n    }\n    const j = jstar / (1 - (jstar - 100) * 0.007);\n    return Cam16.fromJchInViewingConditions(j, c, h, viewingConditions);\n  }\n\n  /**\n   *  @return ARGB representation of color, assuming the color was viewed in\n   *     default viewing conditions, which are near-identical to the default\n   *     viewing conditions for sRGB.\n   */\n  toInt(): number {\n    return this.viewed(ViewingConditions.DEFAULT);\n  }\n\n  /**\n   * @param viewingConditions Information about the environment where the color\n   *     will be viewed.\n   * @return ARGB representation of color\n   */\n  viewed(viewingConditions: ViewingConditions): number {\n    const alpha = this.chroma === 0.0 || this.j === 0.0 ?\n        0.0 :\n        this.chroma / Math.sqrt(this.j / 100.0);\n\n    const t = Math.pow(\n        alpha / Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73),\n        1.0 / 0.9);\n    const hRad = (this.hue * Math.PI) / 180.0;\n\n    const eHue = 0.25 * (Math.cos(hRad + 2.0) + 3.8);\n    const ac = viewingConditions.aw *\n        Math.pow(\n            this.j / 100.0, 1.0 / viewingConditions.c / viewingConditions.z);\n    const p1 =\n        eHue * (50000.0 / 13.0) * viewingConditions.nc * viewingConditions.ncb;\n    const p2 = ac / viewingConditions.nbb;\n\n    const hSin = Math.sin(hRad);\n    const hCos = Math.cos(hRad);\n\n    const gamma = (23.0 * (p2 + 0.305) * t) /\n        (23.0 * p1 + 11.0 * t * hCos + 108.0 * t * hSin);\n    const a = gamma * hCos;\n    const b = gamma * hSin;\n    const rA = (460.0 * p2 + 451.0 * a + 288.0 * b) / 1403.0;\n    const gA = (460.0 * p2 - 891.0 * a - 261.0 * b) / 1403.0;\n    const bA = (460.0 * p2 - 220.0 * a - 6300.0 * b) / 1403.0;\n\n    const rCBase = Math.max(0, (27.13 * Math.abs(rA)) / (400.0 - Math.abs(rA)));\n    const rC = math.signum(rA) * (100.0 / viewingConditions.fl) *\n        Math.pow(rCBase, 1.0 / 0.42);\n    const gCBase = Math.max(0, (27.13 * Math.abs(gA)) / (400.0 - Math.abs(gA)));\n    const gC = math.signum(gA) * (100.0 / viewingConditions.fl) *\n        Math.pow(gCBase, 1.0 / 0.42);\n    const bCBase = Math.max(0, (27.13 * Math.abs(bA)) / (400.0 - Math.abs(bA)));\n    const bC = math.signum(bA) * (100.0 / viewingConditions.fl) *\n        Math.pow(bCBase, 1.0 / 0.42);\n    const rF = rC / viewingConditions.rgbD[0];\n    const gF = gC / viewingConditions.rgbD[1];\n    const bF = bC / viewingConditions.rgbD[2];\n\n    const x = 1.86206786 * rF - 1.01125463 * gF + 0.14918677 * bF;\n    const y = 0.38752654 * rF + 0.62144744 * gF - 0.00897398 * bF;\n    const z = -0.01584150 * rF - 0.03412294 * gF + 1.04996444 * bF;\n\n    const argb = utils.argbFromXyz(x, y, z);\n    return argb;\n  }\n\n  /// Given color expressed in XYZ and viewed in [viewingConditions], convert to\n  /// CAM16.\n  static fromXyzInViewingConditions(\n      x: number, y: number, z: number,\n      viewingConditions: ViewingConditions): Cam16 {\n    // Transform XYZ to 'cone'/'rgb' responses\n\n    const rC = 0.401288 * x + 0.650173 * y - 0.051461 * z;\n    const gC = -0.250268 * x + 1.204414 * y + 0.045854 * z;\n    const bC = -0.002079 * x + 0.048952 * y + 0.953127 * z;\n\n    // Discount illuminant\n    const rD = viewingConditions.rgbD[0] * rC;\n    const gD = viewingConditions.rgbD[1] * gC;\n    const bD = viewingConditions.rgbD[2] * bC;\n\n    // chromatic adaptation\n    const rAF = Math.pow(viewingConditions.fl * Math.abs(rD) / 100.0, 0.42);\n    const gAF = Math.pow(viewingConditions.fl * Math.abs(gD) / 100.0, 0.42);\n    const bAF = Math.pow(viewingConditions.fl * Math.abs(bD) / 100.0, 0.42);\n    const rA = math.signum(rD) * 400.0 * rAF / (rAF + 27.13);\n    const gA = math.signum(gD) * 400.0 * gAF / (gAF + 27.13);\n    const bA = math.signum(bD) * 400.0 * bAF / (bAF + 27.13);\n\n    // redness-greenness\n    const a = (11.0 * rA + -12.0 * gA + bA) / 11.0;\n    // yellowness-blueness\n    const b = (rA + gA - 2.0 * bA) / 9.0;\n\n    // auxiliary components\n    const u = (20.0 * rA + 20.0 * gA + 21.0 * bA) / 20.0;\n    const p2 = (40.0 * rA + 20.0 * gA + bA) / 20.0;\n\n    // hue\n    const atan2 = Math.atan2(b, a);\n    const atanDegrees = atan2 * 180.0 / Math.PI;\n    const hue = atanDegrees < 0 ? atanDegrees + 360.0 :\n        atanDegrees >= 360      ? atanDegrees - 360 :\n                                  atanDegrees;\n    const hueRadians = hue * Math.PI / 180.0;\n\n    // achromatic response to color\n    const ac = p2 * viewingConditions.nbb;\n\n    // CAM16 lightness and brightness\n    const J = 100.0 *\n        Math.pow(\n            ac / viewingConditions.aw,\n            viewingConditions.c * viewingConditions.z);\n    const Q = (4.0 / viewingConditions.c) * Math.sqrt(J / 100.0) *\n        (viewingConditions.aw + 4.0) * (viewingConditions.fLRoot);\n\n    const huePrime = (hue < 20.14) ? hue + 360 : hue;\n    const eHue =\n        (1.0 / 4.0) * (Math.cos(huePrime * Math.PI / 180.0 + 2.0) + 3.8);\n    const p1 =\n        50000.0 / 13.0 * eHue * viewingConditions.nc * viewingConditions.ncb;\n    const t = p1 * Math.sqrt(a * a + b * b) / (u + 0.305);\n    const alpha = Math.pow(t, 0.9) *\n        Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73);\n    // CAM16 chroma, colorfulness, chroma\n    const C = alpha * Math.sqrt(J / 100.0);\n    const M = C * viewingConditions.fLRoot;\n    const s = 50.0 *\n        Math.sqrt((alpha * viewingConditions.c) / (viewingConditions.aw + 4.0));\n\n    // CAM16-UCS components\n    const jstar = (1.0 + 100.0 * 0.007) * J / (1.0 + 0.007 * J);\n    const mstar = Math.log(1.0 + 0.0228 * M) / 0.0228;\n    const astar = mstar * Math.cos(hueRadians);\n    const bstar = mstar * Math.sin(hueRadians);\n    return new Cam16(hue, C, J, Q, M, s, jstar, astar, bstar);\n  }\n\n  /// XYZ representation of CAM16 seen in [viewingConditions].\n  xyzInViewingConditions(viewingConditions: ViewingConditions): number[] {\n    const alpha = (this.chroma === 0.0 || this.j === 0.0) ?\n        0.0 :\n        this.chroma / Math.sqrt(this.j / 100.0);\n\n    const t = Math.pow(\n        alpha / Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73),\n        1.0 / 0.9);\n    const hRad = this.hue * Math.PI / 180.0;\n\n    const eHue = 0.25 * (Math.cos(hRad + 2.0) + 3.8);\n    const ac = viewingConditions.aw *\n        Math.pow(\n            this.j / 100.0, 1.0 / viewingConditions.c / viewingConditions.z);\n    const p1 =\n        eHue * (50000.0 / 13.0) * viewingConditions.nc * viewingConditions.ncb;\n\n    const p2 = (ac / viewingConditions.nbb);\n\n    const hSin = Math.sin(hRad);\n    const hCos = Math.cos(hRad);\n\n    const gamma = 23.0 * (p2 + 0.305) * t /\n        (23.0 * p1 + 11 * t * hCos + 108.0 * t * hSin);\n    const a = gamma * hCos;\n    const b = gamma * hSin;\n    const rA = (460.0 * p2 + 451.0 * a + 288.0 * b) / 1403.0;\n    const gA = (460.0 * p2 - 891.0 * a - 261.0 * b) / 1403.0;\n    const bA = (460.0 * p2 - 220.0 * a - 6300.0 * b) / 1403.0;\n\n    const rCBase = Math.max(0, (27.13 * Math.abs(rA)) / (400.0 - Math.abs(rA)));\n    const rC = math.signum(rA) * (100.0 / viewingConditions.fl) *\n        Math.pow(rCBase, 1.0 / 0.42);\n    const gCBase = Math.max(0, (27.13 * Math.abs(gA)) / (400.0 - Math.abs(gA)));\n    const gC = math.signum(gA) * (100.0 / viewingConditions.fl) *\n        Math.pow(gCBase, 1.0 / 0.42);\n    const bCBase = Math.max(0, (27.13 * Math.abs(bA)) / (400.0 - Math.abs(bA)));\n    const bC = math.signum(bA) * (100.0 / viewingConditions.fl) *\n        Math.pow(bCBase, 1.0 / 0.42);\n    const rF = rC / viewingConditions.rgbD[0];\n    const gF = gC / viewingConditions.rgbD[1];\n    const bF = bC / viewingConditions.rgbD[2];\n\n    const x = 1.86206786 * rF - 1.01125463 * gF + 0.14918677 * bF;\n    const y = 0.38752654 * rF + 0.62144744 * gF - 0.00897398 * bF;\n    const z = -0.01584150 * rF - 0.03412294 * gF + 1.04996444 * bF;\n\n    return [x, y, z];\n  }\n}\n"]}