{"version": 3, "file": "viewing_conditions.js", "sourceRoot": "", "sources": ["viewing_conditions.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAK,KAAK,MAAM,yBAAyB,CAAC;AACjD,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAE/C;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,iBAAiB;IAI5B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,MAAM,CAAC,IAAI,CACP,UAAU,GAAG,KAAK,CAAC,aAAa,EAAE,EAClC,iBAAiB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,EACtE,eAAe,GAAG,IAAI,EAAE,QAAQ,GAAG,GAAG,EACtC,qBAAqB,GAAG,KAAK;QAC/B,MAAM,GAAG,GAAG,UAAU,CAAC;QACvB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;QACtE,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;QACtE,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;QACtE,MAAM,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC;QAChC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,GAAG,qBAAqB,CAAC,CAAC;YAC3B,GAAG,CAAC,CAAC;YACL,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3E,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,EAAE,GAAG,CAAC,CAAC;QACb,MAAM,IAAI,GAAG;YACX,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;YAC1B,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;YAC1B,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;SAC3B,CAAC;QACF,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,iBAAiB,GAAG,GAAG,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;QACrB,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB;YAC7B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,iBAAiB,CAAC,CAAC;QACzD,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,GAAG,CAAC;QAChB,MAAM,WAAW,GAAG;YAClB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC;YAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC;YAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC;SAC5C,CAAC;QACF,MAAM,IAAI,GAAG;YACX,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACnD,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACnD,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;SACpD,CAAC;QACF,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAC5D,OAAO,IAAI,iBAAiB,CACxB,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;OAMG;IACH,YACW,CAAS,EAAS,EAAU,EAAS,GAAW,EAChD,GAAW,EAAS,CAAS,EAAS,EAAU,EAChD,IAAc,EAAS,EAAU,EAAS,MAAc,EACxD,CAAS;QAHT,MAAC,GAAD,CAAC,CAAQ;QAAS,OAAE,GAAF,EAAE,CAAQ;QAAS,QAAG,GAAH,GAAG,CAAQ;QAChD,QAAG,GAAH,GAAG,CAAQ;QAAS,MAAC,GAAD,CAAC,CAAQ;QAAS,OAAE,GAAF,EAAE,CAAQ;QAChD,SAAI,GAAJ,IAAI,CAAU;QAAS,OAAE,GAAF,EAAE,CAAQ;QAAS,WAAM,GAAN,MAAM,CAAQ;QACxD,MAAC,GAAD,CAAC,CAAQ;IAAG,CAAC;;AAlFxB,qCAAqC;AAC9B,yBAAO,GAAG,iBAAiB,CAAC,IAAI,EAAE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as utils from '../utils/color_utils.js';\nimport * as math from '../utils/math_utils.js';\n\n/**\n * In traditional color spaces, a color can be identified solely by the\n * observer's measurement of the color. Color appearance models such as CAM16\n * also use information about the environment where the color was\n * observed, known as the viewing conditions.\n *\n * For example, white under the traditional assumption of a midday sun white\n * point is accurately measured as a slightly chromatic blue by CAM16. (roughly,\n * hue 203, chroma 3, lightness 100)\n *\n * This class caches intermediate values of the CAM16 conversion process that\n * depend only on viewing conditions, enabling speed ups.\n */\nexport class ViewingConditions {\n  /** sRGB-like viewing conditions.  */\n  static DEFAULT = ViewingConditions.make();\n\n  /**\n   * Create ViewingConditions from a simple, physically relevant, set of\n   * parameters.\n   *\n   * @param whitePoint White point, measured in the XYZ color space.\n   *     default = D65, or sunny day afternoon\n   * @param adaptingLuminance The luminance of the adapting field. Informally,\n   *     how bright it is in the room where the color is viewed. Can be\n   *     calculated from lux by multiplying lux by 0.0586. default = 11.72,\n   *     or 200 lux.\n   * @param backgroundLstar The lightness of the area surrounding the color.\n   *     measured by L* in L*a*b*. default = 50.0\n   * @param surround A general description of the lighting surrounding the\n   *     color. 0 is pitch dark, like watching a movie in a theater. 1.0 is a\n   *     dimly light room, like watching TV at home at night. 2.0 means there\n   *     is no difference between the lighting on the color and around it.\n   *     default = 2.0\n   * @param discountingIlluminant Whether the eye accounts for the tint of the\n   *     ambient lighting, such as knowing an apple is still red in green light.\n   *     default = false, the eye does not perform this process on\n   *       self-luminous objects like displays.\n   */\n  static make(\n      whitePoint = utils.whitePointD65(),\n      adaptingLuminance = (200.0 / Math.PI) * utils.yFromLstar(50.0) / 100.0,\n      backgroundLstar = 50.0, surround = 2.0,\n      discountingIlluminant = false): ViewingConditions {\n    const xyz = whitePoint;\n    const rW = xyz[0] * 0.401288 + xyz[1] * 0.650173 + xyz[2] * -0.051461;\n    const gW = xyz[0] * -0.250268 + xyz[1] * 1.204414 + xyz[2] * 0.045854;\n    const bW = xyz[0] * -0.002079 + xyz[1] * 0.048952 + xyz[2] * 0.953127;\n    const f = 0.8 + surround / 10.0;\n    const c = f >= 0.9 ? math.lerp(0.59, 0.69, (f - 0.9) * 10.0) :\n                         math.lerp(0.525, 0.59, (f - 0.8) * 10.0);\n    let d = discountingIlluminant ?\n        1.0 :\n        f * (1.0 - (1.0 / 3.6) * Math.exp((-adaptingLuminance - 42.0) / 92.0));\n    d = d > 1.0 ? 1.0 : d < 0.0 ? 0.0 : d;\n    const nc = f;\n    const rgbD = [\n      d * (100.0 / rW) + 1.0 - d,\n      d * (100.0 / gW) + 1.0 - d,\n      d * (100.0 / bW) + 1.0 - d,\n    ];\n    const k = 1.0 / (5.0 * adaptingLuminance + 1.0);\n    const k4 = k * k * k * k;\n    const k4F = 1.0 - k4;\n    const fl = k4 * adaptingLuminance +\n        0.1 * k4F * k4F * Math.cbrt(5.0 * adaptingLuminance);\n    const n = utils.yFromLstar(backgroundLstar) / whitePoint[1];\n    const z = 1.48 + Math.sqrt(n);\n    const nbb = 0.725 / Math.pow(n, 0.2);\n    const ncb = nbb;\n    const rgbAFactors = [\n      Math.pow((fl * rgbD[0] * rW) / 100.0, 0.42),\n      Math.pow((fl * rgbD[1] * gW) / 100.0, 0.42),\n      Math.pow((fl * rgbD[2] * bW) / 100.0, 0.42),\n    ];\n    const rgbA = [\n      (400.0 * rgbAFactors[0]) / (rgbAFactors[0] + 27.13),\n      (400.0 * rgbAFactors[1]) / (rgbAFactors[1] + 27.13),\n      (400.0 * rgbAFactors[2]) / (rgbAFactors[2] + 27.13),\n    ];\n    const aw = (2.0 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]) * nbb;\n    return new ViewingConditions(\n        n, aw, nbb, ncb, c, nc, rgbD, fl, Math.pow(fl, 0.25), z);\n  }\n\n  /**\n   * Parameters are intermediate values of the CAM16 conversion process. Their\n   * names are shorthand for technical color science terminology, this class\n   * would not benefit from documenting them individually. A brief overview\n   * is available in the CAM16 specification, and a complete overview requires\n   * a color science textbook, such as Fairchild's Color Appearance Models.\n   */\n  private constructor(\n      public n: number, public aw: number, public nbb: number,\n      public ncb: number, public c: number, public nc: number,\n      public rgbD: number[], public fl: number, public fLRoot: number,\n      public z: number) {}\n}\n"]}