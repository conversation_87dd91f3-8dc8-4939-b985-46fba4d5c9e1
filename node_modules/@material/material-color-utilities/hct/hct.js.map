{"version": 3, "file": "hct.js", "sourceRoot": "", "sources": ["hct.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAK,KAAK,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACjC,OAAO,EAAC,SAAS,EAAC,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAC,iBAAiB,EAAC,MAAM,yBAAyB,CAAC;AAG1D;;;;GAIG;AACH,MAAM,OAAO,GAAG;IAcd,MAAM,CAAC,IAAI,CAAC,GAAW,EAAE,MAAc,EAAE,IAAY;QACnD,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY;QACzB,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;;OAGG;IACH,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,IAAI,GAAG,CAAC,MAAc;QACpB,IAAI,CAAC,gBAAgB,CACjB,SAAS,CAAC,UAAU,CAChB,MAAM,EACN,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,YAAY,CAChB,CACR,CAAC;IACJ,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,IAAI,MAAM,CAAC,SAAiB;QAC1B,IAAI,CAAC,gBAAgB,CACjB,SAAS,CAAC,UAAU,CAChB,IAAI,CAAC,WAAW,EAChB,SAAS,EACT,IAAI,CAAC,YAAY,CAChB,CACR,CAAC;IACJ,CAAC;IAED,uCAAuC;IACvC,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,IAAI,IAAI,CAAC,OAAe;QACtB,IAAI,CAAC,gBAAgB,CACjB,SAAS,CAAC,UAAU,CAChB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,OAAO,CACN,CACR,CAAC;IACJ,CAAC;IAED,YAA4B,IAAY;QAAZ,SAAI,GAAJ,IAAI,CAAQ;QACtC,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,IAAY;QACnC,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,mBAAmB,CAAC,EAAqB;QACvC,iEAAiE;QACjE,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,GAAG,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;QAElD,0DAA0D;QAC1D,MAAM,UAAU,GAAG,KAAK,CAAC,0BAA0B,CAC/C,UAAU,CAAC,CAAC,CAAC,EACb,UAAU,CAAC,CAAC,CAAC,EACb,UAAU,CAAC,CAAC,CAAC,EACb,iBAAiB,CAAC,IAAI,EAAE,CAC3B,CAAC;QAEF,sBAAsB;QACtB,iEAAiE;QACjE,4DAA4D;QAC5D,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CACtB,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,MAAM,EACjB,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAClC,CAAC;QACF,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * A color system built using CAM16 hue and chroma, and L* from\n * L*a*b*.\n *\n * Using L* creates a link between the color system, contrast, and thus\n * accessibility. Contrast ratio depends on relative luminance, or Y in the XYZ\n * color space. L*, or perceptual luminance can be calculated from Y.\n *\n * Unlike Y, L* is linear to human perception, allowing trivial creation of\n * accurate color tones.\n *\n * Unlike contrast ratio, measuring contrast in L* is linear, and simple to\n * calculate. A difference of 40 in HCT tone guarantees a contrast ratio >= 3.0,\n * and a difference of 50 guarantees a contrast ratio >= 4.5.\n */\n\nimport * as utils from '../utils/color_utils.js';\n\nimport {Cam16} from './cam16.js';\nimport {HctSolver} from './hct_solver.js';\nimport {ViewingConditions} from './viewing_conditions.js';\n\n\n/**\n * HCT, hue, chroma, and tone. A color system that provides a perceptually\n * accurate color measurement system that can also accurately render what colors\n * will appear as in different lighting environments.\n */\nexport class Hct {\n  /**\n   * @param hue 0 <= hue < 360; invalid values are corrected.\n   * @param chroma 0 <= chroma < ?; Informally, colorfulness. The color\n   *     returned may be lower than the requested chroma. Chroma has a different\n   *     maximum for any given hue and tone.\n   * @param tone 0 <= tone <= 100; invalid values are corrected.\n   * @return HCT representation of a color in default viewing conditions.\n   */\n\n  internalHue: number;\n  internalChroma: number;\n  internalTone: number;\n\n  static from(hue: number, chroma: number, tone: number) {\n    return new Hct(HctSolver.solveToInt(hue, chroma, tone));\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return HCT representation of a color in default viewing conditions\n   */\n  static fromInt(argb: number) {\n    return new Hct(argb);\n  }\n\n  toInt(): number {\n    return this.argb;\n  }\n\n  /**\n   * A number, in degrees, representing ex. red, orange, yellow, etc.\n   * Ranges from 0 <= hue < 360.\n   */\n  get hue(): number {\n    return this.internalHue;\n  }\n\n  /**\n   * @param newHue 0 <= newHue < 360; invalid values are corrected.\n   * Chroma may decrease because chroma has a different maximum for any given\n   * hue and tone.\n   */\n  set hue(newHue: number) {\n    this.setInternalState(\n        HctSolver.solveToInt(\n            newHue,\n            this.internalChroma,\n            this.internalTone,\n            ),\n    );\n  }\n\n  get chroma(): number {\n    return this.internalChroma;\n  }\n\n  /**\n   * @param newChroma 0 <= newChroma < ?\n   * Chroma may decrease because chroma has a different maximum for any given\n   * hue and tone.\n   */\n  set chroma(newChroma: number) {\n    this.setInternalState(\n        HctSolver.solveToInt(\n            this.internalHue,\n            newChroma,\n            this.internalTone,\n            ),\n    );\n  }\n\n  /** Lightness. Ranges from 0 to 100. */\n  get tone(): number {\n    return this.internalTone;\n  }\n\n  /**\n   * @param newTone 0 <= newTone <= 100; invalid valids are corrected.\n   * Chroma may decrease because chroma has a different maximum for any given\n   * hue and tone.\n   */\n  set tone(newTone: number) {\n    this.setInternalState(\n        HctSolver.solveToInt(\n            this.internalHue,\n            this.internalChroma,\n            newTone,\n            ),\n    );\n  }\n\n  private constructor(private argb: number) {\n    const cam = Cam16.fromInt(argb);\n    this.internalHue = cam.hue;\n    this.internalChroma = cam.chroma;\n    this.internalTone = utils.lstarFromArgb(argb);\n    this.argb = argb;\n  }\n\n  private setInternalState(argb: number) {\n    const cam = Cam16.fromInt(argb);\n    this.internalHue = cam.hue;\n    this.internalChroma = cam.chroma;\n    this.internalTone = utils.lstarFromArgb(argb);\n    this.argb = argb;\n  }\n\n  /**\n   * Translates a color into different [ViewingConditions].\n   *\n   * Colors change appearance. They look different with lights on versus off,\n   * the same color, as in hex code, on white looks different when on black.\n   * This is called color relativity, most famously explicated by Josef Albers\n   * in Interaction of Color.\n   *\n   * In color science, color appearance models can account for this and\n   * calculate the appearance of a color in different settings. HCT is based on\n   * CAM16, a color appearance model, and uses it to make these calculations.\n   *\n   * See [ViewingConditions.make] for parameters affecting color appearance.\n   */\n  inViewingConditions(vc: ViewingConditions): Hct {\n    // 1. Use CAM16 to find XYZ coordinates of color in specified VC.\n    const cam = Cam16.fromInt(this.toInt());\n    const viewedInVc = cam.xyzInViewingConditions(vc);\n\n    // 2. Create CAM16 of those XYZ coordinates in default VC.\n    const recastInVc = Cam16.fromXyzInViewingConditions(\n        viewedInVc[0],\n        viewedInVc[1],\n        viewedInVc[2],\n        ViewingConditions.make(),\n    );\n\n    // 3. Create HCT from:\n    // - CAM16 using default VC with XYZ coordinates in specified VC.\n    // - L* converted from Y in XYZ coordinates in specified VC.\n    const recastHct = Hct.from(\n        recastInVc.hue,\n        recastInVc.chroma,\n        utils.lstarFromY(viewedInVc[1]),\n    );\n    return recastHct;\n  }\n}\n"]}