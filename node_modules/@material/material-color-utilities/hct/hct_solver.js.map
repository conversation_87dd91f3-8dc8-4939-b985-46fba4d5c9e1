{"version": 3, "file": "hct_solver.js", "sourceRoot": "", "sources": ["hct_solver.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,0DAA0D;AAE1D,OAAO,KAAK,UAAU,MAAM,yBAAyB,CAAC;AACtD,OAAO,KAAK,SAAS,MAAM,wBAAwB,CAAC;AAEpD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACjC,OAAO,EAAC,iBAAiB,EAAC,MAAM,yBAAyB,CAAC;AAG1D,uEAAuE;AACvE,4EAA4E;AAC5E,yCAAyC;AACzC,EAAE;AACF,oCAAoC;AACpC;;GAEG;AACH,MAAM,OAAO,SAAS;IA+HpB;;;;;;OAMG;IACK,MAAM,CAAC,eAAe,CAAC,KAAa;QAC1C,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;;OAQG;IACK,MAAM,CAAC,gBAAgB,CAAC,YAAoB;QAClD,MAAM,UAAU,GAAG,YAAY,GAAG,KAAK,CAAC;QACxC,IAAI,YAAY,GAAG,GAAG,CAAC;QACvB,IAAI,UAAU,IAAI,SAAS,EAAE;YAC3B,YAAY,GAAG,UAAU,GAAG,KAAK,CAAC;SACnC;aAAM;YACL,YAAY,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;SAChE;QACD,OAAO,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,SAAiB;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;QAC/C,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC;IACjE,CAAC;IAED;;;;;OAKG;IACK,MAAM,CAAC,KAAK,CAAC,MAAgB;QACnC,MAAM,cAAc,GAChB,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;QAC5E,MAAM,EAAE,GAAG,SAAS,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,EAAE,GAAG,SAAS,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,EAAE,GAAG,SAAS,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,oBAAoB;QACpB,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAC/C,sBAAsB;QACtB,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;QACrC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC7D,MAAM,OAAO,GAAG,SAAS,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,SAAS,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjD,OAAO,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG;IACK,MAAM,CAAC,SAAS,CAAC,MAAc,EAAE,GAAW,EAAE,MAAc;QAElE,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAC5C,CAAC;IAEO,MAAM,CAAC,SAAS,CAAC,MAAgB,EAAE,CAAS,EAAE,MAAgB;QAEpE,OAAO;YACL,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YACvC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YACvC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;SACxC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACK,MAAM,CAAC,aAAa,CACxB,MAAgB,EAChB,UAAkB,EAClB,MAAgB,EAChB,IAAY;QAEd,MAAM,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IAEO,MAAM,CAAC,SAAS,CAAC,CAAS;QAChC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;IAChC,CAAC;IAED;;;;;;;;;OASG;IACK,MAAM,CAAC,SAAS,CAAC,CAAS,EAAE,CAAS;QAC3C,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QACxC,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QACzC,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,MAAM,CAAC,GAAG,MAAM,CAAC;YACjB,MAAM,CAAC,GAAG,MAAM,CAAC;YACjB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;YACrC,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBAC1B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAClB;iBAAM;gBACL,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;aAC3B;SACF;aAAM,IAAI,CAAC,GAAG,CAAC,EAAE;YAChB,MAAM,CAAC,GAAG,MAAM,CAAC;YACjB,MAAM,CAAC,GAAG,MAAM,CAAC;YACjB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;YACrC,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBAC1B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAClB;iBAAM;gBACL,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;aAC3B;SACF;aAAM;YACL,MAAM,CAAC,GAAG,MAAM,CAAC;YACjB,MAAM,CAAC,GAAG,MAAM,CAAC;YACjB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;YACrC,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBAC1B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAClB;iBAAM;gBACL,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;aAC3B;SACF;IACH,CAAC;IAED;;;;;;;;OAQG;IACK,MAAM,CAAC,eAAe,CAAC,CAAS,EAAE,SAAiB;QACzD,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,OAAO,GAAG,GAAG,CAAC;QAClB,IAAI,QAAQ,GAAG,GAAG,CAAC;QACnB,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YAC3B,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACd,SAAS;aACV;YACD,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,CAAC,WAAW,EAAE;gBAChB,IAAI,GAAG,GAAG,CAAC;gBACX,KAAK,GAAG,GAAG,CAAC;gBACZ,OAAO,GAAG,MAAM,CAAC;gBACjB,QAAQ,GAAG,MAAM,CAAC;gBAClB,WAAW,GAAG,IAAI,CAAC;gBACnB,SAAS;aACV;YACD,IAAI,KAAK,IAAI,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE;gBAClE,KAAK,GAAG,KAAK,CAAC;gBACd,IAAI,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;oBAC1D,KAAK,GAAG,GAAG,CAAC;oBACZ,QAAQ,GAAG,MAAM,CAAC;iBACnB;qBAAM;oBACL,IAAI,GAAG,GAAG,CAAC;oBACX,OAAO,GAAG,MAAM,CAAC;iBAClB;aACF;SACF;QACD,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvB,CAAC;IAEO,MAAM,CAAC,QAAQ,CAAC,CAAW,EAAE,CAAW;QAC9C,OAAO;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;SAClB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,CAAS;QACzC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,CAAS;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;;;OAOG;IACK,MAAM,CAAC,aAAa,CAAC,CAAS,EAAE,SAAiB;QACvD,MAAM,OAAO,GAAG,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACxD,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACvB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE;YACnC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC9B,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChB,IAAI,MAAM,GAAG,GAAG,CAAC;gBACjB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5B,MAAM,GAAG,SAAS,CAAC,kBAAkB,CACjC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC5C,MAAM,GAAG,SAAS,CAAC,kBAAkB,CACjC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC9C;qBAAM;oBACL,MAAM,GAAG,SAAS,CAAC,kBAAkB,CACjC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC5C,MAAM,GAAG,SAAS,CAAC,kBAAkB,CACjC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC9C;gBACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE;wBAClC,MAAM;qBACP;yBAAM;wBACL,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;wBACnD,MAAM,kBAAkB,GAAG,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;wBAC7D,MAAM,GAAG,GACL,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;wBACnE,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACpC,IAAI,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;4BAC1D,KAAK,GAAG,GAAG,CAAC;4BACZ,MAAM,GAAG,MAAM,CAAC;yBACjB;6BAAM;4BACL,IAAI,GAAG,GAAG,CAAC;4BACX,OAAO,GAAG,MAAM,CAAC;4BACjB,MAAM,GAAG,MAAM,CAAC;yBACjB;qBACF;iBACF;aACF;SACF;QACD,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,OAAe;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC;QACpE,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;OAQG;IACK,MAAM,CAAC,aAAa,CAAC,UAAkB,EAAE,MAAc,EAAE,CAAS;QAExE,yBAAyB;QACzB,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAC5B,8DAA8D;QAC9D,8DAA8D;QAC9D,8DAA8D;QAC9D,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,CAAC;QACpD,MAAM,WAAW,GACb,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACnE,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACvD,MAAM,EAAE,GACJ,IAAI,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;QAC3E,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAClC,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,EAAE,cAAc,EAAE,EAAE;YACjE,8DAA8D;YAC9D,8DAA8D;YAC9D,8DAA8D;YAC9D,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC;YAC9B,MAAM,KAAK,GACP,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxE,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,WAAW,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACnD,MAAM,EAAE,GAAG,iBAAiB,CAAC,EAAE;gBAC3B,IAAI,CAAC,GAAG,CACJ,WAAW,EACX,GAAG,GAAG,iBAAiB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAClD,CAAC;YACN,MAAM,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC;YACtC,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC;gBACjC,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;YACvB,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;YACzD,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;YACzD,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;YAC1D,MAAM,QAAQ,GAAG,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;YAC1D,MAAM,QAAQ,GAAG,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;YAC1D,MAAM,QAAQ,GAAG,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;YAC1D,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CACnC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAC9B,SAAS,CAAC,2BAA2B,CACxC,CAAC;YACF,8DAA8D;YAC9D,8DAA8D;YAC9D,8DAA8D;YAC9D,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACnD,OAAO,CAAC,CAAC;aACV;YACD,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,GAAG,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,GAAG,IAAI,CAAC,EAAE;gBACZ,OAAO,CAAC,CAAC;aACV;YACD,IAAI,cAAc,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE;gBACrD,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE;oBAClE,OAAO,CAAC,CAAC;iBACV;gBACD,OAAO,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;aAC1C;YACD,+BAA+B;YAC/B,qDAAqD;YACrD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;SACnC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;;;;;;;;;;OAWG;IACH,MAAM,CAAC,UAAU,CAAC,UAAkB,EAAE,MAAc,EAAE,KAAa;QACjE,IAAI,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG,MAAM,IAAI,KAAK,GAAG,OAAO,EAAE;YACxD,OAAO,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SACxC;QACD,UAAU,GAAG,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzD,MAAM,UAAU,GAAG,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAC9C,MAAM,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,WAAW,KAAK,CAAC,EAAE;YACrB,OAAO,WAAW,CAAC;SACpB;QACD,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QACtD,OAAO,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;OAWG;IACH,MAAM,CAAC,UAAU,CAAC,UAAkB,EAAE,MAAc,EAAE,KAAa;QACjE,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;IACxE,CAAC;;AArgBM,qCAA2B,GAAG;IACnC;QACE,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;KACtB;IACD;QACE,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;KACtB;IACD;QACE,sBAAsB;QACtB,qBAAqB;QACrB,qBAAqB;KACtB;CACF,CAAC;AAEK,qCAA2B,GAAG;IACnC;QACE,kBAAkB;QAClB,CAAC,kBAAkB;QACnB,CAAC,iBAAiB;KACnB;IACD;QACE,CAAC,gBAAgB;QACjB,iBAAiB;QACjB,CAAC,iBAAiB;KACnB;IACD;QACE,kBAAkB;QAClB,CAAC,kBAAkB;QACnB,iBAAiB;KAClB;CACF,CAAC;AAEK,uBAAa,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAEzC,yBAAe,GAAG;IACvB,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB;IAC/D,mBAAmB,EAAG,mBAAmB,EAAG,mBAAmB;IAC/D,mBAAmB,EAAG,kBAAkB,EAAI,kBAAkB;IAC9D,mBAAmB,EAAG,kBAAkB,EAAI,iBAAiB;IAC7D,kBAAkB,EAAI,mBAAmB,EAAG,iBAAiB;IAC7D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,kBAAkB;IAC9D,gBAAgB,EAAM,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,iBAAiB;IAC7D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,gBAAgB,EAAM,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,gBAAgB;IAC5D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,kBAAkB;IAC9D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,kBAAkB,EAAI,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,kBAAkB,EAAI,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,iBAAiB;IAC7D,kBAAkB,EAAI,iBAAiB,EAAK,kBAAkB;IAC9D,kBAAkB,EAAI,gBAAgB,EAAM,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,kBAAkB,EAAI,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,gBAAgB,EAAM,kBAAkB,EAAI,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,iBAAiB;IAC7D,kBAAkB,EAAI,kBAAkB,EAAI,gBAAgB;IAC5D,iBAAiB,EAAK,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,iBAAiB,EAAK,iBAAiB,EAAK,kBAAkB;IAC9D,gBAAgB,EAAM,gBAAgB,EAAM,kBAAkB;IAC9D,kBAAkB,EAAI,iBAAiB,EAAK,iBAAiB;IAC7D,gBAAgB,EAAM,kBAAkB,EAAI,kBAAkB;IAC9D,iBAAiB,EAAK,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,kBAAkB,EAAI,iBAAiB,EAAK,iBAAiB;IAC7D,kBAAkB,EAAI,iBAAiB,EAAK,kBAAkB;IAC9D,gBAAgB,EAAM,kBAAkB,EAAI,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,gBAAgB;IAC5D,iBAAiB,EAAK,iBAAiB,EAAK,gBAAgB;IAC5D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,gBAAgB,EAAM,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,gBAAgB,EAAM,gBAAgB;IAC5D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,iBAAiB;IAC7D,iBAAiB,EAAK,iBAAiB,EAAK,gBAAgB;IAC5D,iBAAiB,EAAK,gBAAgB,EAAM,iBAAiB;CAC9D,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This file is automatically generated. Do not modify it.\n\nimport * as colorUtils from '../utils/color_utils.js';\nimport * as mathUtils from '../utils/math_utils.js';\n\nimport {Cam16} from './cam16.js';\nimport {ViewingConditions} from './viewing_conditions.js';\n\n\n// material_color_utilities is designed to have a consistent API across\n// platforms and modular components that can be moved around easily. Using a\n// class as a namespace facilitates this.\n//\n// tslint:disable:class-as-namespace\n/**\n * A class that solves the HCT equation.\n */\nexport class HctSolver {\n  static SCALED_DISCOUNT_FROM_LINRGB = [\n    [\n      0.001200833568784504,\n      0.002389694492170889,\n      0.0002795742885861124,\n    ],\n    [\n      0.0005891086651375999,\n      0.0029785502573438758,\n      0.0003270666104008398,\n    ],\n    [\n      0.00010146692491640572,\n      0.0005364214359186694,\n      0.0032979401770712076,\n    ],\n  ];\n\n  static LINRGB_FROM_SCALED_DISCOUNT = [\n    [\n      1373.2198709594231,\n      -1100.4251190754821,\n      -7.278681089101213,\n    ],\n    [\n      -271.815969077903,\n      559.6580465940733,\n      -32.46047482791194,\n    ],\n    [\n      1.9622899599665666,\n      -57.173814538844006,\n      308.7233197812385,\n    ],\n  ];\n\n  static Y_FROM_LINRGB = [0.2126, 0.7152, 0.0722];\n\n  static CRITICAL_PLANES = [\n    0.015176349177441876, 0.045529047532325624, 0.07588174588720938,\n    0.10623444424209313,  0.13658714259697685,  0.16693984095186062,\n    0.19729253930674434,  0.2276452376616281,   0.2579979360165119,\n    0.28835063437139563,  0.3188300904430532,   0.350925934958123,\n    0.3848314933096426,   0.42057480301049466,  0.458183274052838,\n    0.4976837250274023,   0.5391024159806381,   0.5824650784040898,\n    0.6277969426914107,   0.6751227633498623,   0.7244668422128921,\n    0.775853049866786,    0.829304845476233,    0.8848452951698498,\n    0.942497089126609,    1.0022825574869039,   1.0642236851973577,\n    1.1283421258858297,   1.1946592148522128,   1.2631959812511864,\n    1.3339731595349034,   1.407011200216447,    1.4823302800086415,\n    1.5599503113873272,   1.6398909516233677,   1.7221716113234105,\n    1.8068114625156377,   1.8938294463134073,   1.9832442801866852,\n    2.075074464868551,    2.1693382909216234,   2.2660538449872063,\n    2.36523901573795,     2.4669114995532007,   2.5710888059345764,\n    2.6777882626779785,   2.7870270208169257,   2.898822059350997,\n    3.0131901897720907,   3.1301480604002863,   3.2497121605402226,\n    3.3718988244681087,   3.4967242352587946,   3.624204428461639,\n    3.754355295633311,    3.887192587735158,    4.022731918402185,\n    4.160988767090289,    4.301978482107941,    4.445716283538092,\n    4.592217266055746,    4.741496401646282,    4.893568542229298,\n    5.048448422192488,    5.20615066083972,     5.3666897647573375,\n    5.5300801301023865,   5.696336044816294,    5.865471690767354,\n    6.037501145825082,    6.212438385869475,    6.390297286737924,\n    6.571091626112461,    6.7548350853498045,   6.941541251256611,\n    7.131223617812143,    7.323895587840543,    7.5195704746346665,\n    7.7182615035334345,   7.919981813454504,    8.124744458384042,\n    8.332562408825165,    8.543448553206703,    8.757415699253682,\n    8.974476575321063,    9.194643831691977,    9.417930041841839,\n    9.644347703669503,    9.873909240696694,    10.106627003236781,\n    10.342513269534024,   10.58158024687427,    10.8238400726681,\n    11.069304815507364,   11.317986476196008,   11.569896988756009,\n    11.825048221409341,   12.083451977536606,   12.345119996613247,\n    12.610063955123938,   12.878295467455942,   13.149826086772048,\n    13.42466730586372,    13.702830557985108,   13.984327217668513,\n    14.269168601521828,   14.55736596900856,    14.848930523210871,\n    15.143873411576273,   15.44220572664832,    15.743938506781891,\n    16.04908273684337,    16.35764934889634,    16.66964922287304,\n    16.985093187232053,   17.30399201960269,    17.62635644741625,\n    17.95219714852476,    18.281524751807332,   18.614349837764564,\n    18.95068293910138,    19.290534541298456,   19.633915083172692,\n    19.98083495742689,    20.331304511189067,   20.685334046541502,\n    21.042933821039977,   21.404114048223256,   21.76888489811322,\n    22.137256497705877,   22.50923893145328,    22.884842241736916,\n    23.264076429332462,   23.6469514538663,     24.033477234264016,\n    24.42366364919083,    24.817520537484558,   25.21505769858089,\n    25.61628489293138,    26.021211842414342,   26.429848230738664,\n    26.842203703840827,   27.258287870275353,   27.678110301598522,\n    28.10168053274597,    28.529008062403893,   28.96010235337422,\n    29.39497283293396,    29.83362889318845,    30.276079891419332,\n    30.722335150426627,   31.172403958865512,   31.62629557157785,\n    32.08401920991837,    32.54558406207592,    33.010999283389665,\n    33.4802739966603,     33.953417292456834,   34.430438229418264,\n    34.911345834551085,   35.39614910352207,    35.88485700094671,\n    36.37747846067349,    36.87402238606382,    37.37449765026789,\n    37.87891309649659,    38.38727753828926,    38.89959975977785,\n    39.41588851594697,    39.93615253289054,    40.460400508064545,\n    40.98864111053629,    41.520882981230194,   42.05713473317016,\n    42.597404951718396,   43.141702194811224,   43.6900349931913,\n    44.24241185063697,    44.798841244188324,   45.35933162437017,\n    45.92389141541209,    46.49252901546552,    47.065252796817916,\n    47.64207110610409,    48.22299226451468,    48.808024568002054,\n    49.3971762874833,     49.9904556690408,     50.587870934119984,\n    51.189430279724725,   51.79514187861014,    52.40501387947288,\n    53.0190544071392,     53.637271562750364,   54.259673423945976,\n    54.88626804504493,    55.517063457223934,   56.15206766869424,\n    56.79128866487574,    57.43473440856916,    58.08241284012621,\n    58.734331877617365,   59.39049941699807,    60.05092333227251,\n    60.715611475655585,   61.38457167773311,    62.057811747619894,\n    62.7353394731159,     63.417162620860914,   64.10328893648692,\n    64.79372614476921,    65.48848194977529,    66.18756403501224,\n    66.89098006357258,    67.59873767827808,    68.31084450182222,\n    69.02730813691093,    69.74813616640164,    70.47333615344107,\n    71.20291564160104,    71.93688215501312,    72.67524319850172,\n    73.41800625771542,    74.16517879925733,    74.9167682708136,\n    75.67278210128072,    76.43322770089146,    77.1981124613393,\n    77.96744375590167,    78.74122893956174,    79.51947534912904,\n    80.30219030335869,    81.08938110306934,    81.88105503125999,\n    82.67721935322541,    83.4778813166706,     84.28304815182372,\n    85.09272707154808,    85.90692527145302,    86.72564993000343,\n    87.54890820862819,    88.3767072518277,     89.2090541872801,\n    90.04595612594655,    90.88742016217518,    91.73345337380438,\n    92.58406282226491,    93.43925555268066,    94.29903859396902,\n    95.16341895893969,    96.03240364439274,    96.9059996312159,\n    97.78421388448044,    98.6670533535366,     99.55452497210776,\n  ];\n\n  /**\n   * Sanitizes a small enough angle in radians.\n   *\n   * @param angle An angle in radians; must not deviate too much\n   * from 0.\n   * @return A coterminal angle between 0 and 2pi.\n   */\n  private static sanitizeRadians(angle: number): number {\n    return (angle + Math.PI * 8) % (Math.PI * 2);\n  }\n\n  /**\n   * Delinearizes an RGB component, returning a floating-point\n   * number.\n   *\n   * @param rgbComponent 0.0 <= rgb_component <= 100.0, represents\n   * linear R/G/B channel\n   * @return 0.0 <= output <= 255.0, color channel converted to\n   * regular RGB space\n   */\n  private static trueDelinearized(rgbComponent: number): number {\n    const normalized = rgbComponent / 100.0;\n    let delinearized = 0.0;\n    if (normalized <= 0.0031308) {\n      delinearized = normalized * 12.92;\n    } else {\n      delinearized = 1.055 * Math.pow(normalized, 1.0 / 2.4) - 0.055;\n    }\n    return delinearized * 255.0;\n  }\n\n  private static chromaticAdaptation(component: number): number {\n    const af = Math.pow(Math.abs(component), 0.42);\n    return mathUtils.signum(component) * 400.0 * af / (af + 27.13);\n  }\n\n  /**\n   * Returns the hue of a linear RGB color in CAM16.\n   *\n   * @param linrgb The linear RGB coordinates of a color.\n   * @return The hue of the color in CAM16, in radians.\n   */\n  private static hueOf(linrgb: number[]): number {\n    const scaledDiscount =\n        mathUtils.matrixMultiply(linrgb, HctSolver.SCALED_DISCOUNT_FROM_LINRGB);\n    const rA = HctSolver.chromaticAdaptation(scaledDiscount[0]);\n    const gA = HctSolver.chromaticAdaptation(scaledDiscount[1]);\n    const bA = HctSolver.chromaticAdaptation(scaledDiscount[2]);\n    // redness-greenness\n    const a = (11.0 * rA + -12.0 * gA + bA) / 11.0;\n    // yellowness-blueness\n    const b = (rA + gA - 2.0 * bA) / 9.0;\n    return Math.atan2(b, a);\n  }\n\n  private static areInCyclicOrder(a: number, b: number, c: number): boolean {\n    const deltaAB = HctSolver.sanitizeRadians(b - a);\n    const deltaAC = HctSolver.sanitizeRadians(c - a);\n    return deltaAB < deltaAC;\n  }\n\n  /**\n   * Solves the lerp equation.\n   *\n   * @param source The starting number.\n   * @param mid The number in the middle.\n   * @param target The ending number.\n   * @return A number t such that lerp(source, target, t) = mid.\n   */\n  private static intercept(source: number, mid: number, target: number):\n      number {\n    return (mid - source) / (target - source);\n  }\n\n  private static lerpPoint(source: number[], t: number, target: number[]):\n      number[] {\n    return [\n      source[0] + (target[0] - source[0]) * t,\n      source[1] + (target[1] - source[1]) * t,\n      source[2] + (target[2] - source[2]) * t,\n    ];\n  }\n\n  /**\n   * Intersects a segment with a plane.\n   *\n   * @param source The coordinates of point A.\n   * @param coordinate The R-, G-, or B-coordinate of the plane.\n   * @param target The coordinates of point B.\n   * @param axis The axis the plane is perpendicular with. (0: R, 1:\n   * G, 2: B)\n   * @return The intersection point of the segment AB with the plane\n   * R=coordinate, G=coordinate, or B=coordinate\n   */\n  private static setCoordinate(\n      source: number[],\n      coordinate: number,\n      target: number[],\n      axis: number,\n      ): number[] {\n    const t = HctSolver.intercept(source[axis], coordinate, target[axis]);\n    return HctSolver.lerpPoint(source, t, target);\n  }\n\n  private static isBounded(x: number): boolean {\n    return 0.0 <= x && x <= 100.0;\n  }\n\n  /**\n   * Returns the nth possible vertex of the polygonal intersection.\n   *\n   * @param y The Y value of the plane.\n   * @param n The zero-based index of the point. 0 <= n <= 11.\n   * @return The nth possible vertex of the polygonal intersection\n   * of the y plane and the RGB cube, in linear RGB coordinates, if\n   * it exists. If this possible vertex lies outside of the cube,\n   * [-1.0, -1.0, -1.0] is returned.\n   */\n  private static nthVertex(y: number, n: number): number[] {\n    const kR = HctSolver.Y_FROM_LINRGB[0];\n    const kG = HctSolver.Y_FROM_LINRGB[1];\n    const kB = HctSolver.Y_FROM_LINRGB[2];\n    const coordA = n % 4 <= 1 ? 0.0 : 100.0;\n    const coordB = n % 2 === 0 ? 0.0 : 100.0;\n    if (n < 4) {\n      const g = coordA;\n      const b = coordB;\n      const r = (y - g * kG - b * kB) / kR;\n      if (HctSolver.isBounded(r)) {\n        return [r, g, b];\n      } else {\n        return [-1.0, -1.0, -1.0];\n      }\n    } else if (n < 8) {\n      const b = coordA;\n      const r = coordB;\n      const g = (y - r * kR - b * kB) / kG;\n      if (HctSolver.isBounded(g)) {\n        return [r, g, b];\n      } else {\n        return [-1.0, -1.0, -1.0];\n      }\n    } else {\n      const r = coordA;\n      const g = coordB;\n      const b = (y - r * kR - g * kG) / kB;\n      if (HctSolver.isBounded(b)) {\n        return [r, g, b];\n      } else {\n        return [-1.0, -1.0, -1.0];\n      }\n    }\n  }\n\n  /**\n   * Finds the segment containing the desired color.\n   *\n   * @param y The Y value of the color.\n   * @param targetHue The hue of the color.\n   * @return A list of two sets of linear RGB coordinates, each\n   * corresponding to an endpoint of the segment containing the\n   * desired color.\n   */\n  private static bisectToSegment(y: number, targetHue: number): number[][] {\n    let left = [-1.0, -1.0, -1.0];\n    let right = left;\n    let leftHue = 0.0;\n    let rightHue = 0.0;\n    let initialized = false;\n    let uncut = true;\n    for (let n = 0; n < 12; n++) {\n      const mid = HctSolver.nthVertex(y, n);\n      if (mid[0] < 0) {\n        continue;\n      }\n      const midHue = HctSolver.hueOf(mid);\n      if (!initialized) {\n        left = mid;\n        right = mid;\n        leftHue = midHue;\n        rightHue = midHue;\n        initialized = true;\n        continue;\n      }\n      if (uncut || HctSolver.areInCyclicOrder(leftHue, midHue, rightHue)) {\n        uncut = false;\n        if (HctSolver.areInCyclicOrder(leftHue, targetHue, midHue)) {\n          right = mid;\n          rightHue = midHue;\n        } else {\n          left = mid;\n          leftHue = midHue;\n        }\n      }\n    }\n    return [left, right];\n  }\n\n  private static midpoint(a: number[], b: number[]): number[] {\n    return [\n      (a[0] + b[0]) / 2,\n      (a[1] + b[1]) / 2,\n      (a[2] + b[2]) / 2,\n    ];\n  }\n\n  private static criticalPlaneBelow(x: number): number {\n    return Math.floor(x - 0.5);\n  }\n\n  private static criticalPlaneAbove(x: number): number {\n    return Math.ceil(x - 0.5);\n  }\n\n  /**\n   * Finds a color with the given Y and hue on the boundary of the\n   * cube.\n   *\n   * @param y The Y value of the color.\n   * @param targetHue The hue of the color.\n   * @return The desired color, in linear RGB coordinates.\n   */\n  private static bisectToLimit(y: number, targetHue: number): number[] {\n    const segment = HctSolver.bisectToSegment(y, targetHue);\n    let left = segment[0];\n    let leftHue = HctSolver.hueOf(left);\n    let right = segment[1];\n    for (let axis = 0; axis < 3; axis++) {\n      if (left[axis] !== right[axis]) {\n        let lPlane = -1;\n        let rPlane = 255;\n        if (left[axis] < right[axis]) {\n          lPlane = HctSolver.criticalPlaneBelow(\n              HctSolver.trueDelinearized(left[axis]));\n          rPlane = HctSolver.criticalPlaneAbove(\n              HctSolver.trueDelinearized(right[axis]));\n        } else {\n          lPlane = HctSolver.criticalPlaneAbove(\n              HctSolver.trueDelinearized(left[axis]));\n          rPlane = HctSolver.criticalPlaneBelow(\n              HctSolver.trueDelinearized(right[axis]));\n        }\n        for (let i = 0; i < 8; i++) {\n          if (Math.abs(rPlane - lPlane) <= 1) {\n            break;\n          } else {\n            const mPlane = Math.floor((lPlane + rPlane) / 2.0);\n            const midPlaneCoordinate = HctSolver.CRITICAL_PLANES[mPlane];\n            const mid =\n                HctSolver.setCoordinate(left, midPlaneCoordinate, right, axis);\n            const midHue = HctSolver.hueOf(mid);\n            if (HctSolver.areInCyclicOrder(leftHue, targetHue, midHue)) {\n              right = mid;\n              rPlane = mPlane;\n            } else {\n              left = mid;\n              leftHue = midHue;\n              lPlane = mPlane;\n            }\n          }\n        }\n      }\n    }\n    return HctSolver.midpoint(left, right);\n  }\n\n  private static inverseChromaticAdaptation(adapted: number): number {\n    const adaptedAbs = Math.abs(adapted);\n    const base = Math.max(0, 27.13 * adaptedAbs / (400.0 - adaptedAbs));\n    return mathUtils.signum(adapted) * Math.pow(base, 1.0 / 0.42);\n  }\n\n  /**\n   * Finds a color with the given hue, chroma, and Y.\n   *\n   * @param hueRadians The desired hue in radians.\n   * @param chroma The desired chroma.\n   * @param y The desired Y.\n   * @return The desired color as a hexadecimal integer, if found; 0\n   * otherwise.\n   */\n  private static findResultByJ(hueRadians: number, chroma: number, y: number):\n      number {\n    // Initial estimate of j.\n    let j = Math.sqrt(y) * 11.0;\n    // ===========================================================\n    // Operations inlined from Cam16 to avoid repeated calculation\n    // ===========================================================\n    const viewingConditions = ViewingConditions.DEFAULT;\n    const tInnerCoeff =\n        1 / Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73);\n    const eHue = 0.25 * (Math.cos(hueRadians + 2.0) + 3.8);\n    const p1 =\n        eHue * (50000.0 / 13.0) * viewingConditions.nc * viewingConditions.ncb;\n    const hSin = Math.sin(hueRadians);\n    const hCos = Math.cos(hueRadians);\n    for (let iterationRound = 0; iterationRound < 5; iterationRound++) {\n      // ===========================================================\n      // Operations inlined from Cam16 to avoid repeated calculation\n      // ===========================================================\n      const jNormalized = j / 100.0;\n      const alpha =\n          chroma === 0.0 || j === 0.0 ? 0.0 : chroma / Math.sqrt(jNormalized);\n      const t = Math.pow(alpha * tInnerCoeff, 1.0 / 0.9);\n      const ac = viewingConditions.aw *\n          Math.pow(\n              jNormalized,\n              1.0 / viewingConditions.c / viewingConditions.z,\n          );\n      const p2 = ac / viewingConditions.nbb;\n      const gamma = 23.0 * (p2 + 0.305) * t /\n          (23.0 * p1 + 11 * t * hCos + 108.0 * t * hSin);\n      const a = gamma * hCos;\n      const b = gamma * hSin;\n      const rA = (460.0 * p2 + 451.0 * a + 288.0 * b) / 1403.0;\n      const gA = (460.0 * p2 - 891.0 * a - 261.0 * b) / 1403.0;\n      const bA = (460.0 * p2 - 220.0 * a - 6300.0 * b) / 1403.0;\n      const rCScaled = HctSolver.inverseChromaticAdaptation(rA);\n      const gCScaled = HctSolver.inverseChromaticAdaptation(gA);\n      const bCScaled = HctSolver.inverseChromaticAdaptation(bA);\n      const linrgb = mathUtils.matrixMultiply(\n          [rCScaled, gCScaled, bCScaled],\n          HctSolver.LINRGB_FROM_SCALED_DISCOUNT,\n      );\n      // ===========================================================\n      // Operations inlined from Cam16 to avoid repeated calculation\n      // ===========================================================\n      if (linrgb[0] < 0 || linrgb[1] < 0 || linrgb[2] < 0) {\n        return 0;\n      }\n      const kR = HctSolver.Y_FROM_LINRGB[0];\n      const kG = HctSolver.Y_FROM_LINRGB[1];\n      const kB = HctSolver.Y_FROM_LINRGB[2];\n      const fnj = kR * linrgb[0] + kG * linrgb[1] + kB * linrgb[2];\n      if (fnj <= 0) {\n        return 0;\n      }\n      if (iterationRound === 4 || Math.abs(fnj - y) < 0.002) {\n        if (linrgb[0] > 100.01 || linrgb[1] > 100.01 || linrgb[2] > 100.01) {\n          return 0;\n        }\n        return colorUtils.argbFromLinrgb(linrgb);\n      }\n      // Iterates with Newton method,\n      // Using 2 * fn(j) / j as the approximation of fn'(j)\n      j = j - (fnj - y) * j / (2 * fnj);\n    }\n    return 0;\n  }\n\n  /**\n   * Finds an sRGB color with the given hue, chroma, and L*, if\n   * possible.\n   *\n   * @param hueDegrees The desired hue, in degrees.\n   * @param chroma The desired chroma.\n   * @param lstar The desired L*.\n   * @return A hexadecimal representing the sRGB color. The color\n   * has sufficiently close hue, chroma, and L* to the desired\n   * values, if possible; otherwise, the hue and L* will be\n   * sufficiently close, and chroma will be maximized.\n   */\n  static solveToInt(hueDegrees: number, chroma: number, lstar: number): number {\n    if (chroma < 0.0001 || lstar < 0.0001 || lstar > 99.9999) {\n      return colorUtils.argbFromLstar(lstar);\n    }\n    hueDegrees = mathUtils.sanitizeDegreesDouble(hueDegrees);\n    const hueRadians = hueDegrees / 180 * Math.PI;\n    const y = colorUtils.yFromLstar(lstar);\n    const exactAnswer = HctSolver.findResultByJ(hueRadians, chroma, y);\n    if (exactAnswer !== 0) {\n      return exactAnswer;\n    }\n    const linrgb = HctSolver.bisectToLimit(y, hueRadians);\n    return colorUtils.argbFromLinrgb(linrgb);\n  }\n\n  /**\n   * Finds an sRGB color with the given hue, chroma, and L*, if\n   * possible.\n   *\n   * @param hueDegrees The desired hue, in degrees.\n   * @param chroma The desired chroma.\n   * @param lstar The desired L*.\n   * @return An CAM16 object representing the sRGB color. The color\n   * has sufficiently close hue, chroma, and L* to the desired\n   * values, if possible; otherwise, the hue and L* will be\n   * sufficiently close, and chroma will be maximized.\n   */\n  static solveToCam(hueDegrees: number, chroma: number, lstar: number): Cam16 {\n    return Cam16.fromInt(HctSolver.solveToInt(hueDegrees, chroma, lstar));\n  }\n}\n"]}