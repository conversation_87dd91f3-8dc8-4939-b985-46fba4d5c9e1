{"version": 3, "file": "quantizer_wsmeans.js", "sourceRoot": "", "sources": ["quantizer_wsmeans.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,gBAAgB,EAAC,MAAM,yBAAyB,CAAC;AAEzD,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B,MAAM,qBAAqB,GAAG,GAAG,CAAC;AAElC;;;;;;;;;;;GAWG;AACH,uEAAuE;AACvE,4EAA4E;AAC5E,yCAAyC;AACzC,EAAE;AACF,8CAA8C;AAC9C,MAAM,OAAO,gBAAgB;IAC3B;;;;;;;;;;OAUG;IACH,MAAM,CAAC,QAAQ,CACX,WAAqB,EAAE,gBAA0B,EACjD,SAAiB;QACnB,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,KAAK,EAAY,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,KAAK,EAAU,CAAC;QACnC,MAAM,aAAa,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAC7C,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,UAAU,EAAE,CAAC;gBACb,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC/C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxB,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;aACjC;iBAAM;gBACL,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;aAC9C;SACF;QAED,MAAM,MAAM,GAAG,IAAI,KAAK,EAAU,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;aACnB;SACF;QAED,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QACnD,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;SAChE;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAY,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3D;QACD,MAAM,wBAAwB,GAAG,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC;QAChE,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,IAAI,wBAAwB,GAAG,CAAC,EAAE;YACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,wBAAwB,EAAE,CAAC,EAAE,EAAE;gBACjD,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC;gBAChC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;gBACxD,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;gBAExD,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACnC;SACF;QAED,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC;SAC/D;QAED,MAAM,WAAW,GAAG,IAAI,KAAK,EAAY,CAAC;QAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;YACrC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,EAAU,CAAC,CAAC;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACrC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACxB;SACF;QAED,MAAM,qBAAqB,GAAG,IAAI,KAAK,EAAsB,CAAC;QAC9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;YACrC,qBAAqB,CAAC,IAAI,CAAC,IAAI,KAAK,EAAoB,CAAC,CAAC;YAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACrC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,gBAAgB,EAAE,CAAC,CAAC;aACvD;SACF;QAGD,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;YACrC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACxB;QACD,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,cAAc,EAAE,SAAS,EAAE,EAAE;YAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACrC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;oBACzC,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClE,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;oBAChD,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;oBACtC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;oBAChD,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;iBACvC;gBACD,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;oBACrC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;iBACvD;aACF;YAED,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,oBAAoB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC/C,MAAM,eAAe,GAAG,QAAQ,CAAC,oBAAoB,CAAC,CAAC;gBACvD,MAAM,gBAAgB,GAAG,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;gBACxE,IAAI,eAAe,GAAG,gBAAgB,CAAC;gBACvC,IAAI,eAAe,GAAG,CAAC,CAAC,CAAC;gBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;oBACrC,IAAI,qBAAqB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;wBACvD,CAAC,GAAG,gBAAgB,EAAE;wBACxB,SAAS;qBACV;oBACD,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,IAAI,QAAQ,GAAG,eAAe,EAAE;wBAC9B,eAAe,GAAG,QAAQ,CAAC;wBAC3B,eAAe,GAAG,CAAC,CAAC;qBACrB;iBACF;gBACD,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;oBAC1B,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAC3B,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;oBAChE,IAAI,cAAc,GAAG,qBAAqB,EAAE;wBAC1C,WAAW,EAAE,CAAC;wBACd,cAAc,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;qBACrC;iBACF;aACF;YAED,IAAI,WAAW,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE;gBACxC,MAAM;aACP;YAED,MAAM,cAAc,GAAG,IAAI,KAAK,CAAS,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,cAAc,GAAG,IAAI,KAAK,CAAS,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,cAAc,GAAG,IAAI,KAAK,CAAS,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACrC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACvB;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxB,cAAc,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;gBACtC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;gBACnD,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;gBACnD,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;aACpD;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACrC,MAAM,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAChC,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBAC9B,SAAS;iBACV;gBACD,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACpC,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACpC,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACpC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACzB;SACF;QAED,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAkB,CAAC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,SAAS;aACV;YAED,MAAM,kBAAkB,GAAG,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,IAAI,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE;gBAC5C,SAAS;aACV;YAED,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;SACjD;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,gBAAgB;IAAtB;QACE,aAAQ,GAAW,CAAC,CAAC,CAAC;QACtB,UAAK,GAAW,CAAC,CAAC,CAAC;IACrB,CAAC;CAAA", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {LabPointProvider} from './lab_point_provider.js';\n\nconst MAX_ITERATIONS = 10;\nconst MIN_MOVEMENT_DISTANCE = 3.0;\n\n/**\n * An image quantizer that improves on the speed of a standard K-Means algorithm\n * by implementing several optimizations, including deduping identical pixels\n * and a triangle inequality rule that reduces the number of comparisons needed\n * to identify which cluster a point should be moved to.\n *\n * Wsmeans stands for Weighted Square Means.\n *\n * This algorithm was designed by <PERSON><PERSON>, and was found in their 2011\n * paper, Improving the Performance of K-Means for Color Quantization.\n * https://arxiv.org/abs/1101.0395\n */\n// material_color_utilities is designed to have a consistent API across\n// platforms and modular components that can be moved around easily. Using a\n// class as a namespace facilitates this.\n//\n// tslint:disable-next-line:class-as-namespace\nexport class QuantizerWsmeans {\n  /**\n   * @param inputPixels Colors in ARGB format.\n   * @param startingClusters Defines the initial state of the quantizer. Passing\n   *     an empty array is fine, the implementation will create its own initial\n   *     state that leads to reproducible results for the same inputs.\n   *     Passing an array that is the result of Wu quantization leads to higher\n   *     quality results.\n   * @param maxColors The number of colors to divide the image into. A lower\n   *     number of colors may be returned.\n   * @return Colors in ARGB format.\n   */\n  static quantize(\n      inputPixels: number[], startingClusters: number[],\n      maxColors: number): Map<number, number> {\n    const pixelToCount = new Map<number, number>();\n    const points = new Array<number[]>();\n    const pixels = new Array<number>();\n    const pointProvider = new LabPointProvider();\n    let pointCount = 0;\n    for (let i = 0; i < inputPixels.length; i++) {\n      const inputPixel = inputPixels[i];\n      const pixelCount = pixelToCount.get(inputPixel);\n      if (pixelCount === undefined) {\n        pointCount++;\n        points.push(pointProvider.fromInt(inputPixel));\n        pixels.push(inputPixel);\n        pixelToCount.set(inputPixel, 1);\n      } else {\n        pixelToCount.set(inputPixel, pixelCount + 1);\n      }\n    }\n\n    const counts = new Array<number>();\n    for (let i = 0; i < pointCount; i++) {\n      const pixel = pixels[i];\n      const count = pixelToCount.get(pixel);\n      if (count !== undefined) {\n        counts[i] = count;\n      }\n    }\n\n    let clusterCount = Math.min(maxColors, pointCount);\n    if (startingClusters.length > 0) {\n      clusterCount = Math.min(clusterCount, startingClusters.length);\n    }\n\n    const clusters = new Array<number[]>();\n    for (let i = 0; i < startingClusters.length; i++) {\n      clusters.push(pointProvider.fromInt(startingClusters[i]));\n    }\n    const additionalClustersNeeded = clusterCount - clusters.length;\n    if (startingClusters.length === 0 && additionalClustersNeeded > 0) {\n      for (let i = 0; i < additionalClustersNeeded; i++) {\n        const l = Math.random() * 100.0;\n        const a = Math.random() * (100.0 - (-100.0) + 1) + -100;\n        const b = Math.random() * (100.0 - (-100.0) + 1) + -100;\n\n        clusters.push(new Array(l, a, b));\n      }\n    }\n\n    const clusterIndices = new Array<number>();\n    for (let i = 0; i < pointCount; i++) {\n      clusterIndices.push(Math.floor(Math.random() * clusterCount));\n    }\n\n    const indexMatrix = new Array<number[]>();\n    for (let i = 0; i < clusterCount; i++) {\n      indexMatrix.push(new Array<number>());\n      for (let j = 0; j < clusterCount; j++) {\n        indexMatrix[i].push(0);\n      }\n    }\n\n    const distanceToIndexMatrix = new Array<DistanceAndIndex[]>();\n    for (let i = 0; i < clusterCount; i++) {\n      distanceToIndexMatrix.push(new Array<DistanceAndIndex>());\n      for (let j = 0; j < clusterCount; j++) {\n        distanceToIndexMatrix[i].push(new DistanceAndIndex());\n      }\n    }\n\n\n    const pixelCountSums = new Array<number>();\n    for (let i = 0; i < clusterCount; i++) {\n      pixelCountSums.push(0);\n    }\n    for (let iteration = 0; iteration < MAX_ITERATIONS; iteration++) {\n      for (let i = 0; i < clusterCount; i++) {\n        for (let j = i + 1; j < clusterCount; j++) {\n          const distance = pointProvider.distance(clusters[i], clusters[j]);\n          distanceToIndexMatrix[j][i].distance = distance;\n          distanceToIndexMatrix[j][i].index = i;\n          distanceToIndexMatrix[i][j].distance = distance;\n          distanceToIndexMatrix[i][j].index = j;\n        }\n        distanceToIndexMatrix[i].sort();\n        for (let j = 0; j < clusterCount; j++) {\n          indexMatrix[i][j] = distanceToIndexMatrix[i][j].index;\n        }\n      }\n\n      let pointsMoved = 0;\n      for (let i = 0; i < pointCount; i++) {\n        const point = points[i];\n        const previousClusterIndex = clusterIndices[i];\n        const previousCluster = clusters[previousClusterIndex];\n        const previousDistance = pointProvider.distance(point, previousCluster);\n        let minimumDistance = previousDistance;\n        let newClusterIndex = -1;\n        for (let j = 0; j < clusterCount; j++) {\n          if (distanceToIndexMatrix[previousClusterIndex][j].distance >=\n              4 * previousDistance) {\n            continue;\n          }\n          const distance = pointProvider.distance(point, clusters[j]);\n          if (distance < minimumDistance) {\n            minimumDistance = distance;\n            newClusterIndex = j;\n          }\n        }\n        if (newClusterIndex !== -1) {\n          const distanceChange = Math.abs(\n              (Math.sqrt(minimumDistance) - Math.sqrt(previousDistance)));\n          if (distanceChange > MIN_MOVEMENT_DISTANCE) {\n            pointsMoved++;\n            clusterIndices[i] = newClusterIndex;\n          }\n        }\n      }\n\n      if (pointsMoved === 0 && iteration !== 0) {\n        break;\n      }\n\n      const componentASums = new Array<number>(clusterCount).fill(0);\n      const componentBSums = new Array<number>(clusterCount).fill(0);\n      const componentCSums = new Array<number>(clusterCount).fill(0);\n\n      for (let i = 0; i < clusterCount; i++) {\n        pixelCountSums[i] = 0;\n      }\n      for (let i = 0; i < pointCount; i++) {\n        const clusterIndex = clusterIndices[i];\n        const point = points[i];\n        const count = counts[i];\n        pixelCountSums[clusterIndex] += count;\n        componentASums[clusterIndex] += (point[0] * count);\n        componentBSums[clusterIndex] += (point[1] * count);\n        componentCSums[clusterIndex] += (point[2] * count);\n      }\n\n      for (let i = 0; i < clusterCount; i++) {\n        const count = pixelCountSums[i];\n        if (count === 0) {\n          clusters[i] = [0.0, 0.0, 0.0];\n          continue;\n        }\n        const a = componentASums[i] / count;\n        const b = componentBSums[i] / count;\n        const c = componentCSums[i] / count;\n        clusters[i] = [a, b, c];\n      }\n    }\n\n    const argbToPopulation = new Map<number, number>();\n    for (let i = 0; i < clusterCount; i++) {\n      const count = pixelCountSums[i];\n      if (count === 0) {\n        continue;\n      }\n\n      const possibleNewCluster = pointProvider.toInt(clusters[i]);\n      if (argbToPopulation.has(possibleNewCluster)) {\n        continue;\n      }\n\n      argbToPopulation.set(possibleNewCluster, count);\n    }\n    return argbToPopulation;\n  }\n}\n\n/**\n *  A wrapper for maintaining a table of distances between K-Means clusters.\n */\nclass DistanceAndIndex {\n  distance: number = -1;\n  index: number = -1;\n}\n"]}