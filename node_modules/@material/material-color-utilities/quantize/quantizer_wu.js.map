{"version": 3, "file": "quantizer_wu.js", "sourceRoot": "", "sources": ["quantizer_wu.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAK,KAAK,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAEhD,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,WAAW,GAAG,EAAE,CAAC,CAAI,gCAAgC;AAC3D,MAAM,UAAU,GAAG,KAAK,CAAC,CAAE,0CAA0C;AAErE,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,KAAK;IACV,KAAK,EAAE,OAAO;IACd,IAAI,EAAE,MAAM;CACb,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,OAAO,WAAW;IACtB,YACY,UAAoB,EAAE,EAAU,WAAqB,EAAE,EACvD,WAAqB,EAAE,EAAU,WAAqB,EAAE,EACxD,UAAoB,EAAE,EAAU,QAAe,EAAE;QAFjD,YAAO,GAAP,OAAO,CAAe;QAAU,aAAQ,GAAR,QAAQ,CAAe;QACvD,aAAQ,GAAR,QAAQ,CAAe;QAAU,aAAQ,GAAR,QAAQ,CAAe;QACxD,YAAO,GAAP,OAAO,CAAe;QAAU,UAAK,GAAL,KAAK,CAAY;IAAG,CAAC;IAEjE;;;;;OAKG;IACH,QAAQ,CAAC,MAAgB,EAAE,SAAiB;QAC1C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACjE,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,MAAgB;QACzC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhE,MAAM,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEnD,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE;YACnD,MAAM,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEvC,MAAM,YAAY,GAAG,CAAC,GAAG,UAAU,CAAC;YACpC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,EAAE,GAAG,CAAC,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,EAAE,GAAG,CAAC,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAExC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;YACzD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC;YACtC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;SAC1E;IACH,CAAC;IAEO,cAAc;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,WAAW,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,WAAW,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChE,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,WAAW,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChE,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,WAAW,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChE,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,WAAW,EAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;gBACpC,IAAI,IAAI,GAAG,CAAC,CAAC;gBACb,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,IAAI,KAAK,GAAG,GAAG,CAAC;gBAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;oBACpC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC5B,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC9B,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC9B,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC9B,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAE7B,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;oBAChB,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;oBAClB,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;oBAClB,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;oBAClB,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;oBAElB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC5D,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC/D,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC/D,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC/D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;iBAC9D;aACF;SACF;IACH,CAAC;IAEO,WAAW,CAAC,SAAiB;QACnC,IAAI,CAAC,KAAK;YACN,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,SAAS,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACzE,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,SAAS,EAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAErB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;QAEnC,IAAI,mBAAmB,GAAG,SAAS,CAAC;QACpC,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC7C,cAAc,CAAC,IAAI,CAAC;oBAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBACrE,cAAc,CAAC,CAAC,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;aAChE;iBAAM;gBACL,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;gBAC3B,CAAC,EAAE,CAAC;aACL;YAED,IAAI,GAAG,CAAC,CAAC;YACT,IAAI,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;oBAC5B,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,GAAG,CAAC,CAAC;iBACV;aACF;YACD,IAAI,IAAI,IAAI,GAAG,EAAE;gBACf,mBAAmB,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC5B,MAAM;aACP;SACF;QACD,OAAO,IAAI,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;IAC/D,CAAC;IAEO,YAAY,CAAC,UAAkB;QACrC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE;YACnC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,MAAM,GAAG,CAAC,EAAE;gBACd,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC;gBAChE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC;gBAChE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC;gBAChE,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;oBAChE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpB;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,IAAS;QACxB,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,OAAO,EAAE,GAAG,UAAU,GAAG,MAAM,CAAC;IAClC,CAAC;IAEO,GAAG,CAAC,GAAQ,EAAE,GAAQ;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9C,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAC5B,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC/D,MAAM,CAAC,CAAC;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAC5B,GAAG,EAAE,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACjE,MAAM,CAAC,CAAC;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAC5B,GAAG,EAAE,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAChE,MAAM,CAAC,CAAC;QAEZ,IAAI,SAAS,CAAC;QACd,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;QAChC,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;QAChC,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;QAChC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;YAChC,IAAI,UAAU,CAAC,WAAW,GAAG,CAAC,EAAE;gBAC9B,OAAO,KAAK,CAAC;aACd;YACD,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;SAC5B;aAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;YACvC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;SAC9B;aAAM;YACL,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC;SAC7B;QAED,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;QAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;QAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;QAEhB,QAAQ,SAAS,EAAE;YACjB,KAAK,UAAU,CAAC,GAAG;gBACjB,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC;gBAChC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,MAAM;YACR,KAAK,UAAU,CAAC,KAAK;gBACnB,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC;gBAChC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,MAAM;YACR,KAAK,UAAU,CAAC,IAAI;gBAClB,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC;gBAChC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;gBAChB,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,SAAS,CAAC,CAAC;SACxD;QAED,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACpE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,QAAQ,CACZ,IAAS,EAAE,SAAiB,EAAE,KAAa,EAAE,IAAY,EAAE,MAAc,EACzE,MAAc,EAAE,MAAc,EAAE,MAAc;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QAEb,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YACjC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9D,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9D,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9D,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7D,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,SAAS;aACV;YAED,IAAI,aAAa,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;YAC1E,IAAI,eAAe,GAAG,KAAK,GAAG,GAAG,CAAC;YAClC,IAAI,IAAI,GAAG,aAAa,GAAG,eAAe,CAAC;YAE3C,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;YACvB,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;YACvB,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;YACvB,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;YACvB,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,SAAS;aACV;YAED,aAAa,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;YACtE,eAAe,GAAG,KAAK,GAAG,GAAG,CAAC;YAC9B,IAAI,IAAI,aAAa,GAAG,eAAe,CAAC;YAExC,IAAI,IAAI,GAAG,GAAG,EAAE;gBACd,GAAG,GAAG,IAAI,CAAC;gBACX,GAAG,GAAG,CAAC,CAAC;aACT;SACF;QACD,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAEO,MAAM,CAAC,IAAS,EAAE,MAAgB;QACxC,OAAO,CACH,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,MAAM,CAAC,IAAS,EAAE,SAAiB,EAAE,MAAgB;QAC3D,QAAQ,SAAS,EAAE;YACjB,KAAK,UAAU,CAAC,GAAG;gBACjB,OAAO,CACH,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxD,KAAK,UAAU,CAAC,KAAK;gBACnB,OAAO,CACH,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxD,KAAK,UAAU,CAAC,IAAI;gBAClB,OAAO,CACH,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxD;gBACE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACtD;IACH,CAAC;IAEO,GAAG,CACP,IAAS,EAAE,SAAiB,EAAE,QAAgB,EAAE,MAAgB;QAClE,QAAQ,SAAS,EAAE;YACjB,KAAK,UAAU,CAAC,GAAG;gBACjB,OAAO,CACH,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzD,KAAK,UAAU,CAAC,KAAK;gBACnB,OAAO,CACH,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzD,KAAK,UAAU,CAAC,IAAI;gBAClB,OAAO,CACH,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzD;gBACE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACtD;IACH,CAAC;IAEO,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC9C,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACxD,CAAC,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,GAAG;IACP,YACW,KAAa,CAAC,EAAS,KAAa,CAAC,EAAS,KAAa,CAAC,EAC5D,KAAa,CAAC,EAAS,KAAa,CAAC,EAAS,KAAa,CAAC,EAC5D,MAAc,CAAC;QAFf,OAAE,GAAF,EAAE,CAAY;QAAS,OAAE,GAAF,EAAE,CAAY;QAAS,OAAE,GAAF,EAAE,CAAY;QAC5D,OAAE,GAAF,EAAE,CAAY;QAAS,OAAE,GAAF,EAAE,CAAY;QAAS,OAAE,GAAF,EAAE,CAAY;QAC5D,QAAG,GAAH,GAAG,CAAY;IAAG,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,iBAAiB;IACrB;;;;;OAKG;IACH,YAAmB,cAAsB,EAAS,WAAmB;QAAlD,mBAAc,GAAd,cAAc,CAAQ;QAAS,gBAAW,GAAX,WAAW,CAAQ;IAAG,CAAC;CAC1E;AAED;;;GAGG;AACH,MAAM,cAAc;IAClB,YAAmB,WAAmB,EAAS,OAAe;QAA3C,gBAAW,GAAX,WAAW,CAAQ;QAAS,YAAO,GAAP,OAAO,CAAQ;IAAG,CAAC;CACnE", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as utils from '../utils/color_utils.js';\n\nimport {QuantizerMap} from './quantizer_map.js';\n\nconst INDEX_BITS = 5;\nconst SIDE_LENGTH = 33;    // ((1 << INDEX_INDEX_BITS) + 1)\nconst TOTAL_SIZE = 35937;  // SIDE_LENGTH * SIDE_LENGTH * SIDE_LENGTH\n\nconst directions = {\n  RED: 'red',\n  GREEN: 'green',\n  BLUE: 'blue',\n};\n\n/**\n * An image quantizer that divides the image's pixels into clusters by\n * recursively cutting an RGB cube, based on the weight of pixels in each area\n * of the cube.\n *\n * The algorithm was described by <PERSON>lin <PERSON> in Graphic Gems II, published in\n * 1991.\n */\nexport class QuantizerWu {\n  constructor(\n      private weights: number[] = [], private momentsR: number[] = [],\n      private momentsG: number[] = [], private momentsB: number[] = [],\n      private moments: number[] = [], private cubes: Box[] = []) {}\n\n  /**\n   * @param pixels Colors in ARGB format.\n   * @param maxColors The number of colors to divide the image into. A lower\n   *     number of colors may be returned.\n   * @return Colors in ARGB format.\n   */\n  quantize(pixels: number[], maxColors: number): number[] {\n    this.constructHistogram(pixels);\n    this.computeMoments();\n    const createBoxesResult = this.createBoxes(maxColors);\n    const results = this.createResult(createBoxesResult.resultCount);\n    return results;\n  }\n\n  private constructHistogram(pixels: number[]) {\n    this.weights = Array.from<number>({length: TOTAL_SIZE}).fill(0);\n    this.momentsR = Array.from<number>({length: TOTAL_SIZE}).fill(0);\n    this.momentsG = Array.from<number>({length: TOTAL_SIZE}).fill(0);\n    this.momentsB = Array.from<number>({length: TOTAL_SIZE}).fill(0);\n    this.moments = Array.from<number>({length: TOTAL_SIZE}).fill(0);\n\n    const countByColor = QuantizerMap.quantize(pixels);\n\n    for (const [pixel, count] of countByColor.entries()) {\n      const red = utils.redFromArgb(pixel);\n      const green = utils.greenFromArgb(pixel);\n      const blue = utils.blueFromArgb(pixel);\n\n      const bitsToRemove = 8 - INDEX_BITS;\n      const iR = (red >> bitsToRemove) + 1;\n      const iG = (green >> bitsToRemove) + 1;\n      const iB = (blue >> bitsToRemove) + 1;\n      const index = this.getIndex(iR, iG, iB);\n\n      this.weights[index] = (this.weights[index] ?? 0) + count;\n      this.momentsR[index] += count * red;\n      this.momentsG[index] += count * green;\n      this.momentsB[index] += count * blue;\n      this.moments[index] += count * (red * red + green * green + blue * blue);\n    }\n  }\n\n  private computeMoments() {\n    for (let r = 1; r < SIDE_LENGTH; r++) {\n      const area = Array.from<number>({length: SIDE_LENGTH}).fill(0);\n      const areaR = Array.from<number>({length: SIDE_LENGTH}).fill(0);\n      const areaG = Array.from<number>({length: SIDE_LENGTH}).fill(0);\n      const areaB = Array.from<number>({length: SIDE_LENGTH}).fill(0);\n      const area2 = Array.from<number>({length: SIDE_LENGTH}).fill(0.0);\n      for (let g = 1; g < SIDE_LENGTH; g++) {\n        let line = 0;\n        let lineR = 0;\n        let lineG = 0;\n        let lineB = 0;\n        let line2 = 0.0;\n        for (let b = 1; b < SIDE_LENGTH; b++) {\n          const index = this.getIndex(r, g, b);\n          line += this.weights[index];\n          lineR += this.momentsR[index];\n          lineG += this.momentsG[index];\n          lineB += this.momentsB[index];\n          line2 += this.moments[index];\n\n          area[b] += line;\n          areaR[b] += lineR;\n          areaG[b] += lineG;\n          areaB[b] += lineB;\n          area2[b] += line2;\n\n          const previousIndex = this.getIndex(r - 1, g, b);\n          this.weights[index] = this.weights[previousIndex] + area[b];\n          this.momentsR[index] = this.momentsR[previousIndex] + areaR[b];\n          this.momentsG[index] = this.momentsG[previousIndex] + areaG[b];\n          this.momentsB[index] = this.momentsB[previousIndex] + areaB[b];\n          this.moments[index] = this.moments[previousIndex] + area2[b];\n        }\n      }\n    }\n  }\n\n  private createBoxes(maxColors: number): CreateBoxesResult {\n    this.cubes =\n        Array.from<number>({length: maxColors}).fill(0).map(() => new Box());\n    const volumeVariance = Array.from<number>({length: maxColors}).fill(0.0);\n    this.cubes[0].r0 = 0;\n    this.cubes[0].g0 = 0;\n    this.cubes[0].b0 = 0;\n\n    this.cubes[0].r1 = SIDE_LENGTH - 1;\n    this.cubes[0].g1 = SIDE_LENGTH - 1;\n    this.cubes[0].b1 = SIDE_LENGTH - 1;\n\n    let generatedColorCount = maxColors;\n    let next = 0;\n    for (let i = 1; i < maxColors; i++) {\n      if (this.cut(this.cubes[next], this.cubes[i])) {\n        volumeVariance[next] =\n            this.cubes[next].vol > 1 ? this.variance(this.cubes[next]) : 0.0;\n        volumeVariance[i] =\n            this.cubes[i].vol > 1 ? this.variance(this.cubes[i]) : 0.0;\n      } else {\n        volumeVariance[next] = 0.0;\n        i--;\n      }\n\n      next = 0;\n      let temp = volumeVariance[0];\n      for (let j = 1; j <= i; j++) {\n        if (volumeVariance[j] > temp) {\n          temp = volumeVariance[j];\n          next = j;\n        }\n      }\n      if (temp <= 0.0) {\n        generatedColorCount = i + 1;\n        break;\n      }\n    }\n    return new CreateBoxesResult(maxColors, generatedColorCount);\n  }\n\n  private createResult(colorCount: number): number[] {\n    const colors: number[] = [];\n    for (let i = 0; i < colorCount; ++i) {\n      const cube = this.cubes[i];\n      const weight = this.volume(cube, this.weights);\n      if (weight > 0) {\n        const r = Math.round(this.volume(cube, this.momentsR) / weight);\n        const g = Math.round(this.volume(cube, this.momentsG) / weight);\n        const b = Math.round(this.volume(cube, this.momentsB) / weight);\n        const color = (255 << 24) | ((r & 0x0ff) << 16) | ((g & 0x0ff) << 8) |\n            (b & 0x0ff);\n        colors.push(color);\n      }\n    }\n    return colors;\n  }\n\n  private variance(cube: Box) {\n    const dr = this.volume(cube, this.momentsR);\n    const dg = this.volume(cube, this.momentsG);\n    const db = this.volume(cube, this.momentsB);\n    const xx = this.moments[this.getIndex(cube.r1, cube.g1, cube.b1)] -\n        this.moments[this.getIndex(cube.r1, cube.g1, cube.b0)] -\n        this.moments[this.getIndex(cube.r1, cube.g0, cube.b1)] +\n        this.moments[this.getIndex(cube.r1, cube.g0, cube.b0)] -\n        this.moments[this.getIndex(cube.r0, cube.g1, cube.b1)] +\n        this.moments[this.getIndex(cube.r0, cube.g1, cube.b0)] +\n        this.moments[this.getIndex(cube.r0, cube.g0, cube.b1)] -\n        this.moments[this.getIndex(cube.r0, cube.g0, cube.b0)];\n    const hypotenuse = dr * dr + dg * dg + db * db;\n    const volume = this.volume(cube, this.weights);\n    return xx - hypotenuse / volume;\n  }\n\n  private cut(one: Box, two: Box) {\n    const wholeR = this.volume(one, this.momentsR);\n    const wholeG = this.volume(one, this.momentsG);\n    const wholeB = this.volume(one, this.momentsB);\n    const wholeW = this.volume(one, this.weights);\n\n    const maxRResult = this.maximize(\n        one, directions.RED, one.r0 + 1, one.r1, wholeR, wholeG, wholeB,\n        wholeW);\n    const maxGResult = this.maximize(\n        one, directions.GREEN, one.g0 + 1, one.g1, wholeR, wholeG, wholeB,\n        wholeW);\n    const maxBResult = this.maximize(\n        one, directions.BLUE, one.b0 + 1, one.b1, wholeR, wholeG, wholeB,\n        wholeW);\n\n    let direction;\n    const maxR = maxRResult.maximum;\n    const maxG = maxGResult.maximum;\n    const maxB = maxBResult.maximum;\n    if (maxR >= maxG && maxR >= maxB) {\n      if (maxRResult.cutLocation < 0) {\n        return false;\n      }\n      direction = directions.RED;\n    } else if (maxG >= maxR && maxG >= maxB) {\n      direction = directions.GREEN;\n    } else {\n      direction = directions.BLUE;\n    }\n\n    two.r1 = one.r1;\n    two.g1 = one.g1;\n    two.b1 = one.b1;\n\n    switch (direction) {\n      case directions.RED:\n        one.r1 = maxRResult.cutLocation;\n        two.r0 = one.r1;\n        two.g0 = one.g0;\n        two.b0 = one.b0;\n        break;\n      case directions.GREEN:\n        one.g1 = maxGResult.cutLocation;\n        two.r0 = one.r0;\n        two.g0 = one.g1;\n        two.b0 = one.b0;\n        break;\n      case directions.BLUE:\n        one.b1 = maxBResult.cutLocation;\n        two.r0 = one.r0;\n        two.g0 = one.g0;\n        two.b0 = one.b1;\n        break;\n      default:\n        throw new Error('unexpected direction ' + direction);\n    }\n\n    one.vol = (one.r1 - one.r0) * (one.g1 - one.g0) * (one.b1 - one.b0);\n    two.vol = (two.r1 - two.r0) * (two.g1 - two.g0) * (two.b1 - two.b0);\n    return true;\n  }\n\n  private maximize(\n      cube: Box, direction: string, first: number, last: number, wholeR: number,\n      wholeG: number, wholeB: number, wholeW: number) {\n    const bottomR = this.bottom(cube, direction, this.momentsR);\n    const bottomG = this.bottom(cube, direction, this.momentsG);\n    const bottomB = this.bottom(cube, direction, this.momentsB);\n    const bottomW = this.bottom(cube, direction, this.weights);\n\n    let max = 0.0;\n    let cut = -1;\n\n    let halfR = 0;\n    let halfG = 0;\n    let halfB = 0;\n    let halfW = 0;\n    for (let i = first; i < last; i++) {\n      halfR = bottomR + this.top(cube, direction, i, this.momentsR);\n      halfG = bottomG + this.top(cube, direction, i, this.momentsG);\n      halfB = bottomB + this.top(cube, direction, i, this.momentsB);\n      halfW = bottomW + this.top(cube, direction, i, this.weights);\n      if (halfW === 0) {\n        continue;\n      }\n\n      let tempNumerator = (halfR * halfR + halfG * halfG + halfB * halfB) * 1.0;\n      let tempDenominator = halfW * 1.0;\n      let temp = tempNumerator / tempDenominator;\n\n      halfR = wholeR - halfR;\n      halfG = wholeG - halfG;\n      halfB = wholeB - halfB;\n      halfW = wholeW - halfW;\n      if (halfW === 0) {\n        continue;\n      }\n\n      tempNumerator = (halfR * halfR + halfG * halfG + halfB * halfB) * 1.0;\n      tempDenominator = halfW * 1.0;\n      temp += tempNumerator / tempDenominator;\n\n      if (temp > max) {\n        max = temp;\n        cut = i;\n      }\n    }\n    return new MaximizeResult(cut, max);\n  }\n\n  private volume(cube: Box, moment: number[]) {\n    return (\n        moment[this.getIndex(cube.r1, cube.g1, cube.b1)] -\n        moment[this.getIndex(cube.r1, cube.g1, cube.b0)] -\n        moment[this.getIndex(cube.r1, cube.g0, cube.b1)] +\n        moment[this.getIndex(cube.r1, cube.g0, cube.b0)] -\n        moment[this.getIndex(cube.r0, cube.g1, cube.b1)] +\n        moment[this.getIndex(cube.r0, cube.g1, cube.b0)] +\n        moment[this.getIndex(cube.r0, cube.g0, cube.b1)] -\n        moment[this.getIndex(cube.r0, cube.g0, cube.b0)]);\n  }\n\n  private bottom(cube: Box, direction: string, moment: number[]) {\n    switch (direction) {\n      case directions.RED:\n        return (\n            -moment[this.getIndex(cube.r0, cube.g1, cube.b1)] +\n            moment[this.getIndex(cube.r0, cube.g1, cube.b0)] +\n            moment[this.getIndex(cube.r0, cube.g0, cube.b1)] -\n            moment[this.getIndex(cube.r0, cube.g0, cube.b0)]);\n      case directions.GREEN:\n        return (\n            -moment[this.getIndex(cube.r1, cube.g0, cube.b1)] +\n            moment[this.getIndex(cube.r1, cube.g0, cube.b0)] +\n            moment[this.getIndex(cube.r0, cube.g0, cube.b1)] -\n            moment[this.getIndex(cube.r0, cube.g0, cube.b0)]);\n      case directions.BLUE:\n        return (\n            -moment[this.getIndex(cube.r1, cube.g1, cube.b0)] +\n            moment[this.getIndex(cube.r1, cube.g0, cube.b0)] +\n            moment[this.getIndex(cube.r0, cube.g1, cube.b0)] -\n            moment[this.getIndex(cube.r0, cube.g0, cube.b0)]);\n      default:\n        throw new Error('unexpected direction $direction');\n    }\n  }\n\n  private top(\n      cube: Box, direction: string, position: number, moment: number[]) {\n    switch (direction) {\n      case directions.RED:\n        return (\n            moment[this.getIndex(position, cube.g1, cube.b1)] -\n            moment[this.getIndex(position, cube.g1, cube.b0)] -\n            moment[this.getIndex(position, cube.g0, cube.b1)] +\n            moment[this.getIndex(position, cube.g0, cube.b0)]);\n      case directions.GREEN:\n        return (\n            moment[this.getIndex(cube.r1, position, cube.b1)] -\n            moment[this.getIndex(cube.r1, position, cube.b0)] -\n            moment[this.getIndex(cube.r0, position, cube.b1)] +\n            moment[this.getIndex(cube.r0, position, cube.b0)]);\n      case directions.BLUE:\n        return (\n            moment[this.getIndex(cube.r1, cube.g1, position)] -\n            moment[this.getIndex(cube.r1, cube.g0, position)] -\n            moment[this.getIndex(cube.r0, cube.g1, position)] +\n            moment[this.getIndex(cube.r0, cube.g0, position)]);\n      default:\n        throw new Error('unexpected direction $direction');\n    }\n  }\n\n  private getIndex(r: number, g: number, b: number): number {\n    return (r << (INDEX_BITS * 2)) + (r << (INDEX_BITS + 1)) + r +\n        (g << INDEX_BITS) + g + b;\n  }\n}\n\n/**\n * Keeps track of the state of each box created as the Wu  quantization\n * algorithm progresses through dividing the image's pixels as plotted in RGB.\n */\nclass Box {\n  constructor(\n      public r0: number = 0, public r1: number = 0, public g0: number = 0,\n      public g1: number = 0, public b0: number = 0, public b1: number = 0,\n      public vol: number = 0) {}\n}\n\n/**\n * Represents final result of Wu algorithm.\n */\nclass CreateBoxesResult {\n  /**\n   * @param requestedCount how many colors the caller asked to be returned from\n   *     quantization.\n   * @param resultCount the actual number of colors achieved from quantization.\n   *     May be lower than the requested count.\n   */\n  constructor(public requestedCount: number, public resultCount: number) {}\n}\n\n/**\n * Represents the result of calculating where to cut an existing box in such\n * a way to maximize variance between the two new boxes created by a cut.\n */\nclass MaximizeResult {\n  constructor(public cutLocation: number, public maximum: number) {}\n}\n"]}