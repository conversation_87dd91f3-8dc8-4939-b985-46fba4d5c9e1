{"version": 3, "file": "quantizer_map.js", "sourceRoot": "", "sources": ["quantizer_map.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAK,KAAK,MAAM,yBAAyB,CAAC;AAEjD;;;GAGG;AACH,uEAAuE;AACvE,4EAA4E;AAC5E,yCAAyC;AACzC,EAAE;AACF,8CAA8C;AAC9C,MAAM,OAAO,YAAY;IACvB;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,MAAgB;QAC9B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACzC,IAAI,KAAK,GAAG,GAAG,EAAE;gBACf,SAAS;aACV;YACD,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7D;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as utils from '../utils/color_utils.js';\n\n/**\n * Quantizes an image into a map, with keys of ARGB colors, and values of the\n * number of times that color appears in the image.\n */\n// material_color_utilities is designed to have a consistent API across\n// platforms and modular components that can be moved around easily. Using a\n// class as a namespace facilitates this.\n//\n// tslint:disable-next-line:class-as-namespace\nexport class QuantizerMap {\n  /**\n   * @param pixels Colors in ARGB format.\n   * @return A Map with keys of ARGB colors, and values of the number of times\n   *     the color appears in the image.\n   */\n  static quantize(pixels: number[]): Map<number, number> {\n    const countByColor = new Map<number, number>();\n    for (let i = 0; i < pixels.length; i++) {\n      const pixel = pixels[i];\n      const alpha = utils.alphaFromArgb(pixel);\n      if (alpha < 255) {\n        continue;\n      }\n      countByColor.set(pixel, (countByColor.get(pixel) ?? 0) + 1);\n    }\n    return countByColor;\n  }\n}\n"]}