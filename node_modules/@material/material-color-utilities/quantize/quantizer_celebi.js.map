{"version": 3, "file": "quantizer_celebi.js", "sourceRoot": "", "sources": ["quantizer_celebi.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,gBAAgB,EAAC,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAC,WAAW,EAAC,MAAM,mBAAmB,CAAC;AAE9C;;;;;;;;;;GAUG;AACH,uEAAuE;AACvE,4EAA4E;AAC5E,yCAAyC;AACzC,EAAE;AACF,8CAA8C;AAC9C,MAAM,OAAO,eAAe;IAC1B;;;;;;;OAOG;IACH,MAAM,CAAC,QAAQ,CAAC,MAAgB,EAAE,SAAiB;QACjD,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAChD,OAAO,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {QuantizerWsmeans} from './quantizer_wsmeans.js';\nimport {QuantizerWu} from './quantizer_wu.js';\n\n/**\n * An image quantizer that improves on the quality of a standard K-Means\n * algorithm by setting the K-Means initial state to the output of a Wu\n * quantizer, instead of random centroids. Improves on speed by several\n * optimizations, as implemented in Wsmeans, or Weighted Square Means, K-Means\n * with those optimizations.\n *\n * This algorithm was designed by <PERSON><PERSON>, and was found in their 2011\n * paper, Improving the Performance of K-Means for Color Quantization.\n * https://arxiv.org/abs/1101.0395\n */\n// material_color_utilities is designed to have a consistent API across\n// platforms and modular components that can be moved around easily. Using a\n// class as a namespace facilitates this.\n//\n// tslint:disable-next-line:class-as-namespace\nexport class QuantizerCelebi {\n  /**\n   * @param pixels Colors in ARGB format.\n   * @param maxColors The number of colors to divide the image into. A lower\n   *     number of colors may be returned.\n   * @return Map with keys of colors in ARGB format, and values of number of\n   *     pixels in the original image that correspond to the color in the\n   *     quantized image.\n   */\n  static quantize(pixels: number[], maxColors: number): Map<number, number> {\n    const wu = new QuantizerWu();\n    const wuResult = wu.quantize(pixels, maxColors);\n    return QuantizerWsmeans.quantize(pixels, wuResult, maxColors);\n  }\n}\n"]}