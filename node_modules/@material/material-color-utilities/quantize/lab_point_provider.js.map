{"version": 3, "file": "lab_point_provider.js", "sourceRoot": "", "sources": ["lab_point_provider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAK,KAAK,MAAM,yBAAyB,CAAC;AAIjD;;;GAGG;AACH,MAAM,OAAO,gBAAgB;IAC3B;;;OAGG;IACH,OAAO,CAAC,IAAY;QAClB,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAe;QACnB,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;OAOG;IACH,QAAQ,CAAC,IAAc,EAAE,EAAY;QACnC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3B,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACrC,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as utils from '../utils/color_utils.js';\n\nimport {PointProvider} from './point_provider.js';\n\n/**\n * Provides conversions needed for K-Means quantization. Converting input to\n * points, and converting the final state of the K-Means algorithm to colors.\n */\nexport class LabPointProvider implements PointProvider {\n  /**\n   * Convert a color represented in ARGB to a 3-element array of L*a*b*\n   * coordinates of the color.\n   */\n  fromInt(argb: number): number[] {\n    return utils.labFromArgb(argb);\n  }\n\n  /**\n   * Convert a 3-element array to a color represented in ARGB.\n   */\n  toInt(point: number[]): number {\n    return utils.argbFromLab(point[0], point[1], point[2]);\n  }\n\n  /**\n   * Standard CIE 1976 delta E formula also takes the square root, unneeded\n   * here. This method is used by quantization algorithms to compare distance,\n   * and the relative ordering is the same, with or without a square root.\n   *\n   * This relatively minor optimization is helpful because this method is\n   * called at least once for each pixel in an image.\n   */\n  distance(from: number[], to: number[]): number {\n    const dL = from[0] - to[0];\n    const dA = from[1] - to[1];\n    const dB = from[2] - to[2];\n    return dL * dL + dA * dA + dB * dB;\n  }\n}\n"]}