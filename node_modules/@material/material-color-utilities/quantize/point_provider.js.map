{"version": 3, "file": "point_provider.js", "sourceRoot": "", "sources": ["point_provider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * An interface to allow use of different color spaces by\n * quantizers.\n */\n\nexport declare interface PointProvider {\n  toInt(point: number[]): number;\n  fromInt(argb: number): number[];\n  distance(from: number[], to: number[]): number;\n}\n"]}