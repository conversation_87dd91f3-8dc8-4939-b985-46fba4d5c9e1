{"version": 3, "file": "dynamic_color.js", "sourceRoot": "", "sources": ["dynamic_color.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,yBAAyB,CAAC;AAIjD,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAoC/C;;;;;;;;;;GAUG;AACH,MAAM,OAAO,YAAY;IAGvB;;;;;OAKG;IACH,MAAM,CAAC,WAAW,CAAC,IAAwB;QACzC,OAAO,IAAI,YAAY,CACnB,IAAI,CAAC,IAAI,IAAI,EAAE,EACf,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,YAAY,IAAI,KAAK,EAC1B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,aAAa,CACrB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,YACa,IAAY,EACZ,OAAgD,EAChD,IAAuC,EACvC,YAAqB,EACrB,UAAoD,EACpD,gBAA0D,EAC1D,aAA6B,EAC7B,aAAwD;QAPxD,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAyC;QAChD,SAAI,GAAJ,IAAI,CAAmC;QACvC,iBAAY,GAAZ,YAAY,CAAS;QACrB,eAAU,GAAV,UAAU,CAA0C;QACpD,qBAAgB,GAAhB,gBAAgB,CAA0C;QAC1D,kBAAa,GAAb,aAAa,CAAgB;QAC7B,kBAAa,GAAb,aAAa,CAA2C;QA7DpD,aAAQ,GAAG,IAAI,GAAG,EAAsB,CAAC;QA+DxD,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,gBAAgB,EAAE;YACrC,MAAM,IAAI,KAAK,CACX,SAAS,IAAI,uBAAuB;gBACpC,yCAAyC,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,aAAa,EAAE;YAClC,MAAM,IAAI,KAAK,CACX,SAAS,IAAI,oBAAoB;gBACjC,yCAAyC,CAAC,CAAC;SAChD;QACD,IAAI,UAAU,IAAI,CAAC,aAAa,EAAE;YAChC,MAAM,IAAI,KAAK,CACX,SAAS,IAAI,iBAAiB;gBAC9B,4CAA4C,CAAC,CAAC;SACnD;IACH,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CAAC,MAAqB;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,MAAqB;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,OAAO,YAAY,CAAC;SACrB;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;SACvB;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,OAAO,CAAC,MAAqB;QAC3B,MAAM,kBAAkB,GAAG,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;QAEpD,iEAAiE;QACjE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;YAClC,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;YAClC,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;YAClC,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;YACxC,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YAEhD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAW,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAElC,MAAM,SAAS,GACX,CAAC,QAAQ,KAAK,QAAQ;gBACrB,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC1C,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;YACzC,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;YAC3C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5C,gCAAgC;YAChC,MAAM,SAAS,GAAG,MAAM,CAAC,aAAc,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC1E,MAAM,SAAS,GACX,OAAO,CAAC,aAAc,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAE7D,iDAAiD;YACjD,0CAA0C;YAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzC,IAAI,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,IAAI,SAAS,CAAC,CAAC;gBAClE,YAAY,CAAC,CAAC;gBACd,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACnD,2CAA2C;YAC3C,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,IAAI,SAAS,CAAC,CAAC;gBAClE,YAAY,CAAC,CAAC;gBACd,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAEnD,IAAI,kBAAkB,EAAE;gBACtB,6DAA6D;gBAC7D,2BAA2B;gBAC3B,KAAK,GAAG,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBACvD,KAAK,GAAG,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;aACxD;YAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,YAAY,IAAI,KAAK,EAAE;gBAC3C,wDAAwD;aACzD;iBAAM;gBACL,4CAA4C;gBAC5C,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;gBAC/D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,YAAY,IAAI,KAAK,EAAE;oBAC3C,4DAA4D;iBAC7D;qBAAM;oBACL,6CAA6C;oBAC7C,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;iBAChE;aACF;YAED,iCAAiC;YACjC,IAAI,EAAE,IAAI,KAAK,IAAI,KAAK,GAAG,EAAE,EAAE;gBAC7B,kEAAkE;gBAClE,aAAa;gBACb,IAAI,YAAY,GAAG,CAAC,EAAE;oBACpB,KAAK,GAAG,EAAE,CAAC;oBACX,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;iBACvD;qBAAM;oBACL,KAAK,GAAG,EAAE,CAAC;oBACX,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;iBACvD;aACF;iBAAM,IAAI,EAAE,IAAI,KAAK,IAAI,KAAK,GAAG,EAAE,EAAE;gBACpC,IAAI,YAAY,EAAE;oBAChB,oEAAoE;oBACpE,SAAS;oBACT,IAAI,YAAY,GAAG,CAAC,EAAE;wBACpB,KAAK,GAAG,EAAE,CAAC;wBACX,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;qBACvD;yBAAM;wBACL,KAAK,GAAG,EAAE,CAAC;wBACX,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;qBACvD;iBACF;qBAAM;oBACL,iDAAiD;oBACjD,IAAI,YAAY,GAAG,CAAC,EAAE;wBACpB,KAAK,GAAG,EAAE,CAAC;qBACZ;yBAAM;wBACL,KAAK,GAAG,EAAE,CAAC;qBACZ;iBACF;aACF;YAED,gEAAgE;YAChE,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;SACjC;aAEI;YACH,mDAAmD;YACnD,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE/B,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;gBAC3B,OAAO,MAAM,CAAC,CAAE,+CAA+C;aAChE;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEvD,MAAM,YAAY,GACd,IAAI,CAAC,aAAc,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAE1D,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,YAAY,EAAE;gBACzD,sCAAsC;aACvC;iBAAM;gBACL,qBAAqB;gBACrB,MAAM,GAAG,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;aAC5D;YAED,IAAI,kBAAkB,EAAE;gBACtB,MAAM,GAAG,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;aAC5D;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE,EAAE;gBACpD,cAAc;gBACd,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,YAAY,EAAE;oBACrD,MAAM,GAAG,EAAE,CAAC;iBACb;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAC;iBACb;aACF;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,uCAAuC;gBAEvC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC5D,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GACpB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC/D,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAChB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;gBAE7D,IAAI,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,YAAY;oBACpD,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,YAAY,EAAE;oBACxD,OAAO,MAAM,CAAC;iBACf;gBAED,2DAA2D;gBAC3D,yCAAyC;gBACzC,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;gBAE1D,2DAA2D;gBAC3D,yCAAyC;gBACzC,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;gBAExD,qCAAqC;gBACrC,MAAM,UAAU,GAAG,EAAE,CAAC;gBACtB,IAAI,WAAW,KAAK,CAAC,CAAC;oBAAE,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrD,IAAI,UAAU,KAAK,CAAC,CAAC;oBAAE,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAEnD,MAAM,YAAY,GAAG,YAAY,CAAC,0BAA0B,CAAC,OAAO,CAAC;oBACjE,YAAY,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;gBACrD,IAAI,YAAY,EAAE;oBAChB,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;iBAC9C;gBACD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC3B,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;iBACtB;gBACD,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;aAC1C;YAED,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,cAAc,CAAC,MAAc,EAAE,KAAa;QACjD,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1D,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,YAAY,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAEtE,IAAI,aAAa,EAAE;YACjB,qEAAqE;YACrE,gEAAgE;YAChE,kEAAkE;YAClE,SAAS;YACT,EAAE;YACF,mEAAmE;YACnE,sEAAsE;YACtE,mEAAmE;YACnE,yCAAyC;YACzC,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,GAAG;gBACnE,YAAY,GAAG,KAAK,IAAI,WAAW,GAAG,KAAK,CAAC;YAChD,OAAO,YAAY,IAAI,KAAK,IAAI,YAAY,IAAI,WAAW;gBACnD,oBAAoB,CAAC,CAAC;gBAC1B,WAAW,CAAC,CAAC;gBACb,UAAU,CAAC;SAChB;aAAM;YACL,OAAO,WAAW,IAAI,KAAK,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBACZ,WAAW,CAAC;SAC1E;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACH,MAAM,CAAC,0BAA0B,CAAC,IAAY;QAC5C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,yBAAyB,CAAC,IAAY;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,qBAAqB,CAAC,IAAY;QACvC,IAAI,YAAY,CAAC,0BAA0B,CAAC,IAAI,CAAC;YAC7C,CAAC,YAAY,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE;YACjD,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Contrast} from '../contrast/contrast.js';\nimport {Hct} from '../hct/hct.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\nimport {DynamicScheme} from '../scheme/dynamic_scheme.js';\nimport * as math from '../utils/math_utils.js';\n\nimport {ContrastCurve} from './contrast_curve.js';\nimport {ToneDeltaPair} from './tone_delta_pair.js';\n\n/**\n * @param name The name of the dynamic color. Defaults to empty.\n * @param palette Function that provides a TonalPalette given\n * DynamicScheme. A TonalPalette is defined by a hue and chroma, so this\n * replaces the need to specify hue/chroma. By providing a tonal palette, when\n * contrast adjustments are made, intended chroma can be preserved.\n * @param tone Function that provides a tone given DynamicScheme.\n * @param isBackground Whether this dynamic color is a background, with\n * some other color as the foreground. Defaults to false.\n * @param background The background of the dynamic color (as a function of a\n *     `DynamicScheme`), if it exists.\n * @param secondBackground A second background of the dynamic color (as a\n *     function of a `DynamicScheme`), if it\n * exists.\n * @param contrastCurve A `ContrastCurve` object specifying how its contrast\n * against its background should behave in various contrast levels options.\n * @param toneDeltaPair A `ToneDeltaPair` object specifying a tone delta\n * constraint between two colors. One of them must be the color being\n * constructed.\n */\ninterface FromPaletteOptions {\n  name?: string;\n  palette: (scheme: DynamicScheme) => TonalPalette;\n  tone: (scheme: DynamicScheme) => number;\n  isBackground?: boolean;\n  background?: (scheme: DynamicScheme) => DynamicColor;\n  secondBackground?: (scheme: DynamicScheme) => DynamicColor;\n  contrastCurve?: ContrastCurve;\n  toneDeltaPair?: (scheme: DynamicScheme) => ToneDeltaPair;\n}\n\n/**\n * A color that adjusts itself based on UI state provided by DynamicScheme.\n *\n * Colors without backgrounds do not change tone when contrast changes. Colors\n * with backgrounds become closer to their background as contrast lowers, and\n * further when contrast increases.\n *\n * Prefer static constructors. They require either a hexcode, a palette and\n * tone, or a hue and chroma. Optionally, they can provide a background\n * DynamicColor.\n */\nexport class DynamicColor {\n  private readonly hctCache = new Map<DynamicScheme, Hct>();\n\n  /**\n   * Create a DynamicColor defined by a TonalPalette and HCT tone.\n   *\n   * @param args Functions with DynamicScheme as input. Must provide a palette\n   * and tone. May provide a background DynamicColor and ToneDeltaConstraint.\n   */\n  static fromPalette(args: FromPaletteOptions): DynamicColor {\n    return new DynamicColor(\n        args.name ?? '',\n        args.palette,\n        args.tone,\n        args.isBackground ?? false,\n        args.background,\n        args.secondBackground,\n        args.contrastCurve,\n        args.toneDeltaPair,\n    );\n  }\n\n  /**\n   * The base constructor for DynamicColor.\n   *\n   * _Strongly_ prefer using one of the convenience constructors. This class is\n   * arguably too flexible to ensure it can support any scenario. Functional\n   * arguments allow  overriding without risks that come with subclasses.\n   *\n   * For example, the default behavior of adjust tone at max contrast\n   * to be at a 7.0 ratio with its background is principled and\n   * matches accessibility guidance. That does not mean it's the desired\n   * approach for _every_ design system, and every color pairing,\n   * always, in every case.\n   *\n   * @param name The name of the dynamic color. Defaults to empty.\n   * @param palette Function that provides a TonalPalette given\n   * DynamicScheme. A TonalPalette is defined by a hue and chroma, so this\n   * replaces the need to specify hue/chroma. By providing a tonal palette, when\n   * contrast adjustments are made, intended chroma can be preserved.\n   * @param tone Function that provides a tone, given a DynamicScheme.\n   * @param isBackground Whether this dynamic color is a background, with\n   * some other color as the foreground. Defaults to false.\n   * @param background The background of the dynamic color (as a function of a\n   *     `DynamicScheme`), if it exists.\n   * @param secondBackground A second background of the dynamic color (as a\n   *     function of a `DynamicScheme`), if it\n   * exists.\n   * @param contrastCurve A `ContrastCurve` object specifying how its contrast\n   * against its background should behave in various contrast levels options.\n   * @param toneDeltaPair A `ToneDeltaPair` object specifying a tone delta\n   * constraint between two colors. One of them must be the color being\n   * constructed.\n   */\n  constructor(\n      readonly name: string,\n      readonly palette: (scheme: DynamicScheme) => TonalPalette,\n      readonly tone: (scheme: DynamicScheme) => number,\n      readonly isBackground: boolean,\n      readonly background?: (scheme: DynamicScheme) => DynamicColor,\n      readonly secondBackground?: (scheme: DynamicScheme) => DynamicColor,\n      readonly contrastCurve?: ContrastCurve,\n      readonly toneDeltaPair?: (scheme: DynamicScheme) => ToneDeltaPair,\n  ) {\n    if ((!background) && secondBackground) {\n      throw new Error(\n          `Color ${name} has secondBackground` +\n          `defined, but background is not defined.`);\n    }\n    if ((!background) && contrastCurve) {\n      throw new Error(\n          `Color ${name} has contrastCurve` +\n          `defined, but background is not defined.`);\n    }\n    if (background && !contrastCurve) {\n      throw new Error(\n          `Color ${name} has background` +\n          `defined, but contrastCurve is not defined.`);\n    }\n  }\n\n  /**\n   * Return a ARGB integer (i.e. a hex code).\n   *\n   * @param scheme Defines the conditions of the user interface, for example,\n   * whether or not it is dark mode or light mode, and what the desired\n   * contrast level is.\n   */\n  getArgb(scheme: DynamicScheme): number {\n    return this.getHct(scheme).toInt();\n  }\n\n  /**\n   * Return a color, expressed in the HCT color space, that this\n   * DynamicColor is under the conditions in scheme.\n   *\n   * @param scheme Defines the conditions of the user interface, for example,\n   * whether or not it is dark mode or light mode, and what the desired\n   * contrast level is.\n   */\n  getHct(scheme: DynamicScheme): Hct {\n    const cachedAnswer = this.hctCache.get(scheme);\n    if (cachedAnswer != null) {\n      return cachedAnswer;\n    }\n    const tone = this.getTone(scheme);\n    const answer = this.palette(scheme).getHct(tone);\n    if (this.hctCache.size > 4) {\n      this.hctCache.clear();\n    }\n    this.hctCache.set(scheme, answer);\n    return answer;\n  }\n\n  /**\n   * Return a tone, T in the HCT color space, that this DynamicColor is under\n   * the conditions in scheme.\n   *\n   * @param scheme Defines the conditions of the user interface, for example,\n   * whether or not it is dark mode or light mode, and what the desired\n   * contrast level is.\n   */\n  getTone(scheme: DynamicScheme): number {\n    const decreasingContrast = scheme.contrastLevel < 0;\n\n    // Case 1: dual foreground, pair of colors with delta constraint.\n    if (this.toneDeltaPair) {\n      const toneDeltaPair = this.toneDeltaPair(scheme);\n      const roleA = toneDeltaPair.roleA;\n      const roleB = toneDeltaPair.roleB;\n      const delta = toneDeltaPair.delta;\n      const polarity = toneDeltaPair.polarity;\n      const stayTogether = toneDeltaPair.stayTogether;\n\n      const bg = this.background!(scheme);\n      const bgTone = bg.getTone(scheme);\n\n      const aIsNearer =\n          (polarity === 'nearer' ||\n           (polarity === 'lighter' && !scheme.isDark) ||\n           (polarity === 'darker' && scheme.isDark));\n      const nearer = aIsNearer ? roleA : roleB;\n      const farther = aIsNearer ? roleB : roleA;\n      const amNearer = this.name === nearer.name;\n      const expansionDir = scheme.isDark ? 1 : -1;\n\n      // 1st round: solve to min, each\n      const nContrast = nearer.contrastCurve!.getContrast(scheme.contrastLevel);\n      const fContrast =\n          farther.contrastCurve!.getContrast(scheme.contrastLevel);\n\n      // If a color is good enough, it is not adjusted.\n      // Initial and adjusted tones for `nearer`\n      const nInitialTone = nearer.tone(scheme);\n      let nTone = Contrast.ratioOfTones(bgTone, nInitialTone) >= nContrast ?\n          nInitialTone :\n          DynamicColor.foregroundTone(bgTone, nContrast);\n      // Initial and adjusted tones for `farther`\n      const fInitialTone = farther.tone(scheme);\n      let fTone = Contrast.ratioOfTones(bgTone, fInitialTone) >= fContrast ?\n          fInitialTone :\n          DynamicColor.foregroundTone(bgTone, fContrast);\n\n      if (decreasingContrast) {\n        // If decreasing contrast, adjust color to the \"bare minimum\"\n        // that satisfies contrast.\n        nTone = DynamicColor.foregroundTone(bgTone, nContrast);\n        fTone = DynamicColor.foregroundTone(bgTone, fContrast);\n      }\n\n      if ((fTone - nTone) * expansionDir >= delta) {\n        // Good! Tones satisfy the constraint; no change needed.\n      } else {\n        // 2nd round: expand farther to match delta.\n        fTone = math.clampDouble(0, 100, nTone + delta * expansionDir);\n        if ((fTone - nTone) * expansionDir >= delta) {\n          // Good! Tones now satisfy the constraint; no change needed.\n        } else {\n          // 3rd round: contract nearer to match delta.\n          nTone = math.clampDouble(0, 100, fTone - delta * expansionDir);\n        }\n      }\n\n      // Avoids the 50-59 awkward zone.\n      if (50 <= nTone && nTone < 60) {\n        // If `nearer` is in the awkward zone, move it away, together with\n        // `farther`.\n        if (expansionDir > 0) {\n          nTone = 60;\n          fTone = Math.max(fTone, nTone + delta * expansionDir);\n        } else {\n          nTone = 49;\n          fTone = Math.min(fTone, nTone + delta * expansionDir);\n        }\n      } else if (50 <= fTone && fTone < 60) {\n        if (stayTogether) {\n          // Fixes both, to avoid two colors on opposite sides of the \"awkward\n          // zone\".\n          if (expansionDir > 0) {\n            nTone = 60;\n            fTone = Math.max(fTone, nTone + delta * expansionDir);\n          } else {\n            nTone = 49;\n            fTone = Math.min(fTone, nTone + delta * expansionDir);\n          }\n        } else {\n          // Not required to stay together; fixes just one.\n          if (expansionDir > 0) {\n            fTone = 60;\n          } else {\n            fTone = 49;\n          }\n        }\n      }\n\n      // Returns `nTone` if this color is `nearer`, otherwise `fTone`.\n      return amNearer ? nTone : fTone;\n    }\n\n    else {\n      // Case 2: No contrast pair; just solve for itself.\n      let answer = this.tone(scheme);\n\n      if (this.background == null) {\n        return answer;  // No adjustment for colors with no background.\n      }\n\n      const bgTone = this.background(scheme).getTone(scheme);\n\n      const desiredRatio =\n          this.contrastCurve!.getContrast(scheme.contrastLevel);\n\n      if (Contrast.ratioOfTones(bgTone, answer) >= desiredRatio) {\n        // Don't \"improve\" what's good enough.\n      } else {\n        // Rough improvement.\n        answer = DynamicColor.foregroundTone(bgTone, desiredRatio);\n      }\n\n      if (decreasingContrast) {\n        answer = DynamicColor.foregroundTone(bgTone, desiredRatio);\n      }\n\n      if (this.isBackground && 50 <= answer && answer < 60) {\n        // Must adjust\n        if (Contrast.ratioOfTones(49, bgTone) >= desiredRatio) {\n          answer = 49;\n        } else {\n          answer = 60;\n        }\n      }\n\n      if (this.secondBackground) {\n        // Case 3: Adjust for dual backgrounds.\n\n        const [bg1, bg2] = [this.background, this.secondBackground];\n        const [bgTone1, bgTone2] =\n            [bg1(scheme).getTone(scheme), bg2(scheme).getTone(scheme)];\n        const [upper, lower] =\n            [Math.max(bgTone1, bgTone2), Math.min(bgTone1, bgTone2)];\n\n        if (Contrast.ratioOfTones(upper, answer) >= desiredRatio &&\n            Contrast.ratioOfTones(lower, answer) >= desiredRatio) {\n          return answer;\n        }\n\n        // The darkest light tone that satisfies the desired ratio,\n        // or -1 if such ratio cannot be reached.\n        const lightOption = Contrast.lighter(upper, desiredRatio);\n\n        // The lightest dark tone that satisfies the desired ratio,\n        // or -1 if such ratio cannot be reached.\n        const darkOption = Contrast.darker(lower, desiredRatio);\n\n        // Tones suitable for the foreground.\n        const availables = [];\n        if (lightOption !== -1) availables.push(lightOption);\n        if (darkOption !== -1) availables.push(darkOption);\n\n        const prefersLight = DynamicColor.tonePrefersLightForeground(bgTone1) ||\n            DynamicColor.tonePrefersLightForeground(bgTone2);\n        if (prefersLight) {\n          return (lightOption < 0) ? 100 : lightOption;\n        }\n        if (availables.length === 1) {\n          return availables[0];\n        }\n        return (darkOption < 0) ? 0 : darkOption;\n      }\n\n      return answer;\n    }\n  }\n\n  /**\n   * Given a background tone, find a foreground tone, while ensuring they reach\n   * a contrast ratio that is as close to [ratio] as possible.\n   *\n   * @param bgTone Tone in HCT. Range is 0 to 100, undefined behavior when it\n   *     falls outside that range.\n   * @param ratio The contrast ratio desired between bgTone and the return\n   *     value.\n   */\n  static foregroundTone(bgTone: number, ratio: number): number {\n    const lighterTone = Contrast.lighterUnsafe(bgTone, ratio);\n    const darkerTone = Contrast.darkerUnsafe(bgTone, ratio);\n    const lighterRatio = Contrast.ratioOfTones(lighterTone, bgTone);\n    const darkerRatio = Contrast.ratioOfTones(darkerTone, bgTone);\n    const preferLighter = DynamicColor.tonePrefersLightForeground(bgTone);\n\n    if (preferLighter) {\n      // This handles an edge case where the initial contrast ratio is high\n      // (ex. 13.0), and the ratio passed to the function is that high\n      // ratio, and both the lighter and darker ratio fails to pass that\n      // ratio.\n      //\n      // This was observed with Tonal Spot's On Primary Container turning\n      // black momentarily between high and max contrast in light mode. PC's\n      // standard tone was T90, OPC's was T10, it was light mode, and the\n      // contrast value was 0.6568521221032331.\n      const negligibleDifference = Math.abs(lighterRatio - darkerRatio) < 0.1 &&\n          lighterRatio < ratio && darkerRatio < ratio;\n      return lighterRatio >= ratio || lighterRatio >= darkerRatio ||\n              negligibleDifference ?\n          lighterTone :\n          darkerTone;\n    } else {\n      return darkerRatio >= ratio || darkerRatio >= lighterRatio ? darkerTone :\n                                                                   lighterTone;\n    }\n  }\n\n  /**\n   * Returns whether [tone] prefers a light foreground.\n   *\n   * People prefer white foregrounds on ~T60-70. Observed over time, and also\n   * by Andrew Somers during research for APCA.\n   *\n   * T60 used as to create the smallest discontinuity possible when skipping\n   * down to T49 in order to ensure light foregrounds.\n   * Since `tertiaryContainer` in dark monochrome scheme requires a tone of\n   * 60, it should not be adjusted. Therefore, 60 is excluded here.\n   */\n  static tonePrefersLightForeground(tone: number): boolean {\n    return Math.round(tone) < 60.0;\n  }\n\n  /**\n   * Returns whether [tone] can reach a contrast ratio of 4.5 with a lighter\n   * color.\n   */\n  static toneAllowsLightForeground(tone: number): boolean {\n    return Math.round(tone) <= 49.0;\n  }\n\n  /**\n   * Adjust a tone such that white has 4.5 contrast, if the tone is\n   * reasonably close to supporting it.\n   */\n  static enableLightForeground(tone: number): number {\n    if (DynamicColor.tonePrefersLightForeground(tone) &&\n        !DynamicColor.toneAllowsLightForeground(tone)) {\n      return 49.0;\n    }\n    return tone;\n  }\n}\n"]}