{"version": 3, "file": "material_dynamic_colors.js", "sourceRoot": "", "sources": ["material_dynamic_colors.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,eAAe,EAAC,MAAM,gCAAgC,CAAC;AAC/D,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAClC,OAAO,EAAC,iBAAiB,EAAC,MAAM,8BAA8B,CAAC;AAE/D,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAE7C,OAAO,EAAC,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAC,aAAa,EAAC,MAAM,sBAAsB,CAAC;AAEnD,SAAS,UAAU,CAAC,MAAqB;IACvC,OAAO,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ;QACtC,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC;AACzC,CAAC;AAED,SAAS,YAAY,CAAC,MAAqB;IACzC,OAAO,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC,UAAU,CAAC;AAC/C,CAAC;AAED,SAAS,uBAAuB,CAC5B,GAAW,EAAE,MAAc,EAAE,IAAY,EACzC,gBAAyB;IAC3B,IAAI,MAAM,GAAG,IAAI,CAAC;IAElB,IAAI,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAClD,IAAI,eAAe,CAAC,MAAM,GAAG,MAAM,EAAE;QACnC,IAAI,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC;QACxC,OAAO,eAAe,CAAC,MAAM,GAAG,MAAM,EAAE;YACtC,MAAM,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACxC,MAAM,iBAAiB,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACxD,IAAI,UAAU,GAAG,iBAAiB,CAAC,MAAM,EAAE;gBACzC,MAAM;aACP;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,EAAE;gBACrD,MAAM;aACP;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;YACnE,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;YAC/D,IAAI,cAAc,GAAG,YAAY,EAAE;gBACjC,eAAe,GAAG,iBAAiB,CAAC;aACrC;YACD,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;SAC7D;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,0BAA0B,CAAC,MAAqB;IACvD,OAAO,iBAAiB,CAAC,IAAI;IACzB,eAAe,CAAC,SAAS;IACzB,sBAAsB,CAAC,SAAS;IAChC,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5C,aAAa,CAAC,SAAS;IACvB,0BAA0B,CAAC,SAAS,CACvC,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,SAAc,EAAE,MAAqB;IAC1D,MAAM,OAAO,GACT,SAAS,CAAC,mBAAmB,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAC;IACtE,IAAI,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,IAAI,CAAC;QACvD,CAAC,YAAY,CAAC,yBAAyB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACzD,OAAO,YAAY,CAAC,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3D;SAAM;QACL,OAAO,YAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACzD;AACH,CAAC;AAED;;GAEG;AACH,yEAAyE;AACzE,8CAA8C;AAC9C,MAAM,OAAO,qBAAqB;IAEhC,MAAM,CAAC,cAAc,CAAC,CAAgB;QACpC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YACrC,qBAAqB,CAAC,UAAU,CAAC;IACrD,CAAC;;AAJM,4CAAsB,GAAG,IAAI,CAAC;AAM9B,4CAAsB,GAAG,YAAY,CAAC,WAAW,CAAC;IACvD,IAAI,EAAE,2BAA2B;IACjC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI;CAC5C,CAAC,CAAC;AAEI,8CAAwB,GAAG,YAAY,CAAC,WAAW,CAAC;IACzD,IAAI,EAAE,6BAA6B;IACnC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI;CAC9C,CAAC,CAAC;AAEI,6CAAuB,GAAG,YAAY,CAAC,WAAW,CAAC;IACxD,IAAI,EAAE,4BAA4B;IAClC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI;CAC7C,CAAC,CAAC;AAEI,4CAAsB,GAAG,YAAY,CAAC,WAAW,CAAC;IACvD,IAAI,EAAE,2BAA2B;IACjC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI;CAC5C,CAAC,CAAC;AAEI,mDAA6B,GAAG,YAAY,CAAC,WAAW,CAAC;IAC9D,IAAI,EAAE,mCAAmC;IACzC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB;IACvC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI;CACnD,CAAC,CAAC;AAEI,gCAAU,GAAG,YAAY,CAAC,WAAW,CAAC;IAC3C,IAAI,EAAE,YAAY;IAClB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,kCAAY,GAAG,YAAY,CAAC,WAAW,CAAC;IAC7C,IAAI,EAAE,eAAe;IACrB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,UAAU;IACnD,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;CAC/C,CAAC,CAAC;AAEI,6BAAO,GAAG,YAAY,CAAC,WAAW,CAAC;IACxC,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,gCAAU,GAAG,YAAY,CAAC,WAAW,CAAC;IAC3C,IAAI,EAAE,aAAa;IACnB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,mCAAa,GAAG,YAAY,CAAC,WAAW,CAAC;IAC9C,IAAI,EAAE,gBAAgB;IACtB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,4CAAsB,GAAG,YAAY,CAAC,WAAW,CAAC;IACvD,IAAI,EAAE,0BAA0B;IAChC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,yCAAmB,GAAG,YAAY,CAAC,WAAW,CAAC;IACpD,IAAI,EAAE,uBAAuB;IAC7B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,sCAAgB,GAAG,YAAY,CAAC,WAAW,CAAC;IACjD,IAAI,EAAE,mBAAmB;IACzB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,0CAAoB,GAAG,YAAY,CAAC,WAAW,CAAC;IACrD,IAAI,EAAE,wBAAwB;IAC9B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,6CAAuB,GAAG,YAAY,CAAC,WAAW,CAAC;IACxD,IAAI,EAAE,2BAA2B;IACjC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,+BAAS,GAAG,YAAY,CAAC,WAAW,CAAC;IAC1C,IAAI,EAAE,YAAY;IAClB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,oCAAc,GAAG,YAAY,CAAC,WAAW,CAAC;IAC/C,IAAI,EAAE,iBAAiB;IACvB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB;IACvC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,sCAAgB,GAAG,YAAY,CAAC,WAAW,CAAC;IACjD,IAAI,EAAE,oBAAoB;IAC1B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB;IACvC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;CAChD,CAAC,CAAC;AAEI,oCAAc,GAAG,YAAY,CAAC,WAAW,CAAC;IAC/C,IAAI,EAAE,iBAAiB;IACvB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;CAChC,CAAC,CAAC;AAEI,sCAAgB,GAAG,YAAY,CAAC,WAAW,CAAC;IACjD,IAAI,EAAE,oBAAoB;IAC1B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc;IACvD,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,6BAAO,GAAG,YAAY,CAAC,WAAW,CAAC;IACxC,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB;IACvC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;CACjD,CAAC,CAAC;AAEI,oCAAc,GAAG,YAAY,CAAC,WAAW,CAAC;IAC/C,IAAI,EAAE,iBAAiB;IACvB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB;IACvC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC7C,CAAC,CAAC;AAEI,4BAAM,GAAG,YAAY,CAAC,WAAW,CAAC;IACvC,IAAI,EAAE,QAAQ;IACd,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;CACf,CAAC,CAAC;AAEI,2BAAK,GAAG,YAAY,CAAC,WAAW,CAAC;IACtC,IAAI,EAAE,OAAO;IACb,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;CACf,CAAC,CAAC;AAEI,iCAAW,GAAG,YAAY,CAAC,WAAW,CAAC;IAC5C,IAAI,EAAE,cAAc;IACpB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEI,6BAAO,GAAG,YAAY,CAAC,WAAW,CAAC;IACxC,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3B;QACD,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5B,CAAC;IACL,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC/C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,OAAO,EACrE,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC;CACzB,CAAC,CAAC;AAEI,+BAAS,GAAG,YAAY,CAAC,WAAW,CAAC;IAC1C,IAAI,EAAE,YAAY;IAClB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,CAAC;IACL,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,OAAO;IAChD,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,sCAAgB,GAAG,YAAY,CAAC,WAAW,CAAC;IACjD,IAAI,EAAE,mBAAmB;IACzB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;YACjB,OAAO,aAAa,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;SAC3C;QACD,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5B,CAAC;IACL,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,OAAO,EACrE,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC;CACzB,CAAC,CAAC;AAEI,wCAAkB,GAAG,YAAY,CAAC,WAAW,CAAC;IACnD,IAAI,EAAE,sBAAsB;IAC5B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;YACjB,OAAO,YAAY,CAAC,cAAc,CAC9B,qBAAqB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;SAC1D;QACD,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;SAC3B;QACD,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5B,CAAC;IACL,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,gBAAgB;IACzD,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,oCAAc,GAAG,YAAY,CAAC,WAAW,CAAC;IAC/C,IAAI,EAAE,iBAAiB;IACvB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc;IACvD,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;CAChD,CAAC,CAAC;AAEI,+BAAS,GAAG,YAAY,CAAC,WAAW,CAAC;IAC1C,IAAI,EAAE,WAAW;IACjB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC/C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,kBAAkB,EACxC,qBAAqB,CAAC,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC;CAC1D,CAAC,CAAC;AAEI,iCAAW,GAAG,YAAY,CAAC,WAAW,CAAC;IAC5C,IAAI,EAAE,cAAc;IACpB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;SAC5B;aAAM;YACL,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;SAC5B;IACH,CAAC;IACL,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,SAAS;IAClD,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,wCAAkB,GAAG,YAAY,CAAC,WAAW,CAAC;IACnD,IAAI,EAAE,qBAAqB;IAC3B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,MAAM,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACvC,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YAClB,OAAO,WAAW,CAAC;SACpB;QACD,IAAI,MAAM,GAAG,uBAAuB,CAChC,CAAC,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,EAC9D,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7B,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,OAAO,MAAM,CAAC;IAChB,CAAC;IACL,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,kBAAkB,EACxC,qBAAqB,CAAC,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC;CAC1D,CAAC,CAAC;AAEI,0CAAoB,GAAG,YAAY,CAAC,WAAW,CAAC;IACrD,IAAI,EAAE,wBAAwB;IAC9B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YAClB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,OAAO,YAAY,CAAC,cAAc,CAC9B,qBAAqB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC7D,CAAC;IACL,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,kBAAkB;IAC3D,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,8BAAQ,GAAG,YAAY,CAAC,WAAW,CAAC;IACzC,IAAI,EAAE,UAAU;IAChB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5B,CAAC;IACL,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC/C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,QAAQ,EACvE,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC;CACzB,CAAC,CAAC;AAEI,gCAAU,GAAG,YAAY,CAAC,WAAW,CAAC;IAC3C,IAAI,EAAE,aAAa;IACnB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,CAAC;IACL,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,QAAQ;IACjD,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,uCAAiB,GAAG,YAAY,CAAC,WAAW,CAAC;IAClD,IAAI,EAAE,oBAAoB;IAC1B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YAClB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,MAAM,UAAU,GACZ,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACzD,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;IACzD,CAAC;IACL,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,QAAQ,EACvE,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC;CACzB,CAAC,CAAC;AAEI,yCAAmB,GAAG,YAAY,CAAC,WAAW,CAAC;IACpD,IAAI,EAAE,uBAAuB;IAC7B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EACA,CAAC,CAAC,EAAE,EAAE;QACJ,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;SAC3B;QACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YAClB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;QACD,OAAO,YAAY,CAAC,cAAc,CAC9B,qBAAqB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;IACL,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,iBAAiB;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,2BAAK,GAAG,YAAY,CAAC,WAAW,CAAC;IACtC,IAAI,EAAE,OAAO;IACb,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY;IAC9B,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC/C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,cAAc,EAAE,qBAAqB,CAAC,KAAK,EAAE,EAAE,EACrE,QAAQ,EAAE,KAAK,CAAC;CACrB,CAAC,CAAC;AAEI,6BAAO,GAAG,YAAY,CAAC,WAAW,CAAC;IACxC,IAAI,EAAE,UAAU;IAChB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY;IAC9B,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;IAChC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,KAAK;IAC9C,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,oCAAc,GAAG,YAAY,CAAC,WAAW,CAAC;IAC/C,IAAI,EAAE,iBAAiB;IACvB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY;IAC9B,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,cAAc,EAAE,qBAAqB,CAAC,KAAK,EAAE,EAAE,EACrE,QAAQ,EAAE,KAAK,CAAC;CACrB,CAAC,CAAC;AAEI,sCAAgB,GAAG,YAAY,CAAC,WAAW,CAAC;IACjD,IAAI,EAAE,oBAAoB;IAC1B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY;IAC9B,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc;IACvD,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,kCAAY,GAAG,YAAY,CAAC,WAAW,CAAC;IAC7C,IAAI,EAAE,eAAe;IACrB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,YAAY,EAClC,qBAAqB,CAAC,eAAe,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;CAChE,CAAC,CAAC;AAEI,qCAAe,GAAG,YAAY,CAAC,WAAW,CAAC;IAChD,IAAI,EAAE,mBAAmB;IACzB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,YAAY,EAClC,qBAAqB,CAAC,eAAe,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;CAChE,CAAC,CAAC;AAEI,oCAAc,GAAG,YAAY,CAAC,WAAW,CAAC;IAC/C,IAAI,EAAE,kBAAkB;IACxB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;IAC3C,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,eAAe;IACxD,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,YAAY;IAC3D,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,2CAAqB,GAAG,YAAY,CAAC,WAAW,CAAC;IACtD,IAAI,EAAE,0BAA0B;IAChC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;IAChC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,eAAe;IACxD,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,YAAY;IAC3D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;CAChD,CAAC,CAAC;AAEI,oCAAc,GAAG,YAAY,CAAC,WAAW,CAAC;IAC/C,IAAI,EAAE,iBAAiB;IACvB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,cAAc,EACpC,qBAAqB,CAAC,iBAAiB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;CAClE,CAAC,CAAC;AAEI,uCAAiB,GAAG,YAAY,CAAC,WAAW,CAAC;IAClD,IAAI,EAAE,qBAAqB;IAC3B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,cAAc,EACpC,qBAAqB,CAAC,iBAAiB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;CAClE,CAAC,CAAC;AAEI,sCAAgB,GAAG,YAAY,CAAC,WAAW,CAAC;IACjD,IAAI,EAAE,oBAAoB;IAC1B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI;IACjB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,iBAAiB;IAC1D,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc;IAC7D,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,6CAAuB,GAAG,YAAY,CAAC,WAAW,CAAC;IACxD,IAAI,EAAE,4BAA4B;IAClC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAClC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,iBAAiB;IAC1D,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc;IAC7D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;CAChD,CAAC,CAAC;AAEI,mCAAa,GAAG,YAAY,CAAC,WAAW,CAAC;IAC9C,IAAI,EAAE,gBAAgB;IACtB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,aAAa,EACnC,qBAAqB,CAAC,gBAAgB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;CACjE,CAAC,CAAC;AAEI,sCAAgB,GAAG,YAAY,CAAC,WAAW,CAAC;IACjD,IAAI,EAAE,oBAAoB;IAC1B,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,CACnC,qBAAqB,CAAC,aAAa,EACnC,qBAAqB,CAAC,gBAAgB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;CACjE,CAAC,CAAC;AAEI,qCAAe,GAAG,YAAY,CAAC,WAAW,CAAC;IAChD,IAAI,EAAE,mBAAmB;IACzB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;IAC3C,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,gBAAgB;IACzD,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,aAAa;IAC5D,aAAa,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACjD,CAAC,CAAC;AAEI,4CAAsB,GAAG,YAAY,CAAC,WAAW,CAAC;IACvD,IAAI,EAAE,2BAA2B;IACjC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;IACjC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;IAC1C,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,gBAAgB;IACzD,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,aAAa;IAC5D,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;CAChD,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {DislikeAnalyzer} from '../dislike/dislike_analyzer.js';\nimport {Hct} from '../hct/hct.js';\nimport {ViewingConditions} from '../hct/viewing_conditions.js';\nimport {DynamicScheme} from '../scheme/dynamic_scheme.js';\nimport {Variant} from '../scheme/variant.js';\n\nimport {ContrastCurve} from './contrast_curve.js';\nimport {DynamicColor} from './dynamic_color.js';\nimport {ToneDeltaPair} from './tone_delta_pair.js';\n\nfunction isFidelity(scheme: DynamicScheme): boolean {\n  return scheme.variant === Variant.FIDELITY ||\n      scheme.variant === Variant.CONTENT;\n}\n\nfunction isMonochrome(scheme: DynamicScheme): boolean {\n  return scheme.variant === Variant.MONOCHROME;\n}\n\nfunction findDesiredChromaByTone(\n    hue: number, chroma: number, tone: number,\n    byDecreasingTone: boolean): number {\n  let answer = tone;\n\n  let closestToChroma = Hct.from(hue, chroma, tone);\n  if (closestToChroma.chroma < chroma) {\n    let chromaPeak = closestToChroma.chroma;\n    while (closestToChroma.chroma < chroma) {\n      answer += byDecreasingTone ? -1.0 : 1.0;\n      const potentialSolution = Hct.from(hue, chroma, answer);\n      if (chromaPeak > potentialSolution.chroma) {\n        break;\n      }\n      if (Math.abs(potentialSolution.chroma - chroma) < 0.4) {\n        break;\n      }\n\n      const potentialDelta = Math.abs(potentialSolution.chroma - chroma);\n      const currentDelta = Math.abs(closestToChroma.chroma - chroma);\n      if (potentialDelta < currentDelta) {\n        closestToChroma = potentialSolution;\n      }\n      chromaPeak = Math.max(chromaPeak, potentialSolution.chroma);\n    }\n  }\n\n  return answer;\n}\n\nfunction viewingConditionsForAlbers(scheme: DynamicScheme): ViewingConditions {\n  return ViewingConditions.make(\n      /*whitePoint=*/ undefined,\n      /*adaptingLuminance=*/ undefined,\n      /*backgroundLstar=*/ scheme.isDark ? 30 : 80,\n      /*surround=*/ undefined,\n      /*discountingIlluminant=*/ undefined,\n  );\n}\n\nfunction performAlbers(prealbers: Hct, scheme: DynamicScheme): number {\n  const albersd =\n      prealbers.inViewingConditions(viewingConditionsForAlbers(scheme));\n  if (DynamicColor.tonePrefersLightForeground(prealbers.tone) &&\n      !DynamicColor.toneAllowsLightForeground(albersd.tone)) {\n    return DynamicColor.enableLightForeground(prealbers.tone);\n  } else {\n    return DynamicColor.enableLightForeground(albersd.tone);\n  }\n}\n\n/**\n * DynamicColors for the colors in the Material Design system.\n */\n// Material Color Utilities namespaces the various utilities it provides.\n// tslint:disable-next-line:class-as-namespace\nexport class MaterialDynamicColors {\n  static contentAccentToneDelta = 15.0;\n  static highestSurface(s: DynamicScheme): DynamicColor {\n    return s.isDark ? MaterialDynamicColors.surfaceBright :\n                      MaterialDynamicColors.surfaceDim;\n  }\n\n  static primaryPaletteKeyColor = DynamicColor.fromPalette({\n    name: 'primary_palette_key_color',\n    palette: (s) => s.primaryPalette,\n    tone: (s) => s.primaryPalette.keyColor.tone,\n  });\n\n  static secondaryPaletteKeyColor = DynamicColor.fromPalette({\n    name: 'secondary_palette_key_color',\n    palette: (s) => s.secondaryPalette,\n    tone: (s) => s.secondaryPalette.keyColor.tone,\n  });\n\n  static tertiaryPaletteKeyColor = DynamicColor.fromPalette({\n    name: 'tertiary_palette_key_color',\n    palette: (s) => s.tertiaryPalette,\n    tone: (s) => s.tertiaryPalette.keyColor.tone,\n  });\n\n  static neutralPaletteKeyColor = DynamicColor.fromPalette({\n    name: 'neutral_palette_key_color',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.neutralPalette.keyColor.tone,\n  });\n\n  static neutralVariantPaletteKeyColor = DynamicColor.fromPalette({\n    name: 'neutral_variant_palette_key_color',\n    palette: (s) => s.neutralVariantPalette,\n    tone: (s) => s.neutralVariantPalette.keyColor.tone,\n  });\n\n  static background = DynamicColor.fromPalette({\n    name: 'background',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 6 : 98,\n    isBackground: true,\n  });\n\n  static onBackground = DynamicColor.fromPalette({\n    name: 'on_background',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 90 : 10,\n    background: (s) => MaterialDynamicColors.background,\n    contrastCurve: new ContrastCurve(3, 3, 4.5, 7),\n  });\n\n  static surface = DynamicColor.fromPalette({\n    name: 'surface',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 6 : 98,\n    isBackground: true,\n  });\n\n  static surfaceDim = DynamicColor.fromPalette({\n    name: 'surface_dim',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 6 : 87,\n    isBackground: true,\n  });\n\n  static surfaceBright = DynamicColor.fromPalette({\n    name: 'surface_bright',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 24 : 98,\n    isBackground: true,\n  });\n\n  static surfaceContainerLowest = DynamicColor.fromPalette({\n    name: 'surface_container_lowest',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 4 : 100,\n    isBackground: true,\n  });\n\n  static surfaceContainerLow = DynamicColor.fromPalette({\n    name: 'surface_container_low',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 10 : 96,\n    isBackground: true,\n  });\n\n  static surfaceContainer = DynamicColor.fromPalette({\n    name: 'surface_container',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 12 : 94,\n    isBackground: true,\n  });\n\n  static surfaceContainerHigh = DynamicColor.fromPalette({\n    name: 'surface_container_high',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 17 : 92,\n    isBackground: true,\n  });\n\n  static surfaceContainerHighest = DynamicColor.fromPalette({\n    name: 'surface_container_highest',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 22 : 90,\n    isBackground: true,\n  });\n\n  static onSurface = DynamicColor.fromPalette({\n    name: 'on_surface',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 90 : 10,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static surfaceVariant = DynamicColor.fromPalette({\n    name: 'surface_variant',\n    palette: (s) => s.neutralVariantPalette,\n    tone: (s) => s.isDark ? 30 : 90,\n    isBackground: true,\n  });\n\n  static onSurfaceVariant = DynamicColor.fromPalette({\n    name: 'on_surface_variant',\n    palette: (s) => s.neutralVariantPalette,\n    tone: (s) => s.isDark ? 80 : 30,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n  });\n\n  static inverseSurface = DynamicColor.fromPalette({\n    name: 'inverse_surface',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 90 : 20,\n  });\n\n  static inverseOnSurface = DynamicColor.fromPalette({\n    name: 'inverse_on_surface',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => s.isDark ? 20 : 95,\n    background: (s) => MaterialDynamicColors.inverseSurface,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static outline = DynamicColor.fromPalette({\n    name: 'outline',\n    palette: (s) => s.neutralVariantPalette,\n    tone: (s) => s.isDark ? 60 : 50,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1.5, 3, 4.5, 7),\n  });\n\n  static outlineVariant = DynamicColor.fromPalette({\n    name: 'outline_variant',\n    palette: (s) => s.neutralVariantPalette,\n    tone: (s) => s.isDark ? 30 : 80,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n  });\n\n  static shadow = DynamicColor.fromPalette({\n    name: 'shadow',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => 0,\n  });\n\n  static scrim = DynamicColor.fromPalette({\n    name: 'scrim',\n    palette: (s) => s.neutralPalette,\n    tone: (s) => 0,\n  });\n\n  static surfaceTint = DynamicColor.fromPalette({\n    name: 'surface_tint',\n    palette: (s) => s.primaryPalette,\n    tone: (s) => s.isDark ? 80 : 40,\n    isBackground: true,\n  });\n\n  static primary = DynamicColor.fromPalette({\n    name: 'primary',\n    palette: (s) => s.primaryPalette,\n    tone:\n        (s) => {\n          if (isMonochrome(s)) {\n            return s.isDark ? 100 : 0;\n          }\n          return s.isDark ? 80 : 40;\n        },\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.primaryContainer, MaterialDynamicColors.primary,\n        15, 'nearer', false),\n  });\n\n  static onPrimary = DynamicColor.fromPalette({\n    name: 'on_primary',\n    palette: (s) => s.primaryPalette,\n    tone:\n        (s) => {\n          if (isMonochrome(s)) {\n            return s.isDark ? 10 : 90;\n          }\n          return s.isDark ? 20 : 100;\n        },\n    background: (s) => MaterialDynamicColors.primary,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static primaryContainer = DynamicColor.fromPalette({\n    name: 'primary_container',\n    palette: (s) => s.primaryPalette,\n    tone:\n        (s) => {\n          if (isFidelity(s)) {\n            return performAlbers(s.sourceColorHct, s);\n          }\n          if (isMonochrome(s)) {\n            return s.isDark ? 85 : 25;\n          }\n          return s.isDark ? 30 : 90;\n        },\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.primaryContainer, MaterialDynamicColors.primary,\n        15, 'nearer', false),\n  });\n\n  static onPrimaryContainer = DynamicColor.fromPalette({\n    name: 'on_primary_container',\n    palette: (s) => s.primaryPalette,\n    tone:\n        (s) => {\n          if (isFidelity(s)) {\n            return DynamicColor.foregroundTone(\n                MaterialDynamicColors.primaryContainer.tone(s), 4.5);\n          }\n          if (isMonochrome(s)) {\n            return s.isDark ? 0 : 100;\n          }\n          return s.isDark ? 90 : 10;\n        },\n    background: (s) => MaterialDynamicColors.primaryContainer,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static inversePrimary = DynamicColor.fromPalette({\n    name: 'inverse_primary',\n    palette: (s) => s.primaryPalette,\n    tone: (s) => s.isDark ? 40 : 80,\n    background: (s) => MaterialDynamicColors.inverseSurface,\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n  });\n\n  static secondary = DynamicColor.fromPalette({\n    name: 'secondary',\n    palette: (s) => s.secondaryPalette,\n    tone: (s) => s.isDark ? 80 : 40,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.secondaryContainer,\n        MaterialDynamicColors.secondary, 15, 'nearer', false),\n  });\n\n  static onSecondary = DynamicColor.fromPalette({\n    name: 'on_secondary',\n    palette: (s) => s.secondaryPalette,\n    tone:\n        (s) => {\n          if (isMonochrome(s)) {\n            return s.isDark ? 10 : 100;\n          } else {\n            return s.isDark ? 20 : 100;\n          }\n        },\n    background: (s) => MaterialDynamicColors.secondary,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static secondaryContainer = DynamicColor.fromPalette({\n    name: 'secondary_container',\n    palette: (s) => s.secondaryPalette,\n    tone:\n        (s) => {\n          const initialTone = s.isDark ? 30 : 90;\n          if (isMonochrome(s)) {\n            return s.isDark ? 30 : 85;\n          }\n          if (!isFidelity(s)) {\n            return initialTone;\n          }\n          let answer = findDesiredChromaByTone(\n              s.secondaryPalette.hue, s.secondaryPalette.chroma, initialTone,\n              s.isDark ? false : true);\n          answer = performAlbers(s.secondaryPalette.getHct(answer), s);\n          return answer;\n        },\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.secondaryContainer,\n        MaterialDynamicColors.secondary, 15, 'nearer', false),\n  });\n\n  static onSecondaryContainer = DynamicColor.fromPalette({\n    name: 'on_secondary_container',\n    palette: (s) => s.secondaryPalette,\n    tone:\n        (s) => {\n          if (!isFidelity(s)) {\n            return s.isDark ? 90 : 10;\n          }\n          return DynamicColor.foregroundTone(\n              MaterialDynamicColors.secondaryContainer.tone(s), 4.5);\n        },\n    background: (s) => MaterialDynamicColors.secondaryContainer,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static tertiary = DynamicColor.fromPalette({\n    name: 'tertiary',\n    palette: (s) => s.tertiaryPalette,\n    tone:\n        (s) => {\n          if (isMonochrome(s)) {\n            return s.isDark ? 90 : 25;\n          }\n          return s.isDark ? 80 : 40;\n        },\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.tertiaryContainer, MaterialDynamicColors.tertiary,\n        15, 'nearer', false),\n  });\n\n  static onTertiary = DynamicColor.fromPalette({\n    name: 'on_tertiary',\n    palette: (s) => s.tertiaryPalette,\n    tone:\n        (s) => {\n          if (isMonochrome(s)) {\n            return s.isDark ? 10 : 90;\n          }\n          return s.isDark ? 20 : 100;\n        },\n    background: (s) => MaterialDynamicColors.tertiary,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static tertiaryContainer = DynamicColor.fromPalette({\n    name: 'tertiary_container',\n    palette: (s) => s.tertiaryPalette,\n    tone:\n        (s) => {\n          if (isMonochrome(s)) {\n            return s.isDark ? 60 : 49;\n          }\n          if (!isFidelity(s)) {\n            return s.isDark ? 30 : 90;\n          }\n          const albersTone =\n              performAlbers(s.tertiaryPalette.getHct(s.sourceColorHct.tone), s);\n          const proposedHct = s.tertiaryPalette.getHct(albersTone);\n          return DislikeAnalyzer.fixIfDisliked(proposedHct).tone;\n        },\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.tertiaryContainer, MaterialDynamicColors.tertiary,\n        15, 'nearer', false),\n  });\n\n  static onTertiaryContainer = DynamicColor.fromPalette({\n    name: 'on_tertiary_container',\n    palette: (s) => s.tertiaryPalette,\n    tone:\n        (s) => {\n          if (isMonochrome(s)) {\n            return s.isDark ? 0 : 100;\n          }\n          if (!isFidelity(s)) {\n            return s.isDark ? 90 : 10;\n          }\n          return DynamicColor.foregroundTone(\n              MaterialDynamicColors.tertiaryContainer.tone(s), 4.5);\n        },\n    background: (s) => MaterialDynamicColors.tertiaryContainer,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static error = DynamicColor.fromPalette({\n    name: 'error',\n    palette: (s) => s.errorPalette,\n    tone: (s) => s.isDark ? 80 : 40,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.errorContainer, MaterialDynamicColors.error, 15,\n        'nearer', false),\n  });\n\n  static onError = DynamicColor.fromPalette({\n    name: 'on_error',\n    palette: (s) => s.errorPalette,\n    tone: (s) => s.isDark ? 20 : 100,\n    background: (s) => MaterialDynamicColors.error,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static errorContainer = DynamicColor.fromPalette({\n    name: 'error_container',\n    palette: (s) => s.errorPalette,\n    tone: (s) => s.isDark ? 30 : 90,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.errorContainer, MaterialDynamicColors.error, 15,\n        'nearer', false),\n  });\n\n  static onErrorContainer = DynamicColor.fromPalette({\n    name: 'on_error_container',\n    palette: (s) => s.errorPalette,\n    tone: (s) => s.isDark ? 90 : 10,\n    background: (s) => MaterialDynamicColors.errorContainer,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static primaryFixed = DynamicColor.fromPalette({\n    name: 'primary_fixed',\n    palette: (s) => s.primaryPalette,\n    tone: (s) => isMonochrome(s) ? 40.0 : 90.0,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.primaryFixed,\n        MaterialDynamicColors.primaryFixedDim, 10, 'lighter', true),\n  });\n\n  static primaryFixedDim = DynamicColor.fromPalette({\n    name: 'primary_fixed_dim',\n    palette: (s) => s.primaryPalette,\n    tone: (s) => isMonochrome(s) ? 30.0 : 80.0,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.primaryFixed,\n        MaterialDynamicColors.primaryFixedDim, 10, 'lighter', true),\n  });\n\n  static onPrimaryFixed = DynamicColor.fromPalette({\n    name: 'on_primary_fixed',\n    palette: (s) => s.primaryPalette,\n    tone: (s) => isMonochrome(s) ? 100.0 : 10.0,\n    background: (s) => MaterialDynamicColors.primaryFixedDim,\n    secondBackground: (s) => MaterialDynamicColors.primaryFixed,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static onPrimaryFixedVariant = DynamicColor.fromPalette({\n    name: 'on_primary_fixed_variant',\n    palette: (s) => s.primaryPalette,\n    tone: (s) => isMonochrome(s) ? 90.0 : 30.0,\n    background: (s) => MaterialDynamicColors.primaryFixedDim,\n    secondBackground: (s) => MaterialDynamicColors.primaryFixed,\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n  });\n\n  static secondaryFixed = DynamicColor.fromPalette({\n    name: 'secondary_fixed',\n    palette: (s) => s.secondaryPalette,\n    tone: (s) => isMonochrome(s) ? 80.0 : 90.0,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.secondaryFixed,\n        MaterialDynamicColors.secondaryFixedDim, 10, 'lighter', true),\n  });\n\n  static secondaryFixedDim = DynamicColor.fromPalette({\n    name: 'secondary_fixed_dim',\n    palette: (s) => s.secondaryPalette,\n    tone: (s) => isMonochrome(s) ? 70.0 : 80.0,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.secondaryFixed,\n        MaterialDynamicColors.secondaryFixedDim, 10, 'lighter', true),\n  });\n\n  static onSecondaryFixed = DynamicColor.fromPalette({\n    name: 'on_secondary_fixed',\n    palette: (s) => s.secondaryPalette,\n    tone: (s) => 10.0,\n    background: (s) => MaterialDynamicColors.secondaryFixedDim,\n    secondBackground: (s) => MaterialDynamicColors.secondaryFixed,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static onSecondaryFixedVariant = DynamicColor.fromPalette({\n    name: 'on_secondary_fixed_variant',\n    palette: (s) => s.secondaryPalette,\n    tone: (s) => isMonochrome(s) ? 25.0 : 30.0,\n    background: (s) => MaterialDynamicColors.secondaryFixedDim,\n    secondBackground: (s) => MaterialDynamicColors.secondaryFixed,\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n  });\n\n  static tertiaryFixed = DynamicColor.fromPalette({\n    name: 'tertiary_fixed',\n    palette: (s) => s.tertiaryPalette,\n    tone: (s) => isMonochrome(s) ? 40.0 : 90.0,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.tertiaryFixed,\n        MaterialDynamicColors.tertiaryFixedDim, 10, 'lighter', true),\n  });\n\n  static tertiaryFixedDim = DynamicColor.fromPalette({\n    name: 'tertiary_fixed_dim',\n    palette: (s) => s.tertiaryPalette,\n    tone: (s) => isMonochrome(s) ? 30.0 : 80.0,\n    isBackground: true,\n    background: (s) => MaterialDynamicColors.highestSurface(s),\n    contrastCurve: new ContrastCurve(1, 1, 3, 7),\n    toneDeltaPair: (s) => new ToneDeltaPair(\n        MaterialDynamicColors.tertiaryFixed,\n        MaterialDynamicColors.tertiaryFixedDim, 10, 'lighter', true),\n  });\n\n  static onTertiaryFixed = DynamicColor.fromPalette({\n    name: 'on_tertiary_fixed',\n    palette: (s) => s.tertiaryPalette,\n    tone: (s) => isMonochrome(s) ? 100.0 : 10.0,\n    background: (s) => MaterialDynamicColors.tertiaryFixedDim,\n    secondBackground: (s) => MaterialDynamicColors.tertiaryFixed,\n    contrastCurve: new ContrastCurve(4.5, 7, 11, 21),\n  });\n\n  static onTertiaryFixedVariant = DynamicColor.fromPalette({\n    name: 'on_tertiary_fixed_variant',\n    palette: (s) => s.tertiaryPalette,\n    tone: (s) => isMonochrome(s) ? 90.0 : 30.0,\n    background: (s) => MaterialDynamicColors.tertiaryFixedDim,\n    secondBackground: (s) => MaterialDynamicColors.tertiaryFixed,\n    contrastCurve: new ContrastCurve(3, 4.5, 7, 11),\n  });\n}\n"]}