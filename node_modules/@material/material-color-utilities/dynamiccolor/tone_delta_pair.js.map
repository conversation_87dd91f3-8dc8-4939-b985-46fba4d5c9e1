{"version": 3, "file": "tone_delta_pair.js", "sourceRoot": "", "sources": ["tone_delta_pair.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AASH;;;;;;;GAOG;AACH,MAAM,OAAO,aAAa;IACxB;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,YACa,KAAmB,EACnB,KAAmB,EACnB,KAAa,EACb,QAAsB,EACtB,YAAqB;QAJrB,UAAK,GAAL,KAAK,CAAc;QACnB,UAAK,GAAL,KAAK,CAAc;QACnB,UAAK,GAAL,KAAK,CAAQ;QACb,aAAQ,GAAR,QAAQ,CAAc;QACtB,iBAAY,GAAZ,YAAY,CAAS;IAC/B,CAAC;CACL", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {DynamicColor} from './dynamic_color.js';\n\n/**\n * Describes the different in tone between colors.\n */\nexport type TonePolarity = 'darker'|'lighter'|'nearer'|'farther';\n\n/**\n * Documents a constraint between two DynamicColors, in which their tones must\n * have a certain distance from each other.\n *\n * Prefer a DynamicColor with a background, this is for special cases when\n * designers want tonal distance, literally contrast, between two colors that\n * don't have a background / foreground relationship or a contrast guarantee.\n */\nexport class ToneDeltaPair {\n  /**\n   * Documents a constraint in tone distance between two DynamicColors.\n   *\n   * The polarity is an adjective that describes \"A\", compared to \"B\".\n   *\n   * For instance, ToneDeltaPair(A, B, 15, 'darker', stayTogether) states that\n   * A's tone should be at least 15 darker than B's.\n   *\n   * 'nearer' and 'farther' describes closeness to the surface roles. For\n   * instance, ToneDeltaPair(A, B, 10, 'nearer', stayTogether) states that A\n   * should be 10 lighter than B in light mode, and 10 darker than B in dark\n   * mode.\n   *\n   * @param roleA The first role in a pair.\n   * @param roleB The second role in a pair.\n   * @param delta Required difference between tones. Absolute value, negative\n   * values have undefined behavior.\n   * @param polarity The relative relation between tones of roleA and roleB,\n   * as described above.\n   * @param stayTogether Whether these two roles should stay on the same side of\n   * the \"awkward zone\" (T50-59). This is necessary for certain cases where\n   * one role has two backgrounds.\n   */\n  constructor(\n      readonly roleA: DynamicColor,\n      readonly roleB: DynamicColor,\n      readonly delta: number,\n      readonly polarity: TonePolarity,\n      readonly stayTogether: boolean,\n  ) {}\n}\n"]}