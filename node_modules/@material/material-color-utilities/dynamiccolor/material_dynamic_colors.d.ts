/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { DynamicScheme } from '../scheme/dynamic_scheme.js';
import { DynamicColor } from './dynamic_color.js';
/**
 * DynamicColors for the colors in the Material Design system.
 */
export declare class MaterialDynamicColors {
    static contentAccentToneDelta: number;
    static highestSurface(s: DynamicScheme): DynamicColor;
    static primaryPaletteKeyColor: DynamicColor;
    static secondaryPaletteKeyColor: DynamicColor;
    static tertiaryPaletteKeyColor: DynamicColor;
    static neutralPaletteKeyColor: DynamicColor;
    static neutralVariantPaletteKeyColor: DynamicColor;
    static background: DynamicColor;
    static onBackground: DynamicColor;
    static surface: DynamicColor;
    static surfaceDim: DynamicColor;
    static surfaceBright: DynamicColor;
    static surfaceContainerLowest: DynamicColor;
    static surfaceContainerLow: DynamicColor;
    static surfaceContainer: DynamicColor;
    static surfaceContainerHigh: DynamicColor;
    static surfaceContainerHighest: DynamicColor;
    static onSurface: DynamicColor;
    static surfaceVariant: DynamicColor;
    static onSurfaceVariant: DynamicColor;
    static inverseSurface: DynamicColor;
    static inverseOnSurface: DynamicColor;
    static outline: DynamicColor;
    static outlineVariant: DynamicColor;
    static shadow: DynamicColor;
    static scrim: DynamicColor;
    static surfaceTint: DynamicColor;
    static primary: DynamicColor;
    static onPrimary: DynamicColor;
    static primaryContainer: DynamicColor;
    static onPrimaryContainer: DynamicColor;
    static inversePrimary: DynamicColor;
    static secondary: DynamicColor;
    static onSecondary: DynamicColor;
    static secondaryContainer: DynamicColor;
    static onSecondaryContainer: DynamicColor;
    static tertiary: DynamicColor;
    static onTertiary: DynamicColor;
    static tertiaryContainer: DynamicColor;
    static onTertiaryContainer: DynamicColor;
    static error: DynamicColor;
    static onError: DynamicColor;
    static errorContainer: DynamicColor;
    static onErrorContainer: DynamicColor;
    static primaryFixed: DynamicColor;
    static primaryFixedDim: DynamicColor;
    static onPrimaryFixed: DynamicColor;
    static onPrimaryFixedVariant: DynamicColor;
    static secondaryFixed: DynamicColor;
    static secondaryFixedDim: DynamicColor;
    static onSecondaryFixed: DynamicColor;
    static onSecondaryFixedVariant: DynamicColor;
    static tertiaryFixed: DynamicColor;
    static tertiaryFixedDim: DynamicColor;
    static onTertiaryFixed: DynamicColor;
    static onTertiaryFixedVariant: DynamicColor;
}
