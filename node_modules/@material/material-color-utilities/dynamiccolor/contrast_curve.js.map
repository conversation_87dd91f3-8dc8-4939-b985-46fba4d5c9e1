{"version": 3, "file": "contrast_curve.js", "sourceRoot": "", "sources": ["contrast_curve.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAE/C;;;;;GAKG;AACH,MAAM,OAAO,aAAa;IACxB;;;;;;;OAOG;IACH,YACa,GAAW,EACX,MAAc,EACd,MAAc,EACd,IAAY;QAHZ,QAAG,GAAH,GAAG,CAAQ;QACX,WAAM,GAAN,MAAM,CAAQ;QACd,WAAM,GAAN,MAAM,CAAQ;QACd,SAAI,GAAJ,IAAI,CAAQ;IACtB,CAAC;IAEJ;;;;;;OAMG;IACH,WAAW,CAAC,aAAqB;QAC/B,IAAI,aAAa,IAAI,CAAC,GAAG,EAAE;YACzB,OAAO,IAAI,CAAC,GAAG,CAAC;SACjB;aAAM,IAAI,aAAa,GAAG,GAAG,EAAE;YAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrE;aAAM,IAAI,aAAa,GAAG,GAAG,EAAE;YAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;SACvE;aAAM,IAAI,aAAa,GAAG,GAAG,EAAE;YAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;SACvE;aAAM;YACL,OAAO,IAAI,CAAC,IAAI,CAAC;SAClB;IACH,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as math from '../utils/math_utils.js';\n\n/**\n * A class containing the contrast curve for a dynamic color on its background.\n *\n * The four values correspond to contrast requirements for contrast levels\n * -1.0, 0.0, 0.5, and 1.0, respectively.\n */\nexport class ContrastCurve {\n  /**\n   * Creates a `ContrastCurve` object.\n   *\n   * @param low Contrast requirement for contrast level -1.0\n   * @param normal Contrast requirement for contrast level 0.0\n   * @param medium Contrast requirement for contrast level 0.5\n   * @param high Contrast requirement for contrast level 1.0\n   */\n  constructor(\n      readonly low: number,\n      readonly normal: number,\n      readonly medium: number,\n      readonly high: number,\n  ) {}\n\n  /**\n   * Returns the contrast ratio at a given contrast level.\n   *\n   * @param contrastLevel The contrast level. 0.0 is the default (normal);\n   * -1.0 is the lowest; 1.0 is the highest.\n   * @return The contrast ratio, a number between 1.0 and 21.0.\n   */\n  getContrast(contrastLevel: number): number {\n    if (contrastLevel <= -1.0) {\n      return this.low;\n    } else if (contrastLevel < 0.0) {\n      return math.lerp(this.low, this.normal, (contrastLevel - (-1)) / 1);\n    } else if (contrastLevel < 0.5) {\n      return math.lerp(this.normal, this.medium, (contrastLevel - 0) / 0.5);\n    } else if (contrastLevel < 1.0) {\n      return math.lerp(this.medium, this.high, (contrastLevel - 0.5) / 0.5);\n    } else {\n      return this.high;\n    }\n  }\n}\n"]}