{"version": 3, "file": "dynamic_scheme.js", "sourceRoot": "", "sources": ["dynamic_scheme.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,GAAG,EAAC,MAAM,eAAe,CAAC;AAClC,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAyC/C;;;;;GAKG;AACH,MAAM,OAAO,aAAa;IA6DxB,YAAY,IAA0B;QACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC5C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,aAAa,CAAC,WAAgB,EAAE,IAAc,EAAE,SAAmB;QAExE,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC;QAClC,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,MAAM,gBACtD,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;SACzB;QACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5B,IAAI,OAAO,GAAG,SAAS,IAAI,SAAS,GAAG,OAAO,EAAE;gBAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7D;SACF;QACD,2EAA2E;QAC3E,mCAAmC;QACnC,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\nimport * as math from '../utils/math_utils.js';\n\nimport {Variant} from './variant.js';\n\n/**\n * @param sourceColorArgb The source color of the theme as an ARGB 32-bit\n *     integer.\n * @param variant The variant, or style, of the theme.\n * @param contrastLevel Value from -1 to 1. -1 represents minimum contrast,\n * 0 represents standard (i.e. the design as spec'd), and 1 represents maximum\n * contrast.\n * @param isDark Whether the scheme is in dark mode or light mode.\n * @param primaryPalette Given a tone, produces a color. Hue and chroma of the\n * color are specified in the design specification of the variant. Usually\n * colorful.\n * @param secondaryPalette Given a tone, produces a color. Hue and chroma of\n * the color are specified in the design specification of the variant. Usually\n * less colorful.\n * @param tertiaryPalette Given a tone, produces a color. Hue and chroma of\n * the color are specified in the design specification of the variant. Usually\n * a different hue from primary and colorful.\n * @param neutralPalette Given a tone, produces a color. Hue and chroma of the\n * color are specified in the design specification of the variant. Usually not\n * colorful at all, intended for background & surface colors.\n * @param neutralVariantPalette Given a tone, produces a color. Hue and chroma\n * of the color are specified in the design specification of the variant.\n * Usually not colorful, but slightly more colorful than Neutral. Intended for\n * backgrounds & surfaces.\n */\ninterface DynamicSchemeOptions {\n  sourceColorArgb: number;\n  variant: Variant;\n  contrastLevel: number;\n  isDark: boolean;\n  primaryPalette: TonalPalette;\n  secondaryPalette: TonalPalette;\n  tertiaryPalette: TonalPalette;\n  neutralPalette: TonalPalette;\n  neutralVariantPalette: TonalPalette;\n}\n\n/**\n * Constructed by a set of values representing the current UI state (such as\n * whether or not its dark theme, what the theme style is, etc.), and\n * provides a set of TonalPalettes that can create colors that fit in\n * with the theme style. Used by DynamicColor to resolve into a color.\n */\nexport class DynamicScheme {\n  /**\n   * The source color of the theme as an HCT color.\n   */\n  sourceColorHct: Hct;\n  /**\n   * Given a tone, produces a reddish, colorful, color.\n   */\n  errorPalette: TonalPalette;\n\n  /** The source color of the theme as an ARGB 32-bit integer. */\n  readonly sourceColorArgb: number;\n\n  /** The variant, or style, of the theme. */\n  readonly variant: Variant;\n\n  /**\n   * Value from -1 to 1. -1 represents minimum contrast. 0 represents standard\n   * (i.e. the design as spec'd), and 1 represents maximum contrast.\n   */\n  readonly contrastLevel: number;\n\n  /** Whether the scheme is in dark mode or light mode. */\n  readonly isDark: boolean;\n\n  /**\n   * Given a tone, produces a color. Hue and chroma of the\n   * color are specified in the design specification of the variant. Usually\n   * colorful.\n   */\n  readonly primaryPalette: TonalPalette;\n\n  /**\n   * Given a tone, produces a color. Hue and chroma of\n   * the color are specified in the design specification of the variant. Usually\n   * less colorful.\n   */\n  readonly secondaryPalette: TonalPalette;\n\n  /**\n   * Given a tone, produces a color. Hue and chroma of\n   * the color are specified in the design specification of the variant. Usually\n   * a different hue from primary and colorful.\n   */\n  readonly tertiaryPalette: TonalPalette;\n\n  /**\n   * Given a tone, produces a color. Hue and chroma of the\n   * color are specified in the design specification of the variant. Usually not\n   * colorful at all, intended for background & surface colors.\n   */\n  readonly neutralPalette: TonalPalette;\n\n  /**\n   * Given a tone, produces a color. Hue and chroma\n   * of the color are specified in the design specification of the variant.\n   * Usually not colorful, but slightly more colorful than Neutral. Intended for\n   * backgrounds & surfaces.\n   */\n  readonly neutralVariantPalette: TonalPalette;\n\n  constructor(args: DynamicSchemeOptions) {\n    this.sourceColorArgb = args.sourceColorArgb;\n    this.variant = args.variant;\n    this.contrastLevel = args.contrastLevel;\n    this.isDark = args.isDark;\n    this.sourceColorHct = Hct.fromInt(args.sourceColorArgb);\n    this.primaryPalette = args.primaryPalette;\n    this.secondaryPalette = args.secondaryPalette;\n    this.tertiaryPalette = args.tertiaryPalette;\n    this.neutralPalette = args.neutralPalette;\n    this.neutralVariantPalette = args.neutralVariantPalette;\n    this.errorPalette = TonalPalette.fromHueAndChroma(25.0, 84.0);\n  }\n\n  /**\n   * Support design spec'ing Dynamic Color by schemes that specify hue\n   * rotations that should be applied at certain breakpoints.\n   * @param sourceColor the source color of the theme, in HCT.\n   * @param hues The \"breakpoints\", i.e. the hues at which a rotation should\n   * be apply.\n   * @param rotations The rotation that should be applied when source color's\n   * hue is >= the same index in hues array, and <= the hue at the next index\n   * in hues array.\n   */\n  static getRotatedHue(sourceColor: Hct, hues: number[], rotations: number[]):\n      number {\n    const sourceHue = sourceColor.hue;\n    if (hues.length !== rotations.length) {\n      throw new Error(`mismatch between hue length ${hues.length} & rotations ${\n          rotations.length}`);\n    }\n    if (rotations.length === 1) {\n      return math.sanitizeDegreesDouble(sourceColor.hue + rotations[0]);\n    }\n    const size = hues.length;\n    for (let i = 0; i <= size - 2; i++) {\n      const thisHue = hues[i];\n      const nextHue = hues[i + 1];\n      if (thisHue < sourceHue && sourceHue < nextHue) {\n        return math.sanitizeDegreesDouble(sourceHue + rotations[i]);\n      }\n    }\n    // If this statement executes, something is wrong, there should have been a\n    // rotation found using the arrays.\n    return sourceHue;\n  }\n}"]}