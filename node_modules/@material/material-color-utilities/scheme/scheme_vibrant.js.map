{"version": 3, "file": "scheme_vibrant.js", "sourceRoot": "", "sources": ["scheme_vibrant.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAGH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAE1D,OAAO,EAAC,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAErC;;;GAGG;AACH,MAAM,OAAO,aAAc,SAAQ,aAAa;IAiD9C,YAAY,cAAmB,EAAE,MAAe,EAAE,aAAqB;QACrE,KAAK,CAAC;YACJ,eAAe,EAAE,cAAc,CAAC,KAAK,EAAE;YACvC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,aAAa;YACb,MAAM;YACN,cAAc,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC;YACxE,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,CAC3C,aAAa,CAAC,aAAa,CACvB,cAAc,EAAE,aAAa,CAAC,IAAI,EAClC,aAAa,CAAC,kBAAkB,CAAC,EACrC,IAAI,CAAC;YACT,eAAe,EAAE,YAAY,CAAC,gBAAgB,CAC1C,aAAa,CAAC,aAAa,CACvB,cAAc,EAAE,aAAa,CAAC,IAAI,EAClC,aAAa,CAAC,iBAAiB,CAAC,EACpC,IAAI,CAAC;YACT,cAAc,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;YACvE,qBAAqB,EACjB,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;SAC5D,CAAC,CAAC;IACL,CAAC;;AArED;;;GAGG;AACqB,kBAAI,GAAG;IAC7B,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;CACN,CAAC;AAEF;;;GAGG;AACqB,gCAAkB,GAAG;IAC3C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACL,CAAC;AAEF;;;GAGG;AACqB,+BAAiB,GAAG;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACL,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\n\nimport {DynamicScheme} from './dynamic_scheme.js';\nimport {Variant} from './variant.js';\n\n/**\n * A Dynamic Color theme that maxes out colorfulness at each position in the\n * Primary Tonal Palette.\n */\nexport class SchemeVibrant extends DynamicScheme {\n  /**\n   * Hues (in degrees) used at breakpoints such that designers can specify a\n   * hue rotation that occurs at a given break point.\n   */\n  private static readonly hues = [\n    0.0,\n    41.0,\n    61.0,\n    101.0,\n    131.0,\n    181.0,\n    251.0,\n    301.0,\n    360.0,\n  ];\n\n  /**\n   * Hue rotations (in degrees) of the Secondary [TonalPalette],\n   * corresponding to the breakpoints in [hues].\n   */\n  private static readonly secondaryRotations = [\n    18.0,\n    15.0,\n    10.0,\n    12.0,\n    15.0,\n    18.0,\n    15.0,\n    12.0,\n    12.0,\n  ];\n\n  /**\n   * Hue rotations (in degrees) of the Tertiary [TonalPalette],\n   * corresponding to the breakpoints in [hues].\n   */\n  private static readonly tertiaryRotations = [\n    35.0,\n    30.0,\n    20.0,\n    25.0,\n    30.0,\n    35.0,\n    30.0,\n    25.0,\n    25.0,\n  ];\n\n  constructor(sourceColorHct: Hct, isDark: boolean, contrastLevel: number) {\n    super({\n      sourceColorArgb: sourceColorHct.toInt(),\n      variant: Variant.VIBRANT,\n      contrastLevel,\n      isDark,\n      primaryPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 200.0),\n      secondaryPalette: TonalPalette.fromHueAndChroma(\n          DynamicScheme.getRotatedHue(\n              sourceColorHct, SchemeVibrant.hues,\n              SchemeVibrant.secondaryRotations),\n          24.0),\n      tertiaryPalette: TonalPalette.fromHueAndChroma(\n          DynamicScheme.getRotatedHue(\n              sourceColorHct, SchemeVibrant.hues,\n              SchemeVibrant.tertiaryRotations),\n          32.0),\n      neutralPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 10.0),\n      neutralVariantPalette:\n          TonalPalette.fromHueAndChroma(sourceColorHct.hue, 12.0),\n    });\n  }\n}\n"]}