{"version": 3, "file": "variant.js", "sourceRoot": "", "sources": ["variant.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH;;;;GAIG;AACH,MAAM,CAAN,IAAY,OAUX;AAVD,WAAY,OAAO;IACjB,iDAAU,CAAA;IACV,2CAAO,CAAA;IACP,iDAAU,CAAA;IACV,2CAAO,CAAA;IACP,iDAAU,CAAA;IACV,6CAAQ,CAAA;IACR,2CAAO,CAAA;IACP,2CAAO,CAAA;IACP,mDAAW,CAAA;AACb,CAAC,EAVW,OAAO,KAAP,OAAO,QAUlB", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Set of themes supported by Dynamic Color.\n * Instantiate the corresponding subclass, ex. SchemeTonalSpot, to create\n * colors corresponding to the theme.\n */\nexport enum Variant {\n  MONOCHROME,\n  NEUTRAL,\n  TONAL_SPOT,\n  VIBRANT,\n  EXPRESSIVE,\n  FIDELITY,\n  CONTENT,\n  RAINBOW,\n  FRUIT_SALAD\n}\n"]}