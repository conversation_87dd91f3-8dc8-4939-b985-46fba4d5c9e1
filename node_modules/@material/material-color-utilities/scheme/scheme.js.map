{"version": 3, "file": "scheme.js", "sourceRoot": "", "sources": ["scheme.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,0DAA0D;AAE1D,OAAO,EAAC,WAAW,EAAC,MAAM,6BAA6B,CAAC;AAExD;;GAEG;AACH,MAAM,OAAO,MAAM;IACjB,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;IACzC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;IACtC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,IAAY;QACvB,OAAO,MAAM,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,IAAI,CAAC,IAAY;QACtB,OAAO,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,YAAY,CAAC,IAAY;QAC9B,OAAO,MAAM,CAAC,oBAAoB,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,WAAW,CAAC,IAAY;QAC7B,OAAO,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,IAAiB;QAC3C,OAAO,IAAI,MAAM,CAAC;YAChB,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YAC5B,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YAC9B,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YAC7B,iBAAiB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;YAC7B,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YACvB,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YACtB,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;SACjC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,IAAiB;QAC1C,OAAO,IAAI,MAAM,CAAC;YAChB,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,iBAAiB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YACvB,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YACtB,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;SACjC,CAAC,CAAC;IACL,CAAC;IAED,YAAqC,KA8BpC;QA9BoC,UAAK,GAAL,KAAK,CA8BzC;IAAG,CAAC;IAEL,MAAM;QACJ,OAAO;YACL,GAAG,IAAI,CAAC,KAAK;SACd,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This file is automatically generated. Do not modify it.\n\nimport {CorePalette} from '../palettes/core_palette.js';\n\n/**\n * Represents a Material color scheme, a mapping of color roles to colors.\n */\nexport class Scheme {\n  get primary(): number {\n    return this.props.primary;\n  }\n\n  get onPrimary(): number {\n    return this.props.onPrimary;\n  }\n\n  get primaryContainer(): number {\n    return this.props.primaryContainer;\n  }\n\n  get onPrimaryContainer(): number {\n    return this.props.onPrimaryContainer;\n  }\n\n  get secondary(): number {\n    return this.props.secondary;\n  }\n\n  get onSecondary(): number {\n    return this.props.onSecondary;\n  }\n\n  get secondaryContainer(): number {\n    return this.props.secondaryContainer;\n  }\n\n  get onSecondaryContainer(): number {\n    return this.props.onSecondaryContainer;\n  }\n\n  get tertiary(): number {\n    return this.props.tertiary;\n  }\n\n  get onTertiary(): number {\n    return this.props.onTertiary;\n  }\n\n  get tertiaryContainer(): number {\n    return this.props.tertiaryContainer;\n  }\n\n  get onTertiaryContainer(): number {\n    return this.props.onTertiaryContainer;\n  }\n\n  get error(): number {\n    return this.props.error;\n  }\n\n  get onError(): number {\n    return this.props.onError;\n  }\n\n  get errorContainer(): number {\n    return this.props.errorContainer;\n  }\n\n  get onErrorContainer(): number {\n    return this.props.onErrorContainer;\n  }\n\n  get background(): number {\n    return this.props.background;\n  }\n\n  get onBackground(): number {\n    return this.props.onBackground;\n  }\n\n  get surface(): number {\n    return this.props.surface;\n  }\n\n  get onSurface(): number {\n    return this.props.onSurface;\n  }\n\n  get surfaceVariant(): number {\n    return this.props.surfaceVariant;\n  }\n\n  get onSurfaceVariant(): number {\n    return this.props.onSurfaceVariant;\n  }\n\n  get outline(): number {\n    return this.props.outline;\n  }\n\n  get outlineVariant(): number {\n    return this.props.outlineVariant;\n  }\n\n  get shadow(): number {\n    return this.props.shadow;\n  }\n\n  get scrim(): number {\n    return this.props.scrim;\n  }\n\n  get inverseSurface(): number {\n    return this.props.inverseSurface;\n  }\n\n  get inverseOnSurface(): number {\n    return this.props.inverseOnSurface;\n  }\n\n  get inversePrimary(): number {\n    return this.props.inversePrimary;\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Light Material color scheme, based on the color's hue.\n   */\n  static light(argb: number): Scheme {\n    return Scheme.lightFromCorePalette(CorePalette.of(argb));\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Dark Material color scheme, based on the color's hue.\n   */\n  static dark(argb: number): Scheme {\n    return Scheme.darkFromCorePalette(CorePalette.of(argb));\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Light Material content color scheme, based on the color's hue.\n   */\n  static lightContent(argb: number): Scheme {\n    return Scheme.lightFromCorePalette(CorePalette.contentOf(argb));\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Dark Material content color scheme, based on the color's hue.\n   */\n  static darkContent(argb: number): Scheme {\n    return Scheme.darkFromCorePalette(CorePalette.contentOf(argb));\n  }\n\n  /**\n   * Light scheme from core palette\n   */\n  static lightFromCorePalette(core: CorePalette): Scheme {\n    return new Scheme({\n      primary: core.a1.tone(40),\n      onPrimary: core.a1.tone(100),\n      primaryContainer: core.a1.tone(90),\n      onPrimaryContainer: core.a1.tone(10),\n      secondary: core.a2.tone(40),\n      onSecondary: core.a2.tone(100),\n      secondaryContainer: core.a2.tone(90),\n      onSecondaryContainer: core.a2.tone(10),\n      tertiary: core.a3.tone(40),\n      onTertiary: core.a3.tone(100),\n      tertiaryContainer: core.a3.tone(90),\n      onTertiaryContainer: core.a3.tone(10),\n      error: core.error.tone(40),\n      onError: core.error.tone(100),\n      errorContainer: core.error.tone(90),\n      onErrorContainer: core.error.tone(10),\n      background: core.n1.tone(99),\n      onBackground: core.n1.tone(10),\n      surface: core.n1.tone(99),\n      onSurface: core.n1.tone(10),\n      surfaceVariant: core.n2.tone(90),\n      onSurfaceVariant: core.n2.tone(30),\n      outline: core.n2.tone(50),\n      outlineVariant: core.n2.tone(80),\n      shadow: core.n1.tone(0),\n      scrim: core.n1.tone(0),\n      inverseSurface: core.n1.tone(20),\n      inverseOnSurface: core.n1.tone(95),\n      inversePrimary: core.a1.tone(80)\n    });\n  }\n\n  /**\n   * Dark scheme from core palette\n   */\n  static darkFromCorePalette(core: CorePalette): Scheme {\n    return new Scheme({\n      primary: core.a1.tone(80),\n      onPrimary: core.a1.tone(20),\n      primaryContainer: core.a1.tone(30),\n      onPrimaryContainer: core.a1.tone(90),\n      secondary: core.a2.tone(80),\n      onSecondary: core.a2.tone(20),\n      secondaryContainer: core.a2.tone(30),\n      onSecondaryContainer: core.a2.tone(90),\n      tertiary: core.a3.tone(80),\n      onTertiary: core.a3.tone(20),\n      tertiaryContainer: core.a3.tone(30),\n      onTertiaryContainer: core.a3.tone(90),\n      error: core.error.tone(80),\n      onError: core.error.tone(20),\n      errorContainer: core.error.tone(30),\n      onErrorContainer: core.error.tone(80),\n      background: core.n1.tone(10),\n      onBackground: core.n1.tone(90),\n      surface: core.n1.tone(10),\n      onSurface: core.n1.tone(90),\n      surfaceVariant: core.n2.tone(30),\n      onSurfaceVariant: core.n2.tone(80),\n      outline: core.n2.tone(60),\n      outlineVariant: core.n2.tone(30),\n      shadow: core.n1.tone(0),\n      scrim: core.n1.tone(0),\n      inverseSurface: core.n1.tone(90),\n      inverseOnSurface: core.n1.tone(20),\n      inversePrimary: core.a1.tone(40)\n    });\n  }\n\n  private constructor(private readonly props: {\n    primary: number,\n    onPrimary: number,\n    primaryContainer: number,\n    onPrimaryContainer: number,\n    secondary: number,\n    onSecondary: number,\n    secondaryContainer: number,\n    onSecondaryContainer: number,\n    tertiary: number,\n    onTertiary: number,\n    tertiaryContainer: number,\n    onTertiaryContainer: number,\n    error: number,\n    onError: number,\n    errorContainer: number,\n    onErrorContainer: number,\n    background: number,\n    onBackground: number,\n    surface: number,\n    onSurface: number,\n    surfaceVariant: number,\n    onSurfaceVariant: number,\n    outline: number,\n    outlineVariant: number,\n    shadow: number,\n    scrim: number,\n    inverseSurface: number,\n    inverseOnSurface: number,\n    inversePrimary: number\n  }) {}\n\n  toJSON() {\n    return {\n      ...this.props\n    };\n  }\n}\n"]}