{"version": 3, "file": "scheme_fruit_salad.js", "sourceRoot": "", "sources": ["scheme_fruit_salad.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAGH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAE/C,OAAO,EAAC,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAErC;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,aAAa;IACjD,YAAY,cAAmB,EAAE,MAAe,EAAE,aAAqB;QACrE,KAAK,CAAC;YACJ,eAAe,EAAE,cAAc,CAAC,KAAK,EAAE;YACvC,OAAO,EAAE,OAAO,CAAC,WAAW;YAC5B,aAAa;YACb,MAAM;YACN,cAAc,EAAE,YAAY,CAAC,gBAAgB,CACzC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC;YAChE,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,CAC3C,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC;YAChE,eAAe,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;YACxE,cAAc,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;YACvE,qBAAqB,EACjB,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;SAC5D,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\nimport * as math from '../utils/math_utils.js';\n\nimport {DynamicScheme} from './dynamic_scheme.js';\nimport {Variant} from './variant.js';\n\n/**\n * A playful theme - the source color's hue does not appear in the theme.\n */\nexport class SchemeFruitSalad extends DynamicScheme {\n  constructor(sourceColorHct: Hct, isDark: boolean, contrastLevel: number) {\n    super({\n      sourceColorArgb: sourceColorHct.toInt(),\n      variant: Variant.FRUIT_SALAD,\n      contrastLevel,\n      isDark,\n      primaryPalette: TonalPalette.fromHueAndChroma(\n          math.sanitizeDegreesDouble(sourceColorHct.hue - 50.0), 48.0),\n      secondaryPalette: TonalPalette.fromHueAndChroma(\n          math.sanitizeDegreesDouble(sourceColorHct.hue - 50.0), 36.0),\n      tertiaryPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 36.0),\n      neutralPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 10.0),\n      neutralVariantPalette:\n          TonalPalette.fromHueAndChroma(sourceColorHct.hue, 16.0),\n    });\n  }\n}\n"]}