{"version": 3, "file": "scheme_fidelity.js", "sourceRoot": "", "sources": ["scheme_fidelity.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,eAAe,EAAC,MAAM,gCAAgC,CAAC;AAE/D,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAC,gBAAgB,EAAC,MAAM,qCAAqC,CAAC;AAErE,OAAO,EAAC,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAErC;;;;;;;;GAQG;AACH,MAAM,OAAO,cAAe,SAAQ,aAAa;IAC/C,YAAY,cAAmB,EAAE,MAAe,EAAE,aAAqB;QACrE,KAAK,CAAC;YACJ,eAAe,EAAE,cAAc,CAAC,KAAK,EAAE;YACvC,OAAO,EAAE,OAAO,CAAC,QAAQ;YACzB,aAAa;YACb,MAAM;YACN,cAAc,EAAE,YAAY,CAAC,gBAAgB,CACzC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,MAAM,CAAC;YAC9C,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,CAC3C,cAAc,CAAC,GAAG,EAClB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,EAAE,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;YACxE,eAAe,EAAE,YAAY,CAAC,OAAO,CACjC,eAAe;iBACV,aAAa,CAAC,IAAI,gBAAgB,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC;iBAC9D,KAAK,EAAE,CAAC;YACjB,cAAc,EAAE,YAAY,CAAC,gBAAgB,CACzC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC;YACpD,qBAAqB,EAAE,YAAY,CAAC,gBAAgB,CAChD,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;SAC3D,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {DislikeAnalyzer} from '../dislike/dislike_analyzer.js';\nimport {Hct} from '../hct/hct.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\nimport {TemperatureCache} from '../temperature/temperature_cache.js';\n\nimport {DynamicScheme} from './dynamic_scheme.js';\nimport {Variant} from './variant.js';\n\n/**\n * A scheme that places the source color in `Scheme.primaryContainer`.\n *\n * Primary Container is the source color, adjusted for color relativity.\n * It maintains constant appearance in light mode and dark mode.\n * This adds ~5 tone in light mode, and subtracts ~5 tone in dark mode.\n * Tertiary Container is the complement to the source color, using\n * `TemperatureCache`. It also maintains constant appearance.\n */\nexport class SchemeFidelity extends DynamicScheme {\n  constructor(sourceColorHct: Hct, isDark: boolean, contrastLevel: number) {\n    super({\n      sourceColorArgb: sourceColorHct.toInt(),\n      variant: Variant.FIDELITY,\n      contrastLevel,\n      isDark,\n      primaryPalette: TonalPalette.fromHueAndChroma(\n          sourceColorHct.hue, sourceColorHct.chroma),\n      secondaryPalette: TonalPalette.fromHueAndChroma(\n          sourceColorHct.hue,\n          Math.max(sourceColorHct.chroma - 32.0, sourceColorHct.chroma * 0.5)),\n      tertiaryPalette: TonalPalette.fromInt(\n          DislikeAnalyzer\n              .fixIfDisliked(new TemperatureCache(sourceColorHct).complement)\n              .toInt()),\n      neutralPalette: TonalPalette.fromHueAndChroma(\n          sourceColorHct.hue, sourceColorHct.chroma / 8.0),\n      neutralVariantPalette: TonalPalette.fromHueAndChroma(\n          sourceColorHct.hue, sourceColorHct.chroma / 8.0 + 4.0),\n    });\n  }\n}\n"]}