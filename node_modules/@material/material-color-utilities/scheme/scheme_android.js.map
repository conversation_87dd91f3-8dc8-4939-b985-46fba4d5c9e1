{"version": 3, "file": "scheme_android.js", "sourceRoot": "", "sources": ["scheme_android.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,WAAW,EAAC,MAAM,6BAA6B,CAAC;AAExD;;GAEG;AACH,MAAM,OAAO,aAAa;IACxB,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC;IAC9C,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;IACzC,CAAC;IAED,IAAI,2BAA2B;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC;IAChD,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,0BAA0B;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC;IAC/C,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;IACtC,CAAC;IAED,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;IAC5C,CAAC;IAED,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC;IAC9C,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC;IAC7C,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;IAC5C,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IAC1C,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IAC1C,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,IAAY;QACvB,MAAM,IAAI,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,IAAI,CAAC,IAAY;QACtB,MAAM,IAAI,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,YAAY,CAAC,IAAY;QAC9B,MAAM,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,WAAW,CAAC,IAAY;QAC7B,MAAM,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,IAAiB;QAC3C,OAAO,IAAI,aAAa,CAAC;YACvB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,yBAAyB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,2BAA2B,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,0BAA0B,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5C,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,iBAAiB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,yBAAyB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,wBAAwB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,qBAAqB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACxC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YAC7B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,qBAAqB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,IAAiB;QAC1C,OAAO,IAAI,aAAa,CAAC;YACvB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,yBAAyB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,2BAA2B,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,0BAA0B,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5C,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,iBAAiB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,yBAAyB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,wBAAwB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,qBAAqB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YAC7B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,qBAAqB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;SACxB,CAAC,CAAC;IACL,CAAC;IAED,YAAqC,KA0BpC;QA1BoC,UAAK,GAAL,KAAK,CA0BzC;IAAG,CAAC;IAEL,MAAM;QACJ,OAAO,EAAC,GAAG,IAAI,CAAC,KAAK,EAAC,CAAC;IACzB,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {CorePalette} from '../palettes/core_palette.js';\n\n/**\n * Represents an Android 12 color scheme, a mapping of color roles to colors.\n */\nexport class SchemeAndroid {\n  get colorAccentPrimary(): number {\n    return this.props.colorAccentPrimary;\n  }\n\n  get colorAccentPrimaryVariant(): number {\n    return this.props.colorAccentPrimaryVariant;\n  }\n\n  get colorAccentSecondary(): number {\n    return this.props.colorAccentSecondary;\n  }\n\n  get colorAccentSecondaryVariant(): number {\n    return this.props.colorAccentSecondaryVariant;\n  }\n\n  get colorAccentTertiary(): number {\n    return this.props.colorAccentTertiary;\n  }\n\n  get colorAccentTertiaryVariant(): number {\n    return this.props.colorAccentTertiaryVariant;\n  }\n\n  get textColorPrimary(): number {\n    return this.props.textColorPrimary;\n  }\n\n  get textColorSecondary(): number {\n    return this.props.textColorSecondary;\n  }\n\n  get textColorTertiary(): number {\n    return this.props.textColorTertiary;\n  }\n\n  get textColorPrimaryInverse(): number {\n    return this.props.textColorPrimaryInverse;\n  }\n\n  get textColorSecondaryInverse(): number {\n    return this.props.textColorSecondaryInverse;\n  }\n\n  get textColorTertiaryInverse(): number {\n    return this.props.textColorTertiaryInverse;\n  }\n\n  get colorBackground(): number {\n    return this.props.colorBackground;\n  }\n\n  get colorBackgroundFloating(): number {\n    return this.props.colorBackgroundFloating;\n  }\n\n  get colorSurface(): number {\n    return this.props.colorSurface;\n  }\n\n  get colorSurfaceVariant(): number {\n    return this.props.colorSurfaceVariant;\n  }\n\n  get colorSurfaceHighlight(): number {\n    return this.props.colorSurfaceHighlight;\n  }\n\n  get surfaceHeader(): number {\n    return this.props.surfaceHeader;\n  }\n\n  get underSurface(): number {\n    return this.props.underSurface;\n  }\n\n  get offState(): number {\n    return this.props.offState;\n  }\n\n  get accentSurface(): number {\n    return this.props.accentSurface;\n  }\n\n  get textPrimaryOnAccent(): number {\n    return this.props.textPrimaryOnAccent;\n  }\n\n  get textSecondaryOnAccent(): number {\n    return this.props.textSecondaryOnAccent;\n  }\n\n  get volumeBackground(): number {\n    return this.props.volumeBackground;\n  }\n\n  get scrim(): number {\n    return this.props.scrim;\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Light Material color scheme, based on the color's hue.\n   */\n  static light(argb: number): SchemeAndroid {\n    const core = CorePalette.of(argb);\n    return SchemeAndroid.lightFromCorePalette(core);\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Dark Material color scheme, based on the color's hue.\n   */\n  static dark(argb: number): SchemeAndroid {\n    const core = CorePalette.of(argb);\n    return SchemeAndroid.darkFromCorePalette(core);\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Light Android color scheme, based on the color's hue.\n   */\n  static lightContent(argb: number): SchemeAndroid {\n    const core = CorePalette.contentOf(argb);\n    return SchemeAndroid.lightFromCorePalette(core);\n  }\n\n  /**\n   * @param argb ARGB representation of a color.\n   * @return Dark Android color scheme, based on the color's hue.\n   */\n  static darkContent(argb: number): SchemeAndroid {\n    const core = CorePalette.contentOf(argb);\n    return SchemeAndroid.darkFromCorePalette(core);\n  }\n\n  /**\n   * Light scheme from core palette\n   */\n  static lightFromCorePalette(core: CorePalette): SchemeAndroid {\n    return new SchemeAndroid({\n      colorAccentPrimary: core.a1.tone(90),\n      colorAccentPrimaryVariant: core.a1.tone(40),\n      colorAccentSecondary: core.a2.tone(90),\n      colorAccentSecondaryVariant: core.a2.tone(40),\n      colorAccentTertiary: core.a3.tone(90),\n      colorAccentTertiaryVariant: core.a3.tone(40),\n      textColorPrimary: core.n1.tone(10),\n      textColorSecondary: core.n2.tone(30),\n      textColorTertiary: core.n2.tone(50),\n      textColorPrimaryInverse: core.n1.tone(95),\n      textColorSecondaryInverse: core.n1.tone(80),\n      textColorTertiaryInverse: core.n1.tone(60),\n      colorBackground: core.n1.tone(95),\n      colorBackgroundFloating: core.n1.tone(98),\n      colorSurface: core.n1.tone(98),\n      colorSurfaceVariant: core.n1.tone(90),\n      colorSurfaceHighlight: core.n1.tone(100),\n      surfaceHeader: core.n1.tone(90),\n      underSurface: core.n1.tone(0),\n      offState: core.n1.tone(20),\n      accentSurface: core.a2.tone(95),\n      textPrimaryOnAccent: core.n1.tone(10),\n      textSecondaryOnAccent: core.n2.tone(30),\n      volumeBackground: core.n1.tone(25),\n      scrim: core.n1.tone(80),\n    });\n  }\n\n  /**\n   * Dark scheme from core palette\n   */\n  static darkFromCorePalette(core: CorePalette): SchemeAndroid {\n    return new SchemeAndroid({\n      colorAccentPrimary: core.a1.tone(90),\n      colorAccentPrimaryVariant: core.a1.tone(70),\n      colorAccentSecondary: core.a2.tone(90),\n      colorAccentSecondaryVariant: core.a2.tone(70),\n      colorAccentTertiary: core.a3.tone(90),\n      colorAccentTertiaryVariant: core.a3.tone(70),\n      textColorPrimary: core.n1.tone(95),\n      textColorSecondary: core.n2.tone(80),\n      textColorTertiary: core.n2.tone(60),\n      textColorPrimaryInverse: core.n1.tone(10),\n      textColorSecondaryInverse: core.n1.tone(30),\n      textColorTertiaryInverse: core.n1.tone(50),\n      colorBackground: core.n1.tone(10),\n      colorBackgroundFloating: core.n1.tone(10),\n      colorSurface: core.n1.tone(20),\n      colorSurfaceVariant: core.n1.tone(30),\n      colorSurfaceHighlight: core.n1.tone(35),\n      surfaceHeader: core.n1.tone(30),\n      underSurface: core.n1.tone(0),\n      offState: core.n1.tone(20),\n      accentSurface: core.a2.tone(95),\n      textPrimaryOnAccent: core.n1.tone(10),\n      textSecondaryOnAccent: core.n2.tone(30),\n      volumeBackground: core.n1.tone(25),\n      scrim: core.n1.tone(80),\n    });\n  }\n\n  private constructor(private readonly props: {\n    colorAccentPrimary: number,\n    colorAccentPrimaryVariant: number,\n    colorAccentSecondary: number,\n    colorAccentSecondaryVariant: number,\n    colorAccentTertiary: number,\n    colorAccentTertiaryVariant: number,\n    textColorPrimary: number,\n    textColorSecondary: number,\n    textColorTertiary: number,\n    textColorPrimaryInverse: number,\n    textColorSecondaryInverse: number,\n    textColorTertiaryInverse: number,\n    colorBackground: number,\n    colorBackgroundFloating: number,\n    colorSurface: number,\n    colorSurfaceVariant: number,\n    colorSurfaceHighlight: number,\n    surfaceHeader: number,\n    underSurface: number,\n    offState: number,\n    accentSurface: number,\n    textPrimaryOnAccent: number,\n    textSecondaryOnAccent: number,\n    volumeBackground: number,\n    scrim: number\n  }) {}\n\n  toJSON() {\n    return {...this.props};\n  }\n}\n"]}