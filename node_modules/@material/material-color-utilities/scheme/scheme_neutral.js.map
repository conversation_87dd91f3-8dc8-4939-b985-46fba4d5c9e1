{"version": 3, "file": "scheme_neutral.js", "sourceRoot": "", "sources": ["scheme_neutral.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAGH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAE1D,OAAO,EAAC,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAErC,oDAAoD;AACpD,MAAM,OAAO,aAAc,SAAQ,aAAa;IAC9C,YAAY,cAAmB,EAAE,MAAe,EAAE,aAAqB;QACrE,KAAK,CAAC;YACJ,eAAe,EAAE,cAAc,CAAC,KAAK,EAAE;YACvC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,aAAa;YACb,MAAM;YACN,cAAc,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;YACvE,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC;YACxE,eAAe,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;YACxE,cAAc,EAAE,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC;YACtE,qBAAqB,EACjB,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC;SAC3D,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\n\nimport {DynamicScheme} from './dynamic_scheme.js';\nimport {Variant} from './variant.js';\n\n/** A Dynamic Color theme that is near grayscale. */\nexport class SchemeNeutral extends DynamicScheme {\n  constructor(sourceColorHct: Hct, isDark: boolean, contrastLevel: number) {\n    super({\n      sourceColorArgb: sourceColorHct.toInt(),\n      variant: Variant.NEUTRAL,\n      contrastLevel,\n      isDark,\n      primaryPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 12.0),\n      secondaryPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 8.0),\n      tertiaryPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 16.0),\n      neutralPalette: TonalPalette.fromHueAndChroma(sourceColorHct.hue, 2.0),\n      neutralVariantPalette:\n          TonalPalette.fromHueAndChroma(sourceColorHct.hue, 2.0),\n    });\n  }\n}"]}