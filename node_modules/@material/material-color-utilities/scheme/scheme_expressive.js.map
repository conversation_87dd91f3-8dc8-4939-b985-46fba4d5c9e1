{"version": 3, "file": "scheme_expressive.js", "sourceRoot": "", "sources": ["scheme_expressive.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAGH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAE/C,OAAO,EAAC,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAErC;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,aAAa;IAiDjD,YAAY,cAAmB,EAAE,MAAe,EAAE,aAAqB;QACrE,KAAK,CAAC;YACJ,eAAe,EAAE,cAAc,CAAC,KAAK,EAAE;YACvC,OAAO,EAAE,OAAO,CAAC,UAAU;YAC3B,aAAa;YACb,MAAM;YACN,cAAc,EAAE,YAAY,CAAC,gBAAgB,CACzC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC;YACjE,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,CAC3C,aAAa,CAAC,aAAa,CACvB,cAAc,EAAE,gBAAgB,CAAC,IAAI,EACrC,gBAAgB,CAAC,kBAAkB,CAAC,EACxC,IAAI,CAAC;YACT,eAAe,EAAE,YAAY,CAAC,gBAAgB,CAC1C,aAAa,CAAC,aAAa,CACvB,cAAc,EAAE,gBAAgB,CAAC,IAAI,EACrC,gBAAgB,CAAC,iBAAiB,CAAC,EACvC,IAAI,CAAC;YACT,cAAc,EACV,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,GAAG,EAAE,EAAE,GAAG,CAAC;YAC/D,qBAAqB,EACjB,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,GAAG,EAAE,EAAE,IAAI,CAAC;SACjE,CAAC,CAAC;IACL,CAAC;;AAvED;;;GAGG;AACqB,qBAAI,GAAa;IACvC,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;CACN,CAAC;AAEF;;;GAGG;AACqB,mCAAkB,GAAa;IACrD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACL,CAAC;AAEF;;;GAGG;AACqB,kCAAiB,GAAa;IACpD,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;CACN,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Hct} from '../hct/hct.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\nimport * as math from '../utils/math_utils.js';\n\nimport {DynamicScheme} from './dynamic_scheme.js';\nimport {Variant} from './variant.js';\n\n/**\n * A Dynamic Color theme that is intentionally detached from the source color.\n */\nexport class SchemeExpressive extends DynamicScheme {\n  /**\n   * Hues (in degrees) used at breakpoints such that designers can specify a\n   * hue rotation that occurs at a given break point.\n   */\n  private static readonly hues: number[] = [\n    0.0,\n    21.0,\n    51.0,\n    121.0,\n    151.0,\n    191.0,\n    271.0,\n    321.0,\n    360.0,\n  ];\n\n  /**\n   * Hue rotations (in degrees) of the Secondary [TonalPalette],\n   * corresponding to the breakpoints in [hues].\n   */\n  private static readonly secondaryRotations: number[] = [\n    45.0,\n    95.0,\n    45.0,\n    20.0,\n    45.0,\n    90.0,\n    45.0,\n    45.0,\n    45.0,\n  ];\n\n  /**\n   * Hue rotations (in degrees) of the Tertiary [TonalPalette],\n   * corresponding to the breakpoints in [hues].\n   */\n  private static readonly tertiaryRotations: number[] = [\n    120.0,\n    120.0,\n    20.0,\n    45.0,\n    20.0,\n    15.0,\n    20.0,\n    120.0,\n    120.0,\n  ];\n\n  constructor(sourceColorHct: Hct, isDark: boolean, contrastLevel: number) {\n    super({\n      sourceColorArgb: sourceColorHct.toInt(),\n      variant: Variant.EXPRESSIVE,\n      contrastLevel,\n      isDark,\n      primaryPalette: TonalPalette.fromHueAndChroma(\n          math.sanitizeDegreesDouble(sourceColorHct.hue + 240.0), 40.0),\n      secondaryPalette: TonalPalette.fromHueAndChroma(\n          DynamicScheme.getRotatedHue(\n              sourceColorHct, SchemeExpressive.hues,\n              SchemeExpressive.secondaryRotations),\n          24.0),\n      tertiaryPalette: TonalPalette.fromHueAndChroma(\n          DynamicScheme.getRotatedHue(\n              sourceColorHct, SchemeExpressive.hues,\n              SchemeExpressive.tertiaryRotations),\n          32.0),\n      neutralPalette:\n          TonalPalette.fromHueAndChroma(sourceColorHct.hue + 15, 8.0),\n      neutralVariantPalette:\n          TonalPalette.fromHueAndChroma(sourceColorHct.hue + 15, 12.0),\n    });\n  }\n}\n"]}