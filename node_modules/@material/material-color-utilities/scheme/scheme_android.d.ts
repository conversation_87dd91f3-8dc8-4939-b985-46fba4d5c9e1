/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { CorePalette } from '../palettes/core_palette.js';
/**
 * Represents an Android 12 color scheme, a mapping of color roles to colors.
 */
export declare class SchemeAndroid {
    private readonly props;
    get colorAccentPrimary(): number;
    get colorAccentPrimaryVariant(): number;
    get colorAccentSecondary(): number;
    get colorAccentSecondaryVariant(): number;
    get colorAccentTertiary(): number;
    get colorAccentTertiaryVariant(): number;
    get textColorPrimary(): number;
    get textColorSecondary(): number;
    get textColorTertiary(): number;
    get textColorPrimaryInverse(): number;
    get textColorSecondaryInverse(): number;
    get textColorTertiaryInverse(): number;
    get colorBackground(): number;
    get colorBackgroundFloating(): number;
    get colorSurface(): number;
    get colorSurfaceVariant(): number;
    get colorSurfaceHighlight(): number;
    get surfaceHeader(): number;
    get underSurface(): number;
    get offState(): number;
    get accentSurface(): number;
    get textPrimaryOnAccent(): number;
    get textSecondaryOnAccent(): number;
    get volumeBackground(): number;
    get scrim(): number;
    /**
     * @param argb ARGB representation of a color.
     * @return Light Material color scheme, based on the color's hue.
     */
    static light(argb: number): SchemeAndroid;
    /**
     * @param argb ARGB representation of a color.
     * @return Dark Material color scheme, based on the color's hue.
     */
    static dark(argb: number): SchemeAndroid;
    /**
     * @param argb ARGB representation of a color.
     * @return Light Android color scheme, based on the color's hue.
     */
    static lightContent(argb: number): SchemeAndroid;
    /**
     * @param argb ARGB representation of a color.
     * @return Dark Android color scheme, based on the color's hue.
     */
    static darkContent(argb: number): SchemeAndroid;
    /**
     * Light scheme from core palette
     */
    static lightFromCorePalette(core: CorePalette): SchemeAndroid;
    /**
     * Dark scheme from core palette
     */
    static darkFromCorePalette(core: CorePalette): SchemeAndroid;
    private constructor();
    toJSON(): {
        colorAccentPrimary: number;
        colorAccentPrimaryVariant: number;
        colorAccentSecondary: number;
        colorAccentSecondaryVariant: number;
        colorAccentTertiary: number;
        colorAccentTertiaryVariant: number;
        textColorPrimary: number;
        textColorSecondary: number;
        textColorTertiary: number;
        textColorPrimaryInverse: number;
        textColorSecondaryInverse: number;
        textColorTertiaryInverse: number;
        colorBackground: number;
        colorBackgroundFloating: number;
        colorSurface: number;
        colorSurfaceVariant: number;
        colorSurfaceHighlight: number;
        surfaceHeader: number;
        underSurface: number;
        offState: number;
        accentSurface: number;
        textPrimaryOnAccent: number;
        textSecondaryOnAccent: number;
        volumeBackground: number;
        scrim: number;
    };
}
