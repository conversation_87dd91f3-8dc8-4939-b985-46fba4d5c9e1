{"version": 3, "file": "test_utils.js", "sourceRoot": "", "sources": ["test_utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,SAAS,CAAC;AAEjB,OAAO,EAAC,WAAW,EAAC,MAAM,mBAAmB,CAAC;AAU9C;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,MAAM,cAAc,GAAmC;IAC5D,YAAY,CACR,IAA0B,EAC1B,qBAA8D;QAChE,OAAO;YACL,OAAO,CAAC,MAAc,EAAE,QAAgB;gBACtC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC3C,OAAO;oBACL,IAAI;oBACJ,OAAO,EAAE,kBAAkB,WAAW,CAAC,MAAM,CAAC,OAC1C,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,WAAW,CAAC,QAAQ,CAAC,EAAE;iBACxD,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport 'jasmine';\n\nimport {hexFromArgb} from './string_utils.js';\n\ndeclare global {\n  namespace jasmine {\n    interface Matchers<T> {\n      matchesColor(expected: number): boolean;\n    }\n  }\n}\n\n/**\n * Exports a matcher called `matchesColor` that takes two numbers, and logs\n * the equivalent hex codes on failure.\n *\n * To use, add to your test file:\n *  beforeEach(() => {\n *    jasmine.addMatchers(customMatchers);\n *  });\n *\n * Then it can be used as a standard matcher:\n *  expect(scheme.onSurface).matchesColor(0xff000000);\n */\nexport const customMatchers: jasmine.CustomMatcherFactories = {\n  matchesColor(\n      util: jasmine.MatchersUtil,\n      customEqualityTesters: readonly jasmine.CustomEqualityTester[]) {\n    return {\n      compare(actual: number, expected: number) {\n        const pass = util.equals(actual, expected);\n        return {\n          pass,\n          message: `Expected color ${hexFromArgb(actual)} to ${\n              pass ? 'NOT' : ''} match: ${hexFromArgb(expected)}`,\n        };\n      },\n    };\n  },\n};\n"]}