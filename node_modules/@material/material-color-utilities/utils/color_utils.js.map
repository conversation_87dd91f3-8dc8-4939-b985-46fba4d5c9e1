{"version": 3, "file": "color_utils.js", "sourceRoot": "", "sources": ["color_utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,0DAA0D;AAE1D,OAAO,KAAK,SAAS,MAAM,iBAAiB,CAAC;AAE7C;;;;;GAKG;AAEH,MAAM,WAAW,GAAG;IAClB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;IACpC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACxB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;CACrC,CAAC;AAEF,MAAM,WAAW,GAAG;IAClB;QACE,kBAAkB;QAClB,CAAC,kBAAkB;QACnB,CAAC,mBAAmB;KACrB;IACD;QACE,CAAC,kBAAkB;QACnB,kBAAkB;QAClB,mBAAmB;KACpB;IACD;QACE,mBAAmB;QACnB,CAAC,mBAAmB;QACpB,kBAAkB;KACnB;CACF,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAEjD;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY;IAClE,OAAO,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;QACpE,CAAC,CAAC;AACR,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,MAAgB;IAC7C,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,IAAY;IACxC,OAAO,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,OAAO,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,IAAY;IACxC,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,IAAY;IACvC,OAAO,IAAI,GAAG,GAAG,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,IAAY;IACnC,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;IACzD,MAAM,MAAM,GAAG,WAAW,CAAC;IAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvE,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvE,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvE,MAAM,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAChC,MAAM,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAChC,MAAM,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAChC,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,MAAM,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACxC,MAAM,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,MAAM,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IACzC,OAAO,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AAC1D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;IACzD,MAAM,UAAU,GAAG,eAAe,CAAC;IACnC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;IAC9B,MAAM,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;IAC1B,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;IAC1B,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;IAChC,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;IAChC,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;IAChC,MAAM,CAAC,GAAG,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,CAAC,GAAG,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,CAAC,GAAG,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACtC,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9C,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAChD,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,MAAM,MAAM,GAAG,WAAW,CAAC;IAC3B,MAAM,CAAC,GACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAC7E,MAAM,CAAC,GACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAC7E,MAAM,CAAC,GACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAC7E,MAAM,UAAU,GAAG,eAAe,CAAC;IACnC,MAAM,WAAW,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,WAAW,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,WAAW,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IAC7B,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;IAC1B,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5B,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAAC,KAAa;IACzC,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAC5B,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAClC,OAAO,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACtD,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,IAAY;IACxC,MAAM,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,OAAO,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC;AACxC,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,UAAU,CAAC,KAAa;IACtC,OAAO,KAAK,GAAG,OAAO,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AACjD,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,UAAU,CAAC,CAAS;IAClC,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;AACxC,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,UAAU,CAAC,YAAoB;IAC7C,MAAM,UAAU,GAAG,YAAY,GAAG,KAAK,CAAC;IACxC,IAAI,UAAU,IAAI,WAAW,EAAE;QAC7B,OAAO,UAAU,GAAG,KAAK,GAAG,KAAK,CAAC;KACnC;SAAM;QACL,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;KAC5D;AACH,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,YAAY,CAAC,YAAoB;IAC/C,MAAM,UAAU,GAAG,YAAY,GAAG,KAAK,CAAC;IACxC,IAAI,YAAY,GAAG,GAAG,CAAC;IACvB,IAAI,UAAU,IAAI,SAAS,EAAE;QAC3B,YAAY,GAAG,UAAU,GAAG,KAAK,CAAC;KACnC;SAAM;QACL,YAAY,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;KAChE;IACD,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;AACtE,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,eAAe,CAAC;AACzB,CAAC;AAiBD;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAAC,IAAY;IACvC,MAAM,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAC5B,MAAM,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IAC9B,MAAM,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAC7B,MAAM,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;AACtB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAO;IAC7C,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IACjC,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IACjC,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IACjC,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IACjC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;AAClE,CAAC;AAED,SAAS,cAAc,CAAC,KAAa;IACnC,IAAI,KAAK,GAAG,CAAC;QAAE,OAAO,CAAC,CAAC;IACxB,IAAI,KAAK,GAAG,GAAG;QAAE,OAAO,GAAG,CAAC;IAC5B,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,IAAI,CAAC,CAAS;IACrB,MAAM,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;IAC1B,MAAM,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC;IAC7B,IAAI,CAAC,GAAG,CAAC,EAAE;QACT,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;KAC/B;SAAM;QACL,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;KAC/B;AACH,CAAC;AAED,SAAS,OAAO,CAAC,EAAU;IACzB,MAAM,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;IAC1B,MAAM,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC;IAC7B,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACzB,IAAI,GAAG,GAAG,CAAC,EAAE;QACX,OAAO,GAAG,CAAC;KACZ;SAAM;QACL,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;KAChC;AACH,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This file is automatically generated. Do not modify it.\n\nimport * as mathUtils from './math_utils.js';\n\n/**\n * Color science utilities.\n *\n * Utility methods for color science constants and color space\n * conversions that aren't HCT or CAM16.\n */\n\nconst SRGB_TO_XYZ = [\n  [0.41233895, 0.35762064, 0.18051042],\n  [0.2126, 0.7152, 0.0722],\n  [0.01932141, 0.11916382, 0.95034478],\n];\n\nconst XYZ_TO_SRGB = [\n  [\n    3.2413774792388685,\n    -1.5376652402851851,\n    -0.49885366846268053,\n  ],\n  [\n    -0.9691452513005321,\n    1.8758853451067872,\n    0.04156585616912061,\n  ],\n  [\n    0.05562093689691305,\n    -0.20395524564742123,\n    1.0571799111220335,\n  ],\n];\n\nconst WHITE_POINT_D65 = [95.047, 100.0, 108.883];\n\n/**\n * Converts a color from RGB components to ARGB format.\n */\nexport function argbFromRgb(red: number, green: number, blue: number): number {\n  return (255 << 24 | (red & 255) << 16 | (green & 255) << 8 | blue & 255) >>>\n      0;\n}\n\n/**\n * Converts a color from linear RGB components to ARGB format.\n */\nexport function argbFromLinrgb(linrgb: number[]): number {\n  const r = delinearized(linrgb[0]);\n  const g = delinearized(linrgb[1]);\n  const b = delinearized(linrgb[2]);\n  return argbFromRgb(r, g, b);\n}\n\n/**\n * Returns the alpha component of a color in ARGB format.\n */\nexport function alphaFromArgb(argb: number): number {\n  return argb >> 24 & 255;\n}\n\n/**\n * Returns the red component of a color in ARGB format.\n */\nexport function redFromArgb(argb: number): number {\n  return argb >> 16 & 255;\n}\n\n/**\n * Returns the green component of a color in ARGB format.\n */\nexport function greenFromArgb(argb: number): number {\n  return argb >> 8 & 255;\n}\n\n/**\n * Returns the blue component of a color in ARGB format.\n */\nexport function blueFromArgb(argb: number): number {\n  return argb & 255;\n}\n\n/**\n * Returns whether a color in ARGB format is opaque.\n */\nexport function isOpaque(argb: number): boolean {\n  return alphaFromArgb(argb) >= 255;\n}\n\n/**\n * Converts a color from ARGB to XYZ.\n */\nexport function argbFromXyz(x: number, y: number, z: number): number {\n  const matrix = XYZ_TO_SRGB;\n  const linearR = matrix[0][0] * x + matrix[0][1] * y + matrix[0][2] * z;\n  const linearG = matrix[1][0] * x + matrix[1][1] * y + matrix[1][2] * z;\n  const linearB = matrix[2][0] * x + matrix[2][1] * y + matrix[2][2] * z;\n  const r = delinearized(linearR);\n  const g = delinearized(linearG);\n  const b = delinearized(linearB);\n  return argbFromRgb(r, g, b);\n}\n\n/**\n * Converts a color from XYZ to ARGB.\n */\nexport function xyzFromArgb(argb: number): number[] {\n  const r = linearized(redFromArgb(argb));\n  const g = linearized(greenFromArgb(argb));\n  const b = linearized(blueFromArgb(argb));\n  return mathUtils.matrixMultiply([r, g, b], SRGB_TO_XYZ);\n}\n\n/**\n * Converts a color represented in Lab color space into an ARGB\n * integer.\n */\nexport function argbFromLab(l: number, a: number, b: number): number {\n  const whitePoint = WHITE_POINT_D65;\n  const fy = (l + 16.0) / 116.0;\n  const fx = a / 500.0 + fy;\n  const fz = fy - b / 200.0;\n  const xNormalized = labInvf(fx);\n  const yNormalized = labInvf(fy);\n  const zNormalized = labInvf(fz);\n  const x = xNormalized * whitePoint[0];\n  const y = yNormalized * whitePoint[1];\n  const z = zNormalized * whitePoint[2];\n  return argbFromXyz(x, y, z);\n}\n\n/**\n * Converts a color from ARGB representation to L*a*b*\n * representation.\n *\n * @param argb the ARGB representation of a color\n * @return a Lab object representing the color\n */\nexport function labFromArgb(argb: number): number[] {\n  const linearR = linearized(redFromArgb(argb));\n  const linearG = linearized(greenFromArgb(argb));\n  const linearB = linearized(blueFromArgb(argb));\n  const matrix = SRGB_TO_XYZ;\n  const x =\n      matrix[0][0] * linearR + matrix[0][1] * linearG + matrix[0][2] * linearB;\n  const y =\n      matrix[1][0] * linearR + matrix[1][1] * linearG + matrix[1][2] * linearB;\n  const z =\n      matrix[2][0] * linearR + matrix[2][1] * linearG + matrix[2][2] * linearB;\n  const whitePoint = WHITE_POINT_D65;\n  const xNormalized = x / whitePoint[0];\n  const yNormalized = y / whitePoint[1];\n  const zNormalized = z / whitePoint[2];\n  const fx = labF(xNormalized);\n  const fy = labF(yNormalized);\n  const fz = labF(zNormalized);\n  const l = 116.0 * fy - 16;\n  const a = 500.0 * (fx - fy);\n  const b = 200.0 * (fy - fz);\n  return [l, a, b];\n}\n\n/**\n * Converts an L* value to an ARGB representation.\n *\n * @param lstar L* in L*a*b*\n * @return ARGB representation of grayscale color with lightness\n * matching L*\n */\nexport function argbFromLstar(lstar: number): number {\n  const y = yFromLstar(lstar);\n  const component = delinearized(y);\n  return argbFromRgb(component, component, component);\n}\n\n/**\n * Computes the L* value of a color in ARGB representation.\n *\n * @param argb ARGB representation of a color\n * @return L*, from L*a*b*, coordinate of the color\n */\nexport function lstarFromArgb(argb: number): number {\n  const y = xyzFromArgb(argb)[1];\n  return 116.0 * labF(y / 100.0) - 16.0;\n}\n\n/**\n * Converts an L* value to a Y value.\n *\n * L* in L*a*b* and Y in XYZ measure the same quantity, luminance.\n *\n * L* measures perceptual luminance, a linear scale. Y in XYZ\n * measures relative luminance, a logarithmic scale.\n *\n * @param lstar L* in L*a*b*\n * @return Y in XYZ\n */\nexport function yFromLstar(lstar: number): number {\n  return 100.0 * labInvf((lstar + 16.0) / 116.0);\n}\n\n/**\n * Converts a Y value to an L* value.\n *\n * L* in L*a*b* and Y in XYZ measure the same quantity, luminance.\n *\n * L* measures perceptual luminance, a linear scale. Y in XYZ\n * measures relative luminance, a logarithmic scale.\n *\n * @param y Y in XYZ\n * @return L* in L*a*b*\n */\nexport function lstarFromY(y: number): number {\n  return labF(y / 100.0) * 116.0 - 16.0;\n}\n\n/**\n * Linearizes an RGB component.\n *\n * @param rgbComponent 0 <= rgb_component <= 255, represents R/G/B\n * channel\n * @return 0.0 <= output <= 100.0, color channel converted to\n * linear RGB space\n */\nexport function linearized(rgbComponent: number): number {\n  const normalized = rgbComponent / 255.0;\n  if (normalized <= 0.040449936) {\n    return normalized / 12.92 * 100.0;\n  } else {\n    return Math.pow((normalized + 0.055) / 1.055, 2.4) * 100.0;\n  }\n}\n\n/**\n * Delinearizes an RGB component.\n *\n * @param rgbComponent 0.0 <= rgb_component <= 100.0, represents\n * linear R/G/B channel\n * @return 0 <= output <= 255, color channel converted to regular\n * RGB space\n */\nexport function delinearized(rgbComponent: number): number {\n  const normalized = rgbComponent / 100.0;\n  let delinearized = 0.0;\n  if (normalized <= 0.0031308) {\n    delinearized = normalized * 12.92;\n  } else {\n    delinearized = 1.055 * Math.pow(normalized, 1.0 / 2.4) - 0.055;\n  }\n  return mathUtils.clampInt(0, 255, Math.round(delinearized * 255.0));\n}\n\n/**\n * Returns the standard white point; white on a sunny day.\n *\n * @return The white point\n */\nexport function whitePointD65(): number[] {\n  return WHITE_POINT_D65;\n}\n\n/**\n * RGBA component\n * \n * @param r Red value should be between 0-255\n * @param g Green value should be between 0-255\n * @param b Blue value should be between 0-255\n * @param a Alpha value should be between 0-255\n */\nexport interface Rgba {\n  r: number;\n  g: number;\n  b: number;\n  a: number;\n}\n\n/**\n * Return RGBA from a given int32 color\n *\n * @param argb ARGB representation of a int32 color.\n * @return RGBA representation of a int32 color.\n */\nexport function rgbaFromArgb(argb: number): Rgba {\n  const r = redFromArgb(argb);\n  const g = greenFromArgb(argb);\n  const b = blueFromArgb(argb);\n  const a = alphaFromArgb(argb);\n  return {r, g, b, a};\n}\n\n/**\n * Return int32 color from a given RGBA component\n * \n * @param rgba RGBA representation of a int32 color.\n * @returns ARGB representation of a int32 color.\n */\nexport function argbFromRgba({r, g, b, a}: Rgba): number {\n  const rValue = clampComponent(r);\n  const gValue = clampComponent(g);\n  const bValue = clampComponent(b);\n  const aValue = clampComponent(a);\n  return (aValue << 24) | (rValue << 16) | (gValue << 8) | bValue;\n}\n\nfunction clampComponent(value: number) {\n  if (value < 0) return 0;\n  if (value > 255) return 255;\n  return value\n}\n\nfunction labF(t: number): number {\n  const e = 216.0 / 24389.0;\n  const kappa = 24389.0 / 27.0;\n  if (t > e) {\n    return Math.pow(t, 1.0 / 3.0);\n  } else {\n    return (kappa * t + 16) / 116;\n  }\n}\n\nfunction labInvf(ft: number): number {\n  const e = 216.0 / 24389.0;\n  const kappa = 24389.0 / 27.0;\n  const ft3 = ft * ft * ft;\n  if (ft3 > e) {\n    return ft3;\n  } else {\n    return (116 * ft - 16) / kappa;\n  }\n}\n"]}