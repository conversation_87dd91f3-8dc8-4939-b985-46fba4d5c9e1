{"version": 3, "file": "theme_utils.js", "sourceRoot": "", "sources": ["theme_utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,KAAK,EAAC,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAC,WAAW,EAAC,MAAM,6BAA6B,CAAC;AAExD,OAAO,EAAC,MAAM,EAAC,MAAM,qBAAqB,CAAC;AAE3C,OAAO,EAAC,oBAAoB,EAAC,MAAM,kBAAkB,CAAC;AACtD,OAAO,EAAC,WAAW,EAAC,MAAM,mBAAmB,CAAC;AA8C9C;;;;;;GAMG;AACH,MAAM,UAAU,oBAAoB,CAChC,MAAc,EAAE,eAA8B,EAAE;IAClD,MAAM,OAAO,GAAG,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IACvC,OAAO;QACL,MAAM;QACN,OAAO,EAAE;YACP,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;SAC1B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,OAAO,CAAC,EAAE;YACnB,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,QAAQ,EAAE,OAAO,CAAC,EAAE;YACpB,OAAO,EAAE,OAAO,CAAC,EAAE;YACnB,cAAc,EAAE,OAAO,CAAC,EAAE;YAC1B,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB;QACD,YAAY,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KAC9D,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAChC,KAAuB,EAAE,eAA8B,EAAE;IAC3D,MAAM,MAAM,GAAG,MAAM,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACjD,OAAO,oBAAoB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AACpD,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,WAAW,CACvB,MAAc,EAAE,KAAkB;IACpC,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IACxB,MAAM,IAAI,GAAG,KAAK,CAAC;IACnB,MAAM,EAAE,GAAG,MAAM,CAAC;IAClB,IAAI,KAAK,CAAC,KAAK,EAAE;QACf,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACnC;IACD,MAAM,OAAO,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IACtC,MAAM,KAAK,GAAG,OAAO,CAAC,EAAE,CAAC;IACzB,OAAO;QACL,KAAK;QACL,KAAK;QACL,KAAK,EAAE;YACL,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;YACxB,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;SACjC;QACD,IAAI,EAAE;YACJ,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;SACjC;KACF,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,UAAU,CAAC,KAAY,EAAE,OAKxC;IACC,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC;IAChD,MAAM,MAAM,GAAG,OAAO,EAAE,IAAI,IAAI,KAAK,CAAC;IACtC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;IACjE,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACpC,IAAI,OAAO,EAAE,gBAAgB,EAAE;QAC7B,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACzD,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;KAC5D;IACD,IAAI,OAAO,EAAE,YAAY,EAAE;QACzB,MAAM,KAAK,GAAG,OAAO,EAAE,YAAY,IAAI,EAAE,CAAC;QAC1C,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YAC3D,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACzE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,MAAM,KAAK,GAAG,oBAAoB,UAAU,IAAI,UAAU,GAAG,IAAI,EAAE,CAAC;gBACpE,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACxC;SACF;KACF;AACH,CAAC;AAED,SAAS,mBAAmB,CACxB,MAAmB,EACnB,MAAc,EACd,SAAiB,EAAE;IAErB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE;QAC1D,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACpE,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,kBAAkB,KAAK,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;KACrE;AACH,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {Blend} from '../blend/blend.js';\nimport {CorePalette} from '../palettes/core_palette.js';\nimport {TonalPalette} from '../palettes/tonal_palette.js';\nimport {Scheme} from '../scheme/scheme.js';\n\nimport {sourceColorFromImage} from './image_utils.js';\nimport {hexFromArgb} from './string_utils.js';\n\n/**\n * Custom color used to pair with a theme\n */\nexport interface CustomColor {\n  value: number;\n  name: string;\n  blend: boolean;\n}\n\n/**\n * Color group\n */\nexport interface ColorGroup {\n  color: number;\n  onColor: number;\n  colorContainer: number;\n  onColorContainer: number;\n}\n\n/**\n * Custom Color Group\n */\nexport interface CustomColorGroup {\n  color: CustomColor;\n  value: number;\n  light: ColorGroup;\n  dark: ColorGroup;\n}\n\n/**\n * Theme\n */\nexport interface Theme {\n  source: number;\n  schemes: {light: Scheme; dark: Scheme;};\n  palettes: {\n    primary: TonalPalette; secondary: TonalPalette; tertiary: TonalPalette;\n    neutral: TonalPalette;\n    neutralVariant: TonalPalette;\n    error: TonalPalette;\n  };\n  customColors: CustomColorGroup[];\n}\n\n/**\n * Generate a theme from a source color\n *\n * @param source Source color\n * @param customColors Array of custom colors\n * @return Theme object\n */\nexport function themeFromSourceColor(\n    source: number, customColors: CustomColor[] = []): Theme {\n  const palette = CorePalette.of(source);\n  return {\n    source,\n    schemes: {\n      light: Scheme.light(source),\n      dark: Scheme.dark(source),\n    },\n    palettes: {\n      primary: palette.a1,\n      secondary: palette.a2,\n      tertiary: palette.a3,\n      neutral: palette.n1,\n      neutralVariant: palette.n2,\n      error: palette.error,\n    },\n    customColors: customColors.map((c) => customColor(source, c)),\n  };\n}\n\n/**\n * Generate a theme from an image source\n *\n * @param image Image element\n * @param customColors Array of custom colors\n * @return Theme object\n */\nexport async function themeFromImage(\n    image: HTMLImageElement, customColors: CustomColor[] = []) {\n  const source = await sourceColorFromImage(image);\n  return themeFromSourceColor(source, customColors);\n}\n\n/**\n * Generate custom color group from source and target color\n *\n * @param source Source color\n * @param color Custom color\n * @return Custom color group\n *\n * @link https://m3.material.io/styles/color/the-color-system/color-roles\n */\nexport function customColor(\n    source: number, color: CustomColor): CustomColorGroup {\n  let value = color.value;\n  const from = value;\n  const to = source;\n  if (color.blend) {\n    value = Blend.harmonize(from, to);\n  }\n  const palette = CorePalette.of(value);\n  const tones = palette.a1;\n  return {\n    color,\n    value,\n    light: {\n      color: tones.tone(40),\n      onColor: tones.tone(100),\n      colorContainer: tones.tone(90),\n      onColorContainer: tones.tone(10),\n    },\n    dark: {\n      color: tones.tone(80),\n      onColor: tones.tone(20),\n      colorContainer: tones.tone(30),\n      onColorContainer: tones.tone(90),\n    },\n  };\n}\n\n/**\n * Apply a theme to an element\n *\n * @param theme Theme object\n * @param options Options\n */\nexport function applyTheme(theme: Theme, options?: {\n  dark?: boolean,\n  target?: HTMLElement,\n  brightnessSuffix?: boolean,\n  paletteTones?: number[],\n}) {\n  const target = options?.target || document.body;\n  const isDark = options?.dark ?? false;\n  const scheme = isDark ? theme.schemes.dark : theme.schemes.light;\n  setSchemeProperties(target, scheme);\n  if (options?.brightnessSuffix) {\n    setSchemeProperties(target, theme.schemes.dark, '-dark');\n    setSchemeProperties(target, theme.schemes.light, '-light');\n  }\n  if (options?.paletteTones) {\n    const tones = options?.paletteTones ?? [];\n    for (const [key, palette] of Object.entries(theme.palettes)) {\n      const paletteKey = key.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n      for (const tone of tones) {\n        const token = `--md-ref-palette-${paletteKey}-${paletteKey}${tone}`;\n        const color = hexFromArgb(palette.tone(tone));\n        target.style.setProperty(token, color);\n      }\n    }\n  }\n}\n\nfunction setSchemeProperties(\n    target: HTMLElement,\n    scheme: Scheme,\n    suffix: string = '',\n) {\n  for (const [key, value] of Object.entries(scheme.toJSON())) {\n    const token = key.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n    const color = hexFromArgb(value);\n    target.style.setProperty(`--md-sys-color-${token}${suffix}`, color);\n  }\n}"]}