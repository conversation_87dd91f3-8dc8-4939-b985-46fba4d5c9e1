{"version": 3, "file": "image_utils.js", "sourceRoot": "", "sources": ["image_utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,eAAe,EAAC,MAAM,iCAAiC,CAAC;AAChE,OAAO,EAAC,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAExC,OAAO,EAAC,WAAW,EAAC,MAAM,kBAAkB,CAAC;AAE7C;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAAC,KAAuB;IAChE,oCAAoC;IACpC,MAAM,UAAU,GAAG,MAAM,IAAI,OAAO,CAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,CAAC,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;YAClD,OAAO;SACR;QACD,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC3B,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC7B,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/B,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,IAAI,IAAI,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC7C,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACnC,+BAA+B;oBAC/B,OAAO,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC;aACJ;YACD,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;YAC9B,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC;QACF,IAAI,KAAK,CAAC,QAAQ,EAAE;YAClB,QAAQ,EAAE,CAAC;SACZ;aAAM;YACL,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;SACzB;IACH,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,EAAE;YACX,SAAS;SACV;QACD,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;IAED,oCAAoC;IACpC,MAAM,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACrD,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACnC,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACtB,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {QuantizerCelebi} from '../quantize/quantizer_celebi.js';\nimport {Score} from '../score/score.js';\n\nimport {argbFromRgb} from './color_utils.js';\n\n/**\n * Get the source color from an image.\n *\n * @param image The image element\n * @return Source color - the color most suitable for creating a UI theme\n */\nexport async function sourceColorFromImage(image: HTMLImageElement) {\n  // Convert Image data to Pixel Array\n  const imageBytes = await new Promise<Uint8ClampedArray>((resolve, reject) => {\n    const canvas = document.createElement('canvas');\n    const context = canvas.getContext('2d');\n    if (!context) {\n      reject(new Error('Could not get canvas context'));\n      return;\n    }\n    const callback = () => {\n      canvas.width = image.width;\n      canvas.height = image.height;\n      context.drawImage(image, 0, 0);\n      let rect = [0, 0, image.width, image.height];\n      const area = image.dataset['area'];\n      if (area && /^\\d+(\\s*,\\s*\\d+){3}$/.test(area)) {\n        rect = area.split(/\\s*,\\s*/).map(s => {\n          // tslint:disable-next-line:ban\n          return parseInt(s, 10);\n        });\n      }\n      const [sx, sy, sw, sh] = rect;\n      resolve(context.getImageData(sx, sy, sw, sh).data);\n    };\n    if (image.complete) {\n      callback();\n    } else {\n      image.onload = callback;\n    }\n  });\n\n  // Convert Image data to Pixel Array\n  const pixels: number[] = [];\n  for (let i = 0; i < imageBytes.length; i += 4) {\n    const r = imageBytes[i];\n    const g = imageBytes[i + 1];\n    const b = imageBytes[i + 2];\n    const a = imageBytes[i + 3];\n    if (a < 255) {\n      continue;\n    }\n    const argb = argbFromRgb(r, g, b);\n    pixels.push(argb);\n  }\n\n  // Convert Pixels to Material Colors\n  const result = QuantizerCelebi.quantize(pixels, 128);\n  const ranked = Score.score(result);\n  const top = ranked[0];\n  return top;\n}\n"]}