{"version": 3, "file": "string_utils.js", "sourceRoot": "", "sources": ["string_utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAK,UAAU,MAAM,kBAAkB,CAAC;AAE/C;;GAEG;AAEH;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,MAAM,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvC,MAAM,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACzC,MAAM,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACxC,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAElE,iCAAiC;IACjC,KAAK,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE;QAC1C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;SAC1B;KACF;IAED,OAAO,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAAC,GAAW;IACrC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IACjC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IAC/B,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC;KAC1C;IACD,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,OAAO,EAAE;QACX,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5C;SAAM,IAAI,KAAK,EAAE;QAChB,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAClC;SAAM,IAAI,OAAO,EAAE;QAClB,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAClC;IAED,OAAO,CACH,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;AACT,CAAC;AAED,SAAS,WAAW,CAAC,KAAa;IAChC,+BAA+B;IAC/B,OAAO,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC7B,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as colorUtils from './color_utils.js';\n\n/**\n * Utility methods for hexadecimal representations of colors.\n */\n\n/**\n * @param argb ARGB representation of a color.\n * @return Hex string representing color, ex. #ff0000 for red.\n */\nexport function hexFromArgb(argb: number) {\n  const r = colorUtils.redFromArgb(argb);\n  const g = colorUtils.greenFromArgb(argb);\n  const b = colorUtils.blueFromArgb(argb);\n  const outParts = [r.toString(16), g.toString(16), b.toString(16)];\n\n  // Pad single-digit output values\n  for (const [i, part] of outParts.entries()) {\n    if (part.length === 1) {\n      outParts[i] = '0' + part;\n    }\n  }\n\n  return '#' + outParts.join('');\n}\n\n/**\n * @param hex String representing color as hex code. Accepts strings with or\n *     without leading #, and string representing the color using 3, 6, or 8\n *     hex characters.\n * @return ARGB representation of color.\n */\nexport function argbFromHex(hex: string) {\n  hex = hex.replace('#', '');\n  const isThree = hex.length === 3;\n  const isSix = hex.length === 6;\n  const isEight = hex.length === 8;\n  if (!isThree && !isSix && !isEight) {\n    throw new Error('unexpected hex ' + hex);\n  }\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  if (isThree) {\n    r = parseIntHex(hex.slice(0, 1).repeat(2));\n    g = parseIntHex(hex.slice(1, 2).repeat(2));\n    b = parseIntHex(hex.slice(2, 3).repeat(2));\n  } else if (isSix) {\n    r = parseIntHex(hex.slice(0, 2));\n    g = parseIntHex(hex.slice(2, 4));\n    b = parseIntHex(hex.slice(4, 6));\n  } else if (isEight) {\n    r = parseIntHex(hex.slice(2, 4));\n    g = parseIntHex(hex.slice(4, 6));\n    b = parseIntHex(hex.slice(6, 8));\n  }\n\n  return (\n      ((255 << 24) | ((r & 0x0ff) << 16) | ((g & 0x0ff) << 8) | (b & 0x0ff)) >>>\n      0);\n}\n\nfunction parseIntHex(value: string) {\n  // tslint:disable-next-line:ban\n  return parseInt(value, 16);\n}\n"]}