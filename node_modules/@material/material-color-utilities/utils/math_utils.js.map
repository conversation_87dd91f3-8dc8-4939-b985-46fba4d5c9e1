{"version": 3, "file": "math_utils.js", "sourceRoot": "", "sources": ["math_utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,0DAA0D;AAE1D;;GAEG;AAEH;;;;GAIG;AACH,MAAM,UAAU,MAAM,CAAC,GAAW;IAChC,IAAI,GAAG,GAAG,CAAC,EAAE;QACX,OAAO,CAAC,CAAC,CAAC;KACX;SAAM,IAAI,GAAG,KAAK,CAAC,EAAE;QACpB,OAAO,CAAC,CAAC;KACV;SAAM;QACL,OAAO,CAAC,CAAC;KACV;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,IAAI,CAAC,KAAa,EAAE,IAAY,EAAE,MAAc;IAC9D,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC;AAChD,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,QAAQ,CAAC,GAAW,EAAE,GAAW,EAAE,KAAa;IAC9D,IAAI,KAAK,GAAG,GAAG,EAAE;QACf,OAAO,GAAG,CAAC;KACZ;SAAM,IAAI,KAAK,GAAG,GAAG,EAAE;QACtB,OAAO,GAAG,CAAC;KACZ;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAAC,GAAW,EAAE,GAAW,EAAE,KAAa;IACjE,IAAI,KAAK,GAAG,GAAG,EAAE;QACf,OAAO,GAAG,CAAC;KACZ;SAAM,IAAI,KAAK,GAAG,GAAG,EAAE;QACtB,OAAO,GAAG,CAAC;KACZ;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAAe;IAChD,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC;IACxB,IAAI,OAAO,GAAG,CAAC,EAAE;QACf,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC;KACzB;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAAe;IACnD,OAAO,GAAG,OAAO,GAAG,KAAK,CAAC;IAC1B,IAAI,OAAO,GAAG,CAAC,EAAE;QACf,OAAO,GAAG,OAAO,GAAG,KAAK,CAAC;KAC3B;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAY,EAAE,EAAU;IACxD,MAAM,oBAAoB,GAAG,qBAAqB,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9D,OAAO,oBAAoB,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,CAAS,EAAE,CAAS;IACpD,OAAO,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACnD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,GAAa,EAAE,MAAkB;IAC9D,MAAM,CAAC,GACH,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,MAAM,CAAC,GACH,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,MAAM,CAAC,GACH,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This file is automatically generated. Do not modify it.\n\n/**\n * Utility methods for mathematical operations.\n */\n\n/**\n * The signum function.\n *\n * @return 1 if num > 0, -1 if num < 0, and 0 if num = 0\n */\nexport function signum(num: number): number {\n  if (num < 0) {\n    return -1;\n  } else if (num === 0) {\n    return 0;\n  } else {\n    return 1;\n  }\n}\n\n/**\n * The linear interpolation function.\n *\n * @return start if amount = 0 and stop if amount = 1\n */\nexport function lerp(start: number, stop: number, amount: number): number {\n  return (1.0 - amount) * start + amount * stop;\n}\n\n/**\n * Clamps an integer between two integers.\n *\n * @return input when min <= input <= max, and either min or max\n * otherwise.\n */\nexport function clampInt(min: number, max: number, input: number): number {\n  if (input < min) {\n    return min;\n  } else if (input > max) {\n    return max;\n  }\n\n  return input;\n}\n\n/**\n * Clamps an integer between two floating-point numbers.\n *\n * @return input when min <= input <= max, and either min or max\n * otherwise.\n */\nexport function clampDouble(min: number, max: number, input: number): number {\n  if (input < min) {\n    return min;\n  } else if (input > max) {\n    return max;\n  }\n\n  return input;\n}\n\n/**\n * Sanitizes a degree measure as an integer.\n *\n * @return a degree measure between 0 (inclusive) and 360\n * (exclusive).\n */\nexport function sanitizeDegreesInt(degrees: number): number {\n  degrees = degrees % 360;\n  if (degrees < 0) {\n    degrees = degrees + 360;\n  }\n  return degrees;\n}\n\n/**\n * Sanitizes a degree measure as a floating-point number.\n *\n * @return a degree measure between 0.0 (inclusive) and 360.0\n * (exclusive).\n */\nexport function sanitizeDegreesDouble(degrees: number): number {\n  degrees = degrees % 360.0;\n  if (degrees < 0) {\n    degrees = degrees + 360.0;\n  }\n  return degrees;\n}\n\n/**\n * Sign of direction change needed to travel from one angle to\n * another.\n *\n * For angles that are 180 degrees apart from each other, both\n * directions have the same travel distance, so either direction is\n * shortest. The value 1.0 is returned in this case.\n *\n * @param from The angle travel starts from, in degrees.\n * @param to The angle travel ends at, in degrees.\n * @return -1 if decreasing from leads to the shortest travel\n * distance, 1 if increasing from leads to the shortest travel\n * distance.\n */\nexport function rotationDirection(from: number, to: number): number {\n  const increasingDifference = sanitizeDegreesDouble(to - from);\n  return increasingDifference <= 180.0 ? 1.0 : -1.0;\n}\n\n/**\n * Distance of two points on a circle, represented using degrees.\n */\nexport function differenceDegrees(a: number, b: number): number {\n  return 180.0 - Math.abs(Math.abs(a - b) - 180.0);\n}\n\n/**\n * Multiplies a 1x3 row vector with a 3x3 matrix.\n */\nexport function matrixMultiply(row: number[], matrix: number[][]): number[] {\n  const a =\n      row[0] * matrix[0][0] + row[1] * matrix[0][1] + row[2] * matrix[0][2];\n  const b =\n      row[0] * matrix[1][0] + row[1] * matrix[1][1] + row[2] * matrix[1][2];\n  const c =\n      row[0] * matrix[2][0] + row[1] * matrix[2][1] + row[2] * matrix[2][2];\n  return [a, b, c];\n}\n"]}