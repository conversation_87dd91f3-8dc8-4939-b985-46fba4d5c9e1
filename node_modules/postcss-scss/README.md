# PostCSS SCSS Syntax

<img align="right" width="95" height="95"
     title="Philoso<PERSON>’s stone, logo of PostCSS"
     src="http://postcss.github.io/postcss/logo.svg">

A [SCSS] parser for [PostCSS].

**This module does not compile SCSS.** It simply parses mixins as custom
at-rules & variables as properties, so that PostCSS plugins can then transform
SCSS source code alongside CSS.

[PostCSS]: https://github.com/postcss/postcss
[SCSS]:    http://sass-lang.com/

<a href="https://evilmartians.com/?utm_source=postcss">
  <img src="https://evilmartians.com/badges/sponsored-by-evil-martians.svg"
    alt="Sponsored by Evil Martians" width="236" height="54">
</a>


## Docs
Read full docs **[here](https://github.com/postcss/postcss-scss#readme)**.
